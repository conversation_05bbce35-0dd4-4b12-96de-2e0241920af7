(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5687,2912],{18535:function(e,t,n){"use strict";n.d(t,{eI:function(){return eg}});class r extends Error{constructor(e){super(e),this.__dgError=!0,this.name="DeepgramError"}}function o(e){return"object"==typeof e&&null!==e&&"__dgError"in e}class i extends r{constructor(e,t){super(e),this.name="DeepgramApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class s extends r{constructor(e,t){super(e),this.name="DeepgramUnknownError",this.originalError=t}}class a extends r{constructor(){super("You are attempting to use an old format for a newer SDK version. Read more here: https://dpgr.am/js-v3"),this.name="DeepgramVersionError"}}var u,l,c,d,h,p,f,_,m=n(68885),v=n(29972),y=n.n(v),g=n(9535),b=n.n(g);let w=()=>"unknown"!==C,A=()=>"unknown"!==I;function S(e={},t={}){return b()(t,e)}let T=()=>"undefined"==typeof Headers?v.Headers:Headers,O=e=>!!e.url,x=e=>!!e.text,j=e=>!!(P(e)||k(e)),k=e=>!!e,P=e=>!!e,R=e=>{var t,n,r,o,i,s;let a={};return e._experimentalCustomFetch&&(a.global={fetch:{client:e._experimentalCustomFetch}}),(null===(t=(e=b()(e,a)).restProxy)||void 0===t?void 0:t.url)&&(a.global={fetch:{options:{proxy:{url:null===(n=e.restProxy)||void 0===n?void 0:n.url}}}}),(null===(r=(e=b()(e,a)).global)||void 0===r?void 0:r.url)&&(a.global={fetch:{options:{url:e.global.url}},websocket:{options:{url:e.global.url}}}),(null===(o=(e=b()(e,a)).global)||void 0===o?void 0:o.headers)&&(a.global={fetch:{options:{headers:null===(i=e.global)||void 0===i?void 0:i.headers}},websocket:{options:{_nodeOnlyHeaders:null===(s=e.global)||void 0===s?void 0:s.headers}}}),e=b()(e,a)},F="3.11.2";var E=n(20357);let M=void 0!==E&&E.versions&&E.versions.node?E.versions.node:"unknown",I=void 0!==E&&E.versions&&E.versions.bun?E.versions.bun:"unknown",C="undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"unknown",U={"Content-Type":"application/json","X-Client-Info":`@deepgram/sdk; ${w()?"browser":"server"}; v${F}`,"User-Agent":`@deepgram/sdk/${F} ${"unknown"!==M?`node/${M}`:A()?`bun/${I}`:w()?`javascript ${C}`:"unknown"}`},B="https://api.deepgram.com",D="wss://agent.deepgram.com",L={global:{fetch:{options:{url:B,headers:U}},websocket:{options:{url:B.toLowerCase().replace(/^http/,"ws"),_nodeOnlyHeaders:U}}},agent:{fetch:{options:{url:B,headers:U}},websocket:{options:{url:D,_nodeOnlyHeaders:U}}}};(u=h||(h={}))[u.connecting=0]="connecting",u[u.open=1]="open",u[u.closing=2]="closing",u[u.closed=3]="closed",(l=p||(p={})).Connecting="connecting",l.Open="open",l.Closing="closing",l.Closed="closed";var N=n(20357);let q=()=>{};class V extends m.EventEmitter{constructor(e){let t;if(super(),this.factory=void 0,this.namespace="global",this.version="v1",this.baseUrl=B,this.logger=q,"function"==typeof e.key?(this.factory=e.key,t=this.factory()):t=e.key,t||(t=N.env.DEEPGRAM_API_KEY),!t)throw new r("A deepgram API key is required.");this.key=t,e=R(e),this.options=S(e,L)}v(e="v1"){return this.version=e,this}get namespaceOptions(){return Object.assign(Object.assign({},S(this.options[this.namespace],this.options.global)),{key:this.key})}getRequestUrl(e,t={version:this.version},n){t.version=this.version;let r=new URL(e=e.replace(/:(\w+)/g,function(e,n){return t[n]}),this.baseUrl);return n&&function(e,t){Object.keys(t).forEach(n=>{Array.isArray(t[n])?t[n].forEach(t=>{e.append(n,String(t))}):e.append(n,String(t[n]))})}(r.searchParams,n),r}log(e,t,n){this.logger(e,t,n)}}let H="undefined"!=typeof WebSocket;class z extends V{constructor(e){super(e),this.conn=null,this.sendBuffer=[],this.reconnect=q;let{key:t,websocket:{options:n,client:r}}=this.namespaceOptions;this.proxy?this.baseUrl=n.proxy.url:this.baseUrl=n.url,r?this.transport=r:this.transport=null,n._nodeOnlyHeaders?this.headers=n._nodeOnlyHeaders:this.headers={},"Authorization"in this.headers||(this.headers.Authorization=`Token ${t}`)}connect(e,t){if(this.conn)return;this.reconnect=(n=e)=>{this.connect(n,t)};let r=this.getRequestUrl(t,{},e);if(this.transport){this.conn=new this.transport(r,void 0,{headers:this.headers});return}if(A()){n.e(7035).then(n.t.bind(n,7035,23)).then(({default:e})=>{this.conn=new e(r,{headers:this.headers}),console.log("Using WS package"),this.setupConnection()});return}if(H){this.conn=new WebSocket(r,["token",this.namespaceOptions.key]),this.setupConnection();return}this.conn=new G(r,void 0,{close:()=>{this.conn=null}}),n.e(7035).then(n.t.bind(n,7035,23)).then(({default:e})=>{this.conn=new e(r,void 0,{headers:this.headers}),this.setupConnection()})}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null)}connectionState(){switch(this.conn&&this.conn.readyState){case h.connecting:return p.Connecting;case h.open:return p.Open;case h.closing:return p.Closing;default:return p.Closed}}getReadyState(){var e,t;return null!==(t=null===(e=this.conn)||void 0===e?void 0:e.readyState)&&void 0!==t?t:h.closed}isConnected(){return this.connectionState()===p.Open}send(e){let t=()=>{var t,n,r,o;return t=this,n=void 0,r=void 0,o=function*(){var t;if(e instanceof Blob){if(0===e.size){this.log("warn","skipping `send` for zero-byte blob",e);return}e=yield e.arrayBuffer()}if("string"!=typeof e&&!(null==e?void 0:e.byteLength)){this.log("warn","skipping `send` for zero-byte payload",e);return}null===(t=this.conn)||void 0===t||t.send(e)},new(r||(r=Promise))(function(e,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(t){var n;t.done?e(t.value):((n=t.value)instanceof r?n:new r(function(e){e(n)})).then(s,a)}u((o=o.apply(t,n||[])).next())})};this.isConnected()?t():this.sendBuffer.push(t)}get proxy(){var e;return"proxy"===this.key&&!!(null===(e=this.namespaceOptions.websocket.options.proxy)||void 0===e?void 0:e.url)}}class G{constructor(e,t,n){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=h.connecting,this.send=()=>{},this.url=null,this.url=e.toString(),this.close=n.close}}var $=n(61023);class J extends z{constructor(e,t={},n=":version/listen"){var o,i;if(super(e),this.namespace="listen",(null===(o=t.keyterm)||void 0===o?void 0:o.length)&&!(null===(i=t.model)||void 0===i?void 0:i.startsWith("nova-3")))throw new r("Keyterms are only supported with the Nova 3 models.");this.connect(t,n)}setupConnection(){this.conn&&(this.conn.onopen=()=>{this.emit($.l.Open,this)},this.conn.onclose=e=>{this.emit($.l.Close,e)},this.conn.onerror=e=>{this.emit($.l.Error,e)},this.conn.onmessage=e=>{try{let t=JSON.parse(e.data.toString());t.type===$.l.Metadata?this.emit($.l.Metadata,t):t.type===$.l.Transcript?this.emit($.l.Transcript,t):t.type===$.l.UtteranceEnd?this.emit($.l.UtteranceEnd,t):t.type===$.l.SpeechStarted?this.emit($.l.SpeechStarted,t):this.emit($.l.Unhandled,t)}catch(t){this.emit($.l.Error,{event:e,message:"Unable to parse `data` as JSON.",error:t})}})}configure(e){this.send(JSON.stringify({type:"Configure",processors:e}))}keepAlive(){this.send(JSON.stringify({type:"KeepAlive"}))}finalize(){this.send(JSON.stringify({type:"Finalize"}))}finish(){this.requestClose()}requestClose(){this.send(JSON.stringify({type:"CloseStream"}))}}var W=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};let X=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=y():t=fetch,(...e)=>t(...e)},K=(e,t)=>{let n=X(t),r=T();return(t,o)=>W(void 0,void 0,void 0,function*(){let i=new r(null==o?void 0:o.headers);return i.has("Authorization")||i.set("Authorization",`Token ${e}`),n(t,Object.assign(Object.assign({},o),{headers:i}))})},Y=()=>W(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(n.t.bind(n,29972,23))).Response:Response});var Z=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class Q extends V{constructor(e){if(super(e),w()&&!this.proxy)throw new r("Due to CORS we are unable to support REST-based API calls to our API from the browser. Please consider using a proxy: https://dpgr.am/js-proxy for more information.");this.fetch=K(this.key,this.namespaceOptions.fetch.client),this.proxy?this.baseUrl=this.namespaceOptions.fetch.options.proxy.url:this.baseUrl=this.namespaceOptions.fetch.options.url}_getErrorMessage(e){return e.msg||e.message||e.error_description||e.error||JSON.stringify(e)}_handleError(e,t){return Z(this,void 0,void 0,function*(){e instanceof(yield Y())?e.json().then(n=>{t(new i(this._getErrorMessage(n),e.status||500))}).catch(e=>{t(new s(this._getErrorMessage(e),e))}):t(new s(this._getErrorMessage(e),e))})}_getRequestOptions(e,t,n){let r={method:e};return r="GET"===e||"DELETE"===e?Object.assign(Object.assign({},r),t):Object.assign(Object.assign({duplex:"half",body:t},r),n),b()(this.namespaceOptions.fetch.options,r,{clone:!1})}_handleRequest(e,t,n,r){return Z(this,void 0,void 0,function*(){return new Promise((o,i)=>{(0,this.fetch)(t,this._getRequestOptions(e,n,r)).then(e=>{if(!e.ok)throw e;o(e)}).catch(e=>this._handleError(e,i))})})}get(e,t){return Z(this,void 0,void 0,function*(){return this._handleRequest("GET",e,t)})}post(e,t,n){return Z(this,void 0,void 0,function*(){return this._handleRequest("POST",e,t,n)})}put(e,t,n){return Z(this,void 0,void 0,function*(){return this._handleRequest("PUT",e,t,n)})}patch(e,t,n){return Z(this,void 0,void 0,function*(){return this._handleRequest("PATCH",e,t,n)})}delete(e,t){return Z(this,void 0,void 0,function*(){return this._handleRequest("DELETE",e,t)})}get proxy(){var e;return"proxy"===this.key&&!!(null===(e=this.namespaceOptions.fetch.options.proxy)||void 0===e?void 0:e.url)}}var ee=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class et extends Q{constructor(){super(...arguments),this.namespace="listen"}transcribeUrl(e,t,n=":version/listen"){var i,s;return ee(this,void 0,void 0,function*(){try{let o;if(O(e))o=JSON.stringify(e);else throw new r("Unknown transcription source type");if(void 0!==t&&"callback"in t)throw new r("Callback cannot be provided as an option to a synchronous transcription. Use `transcribeUrlCallback` or `transcribeFileCallback` instead.");if((null===(i=null==t?void 0:t.keyterm)||void 0===i?void 0:i.length)&&!(null===(s=t.model)||void 0===s?void 0:s.startsWith("nova-3")))throw new r("Keyterms are only supported with the Nova 3 models.");let a=this.getRequestUrl(n,{},Object.assign({},t));return{result:yield this.post(a,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}transcribeFile(e,t,n=":version/listen"){return ee(this,void 0,void 0,function*(){try{let o;if(j(e))o=e;else throw new r("Unknown transcription source type");if(void 0!==t&&"callback"in t)throw new r("Callback cannot be provided as an option to a synchronous transcription. Use `transcribeUrlCallback` or `transcribeFileCallback` instead.");let i=this.getRequestUrl(n,{},Object.assign({},t));return{result:yield this.post(i,o,{headers:{"Content-Type":"deepgram/audio+video"}}).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}transcribeUrlCallback(e,t,n,i=":version/listen"){return ee(this,void 0,void 0,function*(){try{let o;if(O(e))o=JSON.stringify(e);else throw new r("Unknown transcription source type");let s=this.getRequestUrl(i,{},Object.assign(Object.assign({},n),{callback:t.toString()}));return{result:yield this.post(s,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}transcribeFileCallback(e,t,n,i=":version/listen"){return ee(this,void 0,void 0,function*(){try{let o;if(j(e))o=e;else throw new r("Unknown transcription source type");let s=this.getRequestUrl(i,{},Object.assign(Object.assign({},n),{callback:t.toString()}));return{result:yield this.post(s,o,{headers:{"Content-Type":"deepgram/audio+video"}}).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}}class en extends V{constructor(){super(...arguments),this.namespace="listen"}get prerecorded(){return new et(this.options)}live(e={},t=":version/listen"){return new J(this.options,e,t)}}var er=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class eo extends Q{constructor(){super(...arguments),this.namespace="manage"}getTokenDetails(e=":version/auth/token"){return er(this,void 0,void 0,function*(){try{let t=this.getRequestUrl(e);return{result:yield this.get(t).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjects(e=":version/projects"){return er(this,void 0,void 0,function*(){try{let t=this.getRequestUrl(e);return{result:yield this.get(t).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProject(e,t=":version/projects/:projectId"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}updateProject(e,t,n=":version/projects/:projectId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t),o=JSON.stringify(t);return{result:yield this.patch(r,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}deleteProject(e,t=":version/projects/:projectId"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return yield this.delete(n),{error:null}}catch(e){if(o(e))return{error:e};throw e}})}getProjectKeys(e,t=":version/projects/:projectId/keys"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectKey(e,t,n=":version/projects/:projectId/keys/:keyId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,keyId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}createProjectKey(e,t,n=":version/projects/:projectId/keys"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t),o=JSON.stringify(t);return{result:yield this.post(r,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}deleteProjectKey(e,t,n=":version/projects/:projectId/keys/:keyId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,keyId:t});return yield this.delete(r),{error:null}}catch(e){if(o(e))return{error:e};throw e}})}getProjectMembers(e,t=":version/projects/:projectId/members"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}removeProjectMember(e,t,n=":version/projects/:projectId/members/:memberId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,memberId:t});return yield this.delete(r),{error:null}}catch(e){if(o(e))return{error:e};throw e}})}getProjectMemberScopes(e,t,n=":version/projects/:projectId/members/:memberId/scopes"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,memberId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}updateProjectMemberScope(e,t,n,r=":version/projects/:projectId/members/:memberId/scopes"){return er(this,void 0,void 0,function*(){try{let o=this.getRequestUrl(r,{projectId:e,memberId:t},n),i=JSON.stringify(n);return{result:yield this.put(o,i).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectInvites(e,t=":version/projects/:projectId/invites"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}sendProjectInvite(e,t,n=":version/projects/:projectId/invites"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t),o=JSON.stringify(t);return{result:yield this.post(r,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}deleteProjectInvite(e,t,n=":version/projects/:projectId/invites/:email"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,email:t});return yield this.delete(r).then(e=>e.json()),{error:null}}catch(e){if(o(e))return{error:e};throw e}})}leaveProject(e,t=":version/projects/:projectId/leave"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.delete(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectUsageRequests(e,t,n=":version/projects/:projectId/requests"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t);return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectUsageRequest(e,t,n=":version/projects/:projectId/requests/:requestId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,requestId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectUsageSummary(e,t,n=":version/projects/:projectId/usage"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t);return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectUsageFields(e,t,n=":version/projects/:projectId/usage/fields"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t);return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectBalances(e,t=":version/projects/:projectId/balances"){return er(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getProjectBalance(e,t,n=":version/projects/:projectId/balances/:balanceId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,balanceId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getAllModels(e,t={},n=":version/projects/:projectId/models"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e},t);return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getModel(e,t,n=":version/projects/:projectId/models/:modelId"){return er(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,modelId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}}var ei=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class es extends Q{constructor(){super(...arguments),this.namespace="models"}getAll(e=":version/models",t={}){return ei(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(e,{},t);return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getModel(e,t=":version/models/:modelId"){return ei(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{modelId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}}var ea=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class eu extends Q{constructor(){super(...arguments),this.namespace="selfhosted"}listCredentials(e,t=":version/projects/:projectId/onprem/distribution/credentials"){return ea(this,void 0,void 0,function*(){try{let n=this.getRequestUrl(t,{projectId:e});return{result:yield this.get(n).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}getCredentials(e,t,n=":version/projects/:projectId/onprem/distribution/credentials/:credentialsId"){return ea(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,credentialsId:t});return{result:yield this.get(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}createCredentials(e,t,n=":version/projects/:projectId/onprem/distribution/credentials"){return ea(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e}),o=JSON.stringify(t);return{result:yield this.post(r,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}deleteCredentials(e,t,n=":version/projects/:projectId/onprem/distribution/credentials/:credentialsId"){return ea(this,void 0,void 0,function*(){try{let r=this.getRequestUrl(n,{projectId:e,credentialsId:t});return{result:yield this.delete(r).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}}var el=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class ec extends Q{constructor(){super(...arguments),this.namespace="read"}analyzeUrl(e,t,n=":version/read"){return el(this,void 0,void 0,function*(){try{let o;if(O(e))o=JSON.stringify(e);else throw new r("Unknown source type");if(void 0!==t&&"callback"in t)throw new r("Callback cannot be provided as an option to a synchronous transcription. Use `analyzeUrlCallback` or `analyzeTextCallback` instead.");let i=this.getRequestUrl(n,{},Object.assign({},t));return{result:yield this.post(i,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}analyzeText(e,t,n=":version/read"){return el(this,void 0,void 0,function*(){try{let o;if(x(e))o=JSON.stringify(e);else throw new r("Unknown source type");if(void 0!==t&&"callback"in t)throw new r("Callback cannot be provided as an option to a synchronous requests. Use `analyzeUrlCallback` or `analyzeTextCallback` instead.");let i=this.getRequestUrl(n,{},Object.assign({},t));return{result:yield this.post(i,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}analyzeUrlCallback(e,t,n,i=":version/read"){return el(this,void 0,void 0,function*(){try{let o;if(O(e))o=JSON.stringify(e);else throw new r("Unknown source type");let s=this.getRequestUrl(i,{},Object.assign(Object.assign({},n),{callback:t.toString()}));return{result:yield this.post(s,o).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}analyzeTextCallback(e,t,n,i=":version/read"){return el(this,void 0,void 0,function*(){try{let o;if(x(e))o=JSON.stringify(e);else throw new r("Unknown source type");let s=this.getRequestUrl(i,{},Object.assign(Object.assign({},n),{callback:t.toString()}));return{result:yield this.post(s,o,{headers:{"Content-Type":"deepgram/audio+video"}}).then(e=>e.json()),error:null}}catch(e){if(o(e))return{result:null,error:e};throw e}})}}(c=f||(f={})).Open="Open",c.Close="Close",c.Error="Error",c.Metadata="Metadata",c.Flushed="Flushed",c.Warning="Warning",c.Audio="Audio",c.Unhandled="Unhandled";var ed=n(86300).Buffer;class eh extends z{constructor(e,t={},n=":version/speak"){super(e),this.namespace="speak",this.connect(t,n)}setupConnection(){this.conn&&(this.conn.onopen=()=>{this.emit(f.Open,this)},this.conn.onclose=e=>{this.emit(f.Close,e)},this.conn.onerror=e=>{this.emit(f.Error,e)},this.conn.onmessage=e=>{this.handleMessage(e)})}handleTextMessage(e){e.type===f.Metadata?this.emit(f.Metadata,e):e.type===f.Flushed?this.emit(f.Flushed,e):e.type===f.Warning?this.emit(f.Warning,e):this.emit(f.Unhandled,e)}handleBinaryMessage(e){this.emit(f.Audio,e)}sendText(e){this.send(JSON.stringify({type:"Speak",text:e}))}flush(){this.send(JSON.stringify({type:"Flush"}))}clear(){this.send(JSON.stringify({type:"Clear"}))}requestClose(){this.send(JSON.stringify({type:"Close"}))}handleMessage(e){if("string"==typeof e.data)try{let t=JSON.parse(e.data);this.handleTextMessage(t)}catch(t){this.emit(f.Error,{event:e,message:"Unable to parse `data` as JSON.",error:t})}else e.data instanceof Blob?e.data.arrayBuffer().then(e=>{this.handleBinaryMessage(ed.from(e))}):e.data instanceof ArrayBuffer?this.handleBinaryMessage(ed.from(e.data)):ed.isBuffer(e.data)?this.handleBinaryMessage(e.data):(console.log("Received unknown data type",e.data),this.emit(f.Error,{event:e,message:"Received unknown data type."}))}}var ep=function(e,t,n,r){return new(n||(n=Promise))(function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(s,a)}u((r=r.apply(e,t||[])).next())})};class ef extends Q{constructor(){super(...arguments),this.namespace="speak"}request(e,t,n=":version/speak"){return ep(this,void 0,void 0,function*(){let o;if(x(e))o=JSON.stringify(e);else throw new r("Unknown transcription source type");let i=this.getRequestUrl(n,{},Object.assign({model:"aura-asteria-en"},t));return this.result=yield this.post(i,o,{headers:{Accept:"audio/*","Content-Type":"application/json"}}),this})}getStream(){return ep(this,void 0,void 0,function*(){if(!this.result)throw new s("Tried to get stream before making request","");return this.result.body})}getHeaders(){return ep(this,void 0,void 0,function*(){if(!this.result)throw new s("Tried to get headers before making request","");return this.result.headers})}}class e_ extends V{constructor(){super(...arguments),this.namespace="speak"}request(e,t,n=":version/speak"){return new ef(this.options).request(e,t,n)}live(e={},t=":version/speak"){return new eh(this.options,e,t)}}(d=_||(_={})).Open="Open",d.Close="Close",d.Error="Error",d.Audio="Audio",d.Welcome="Welcome",d.SettingsApplied="SettingsApplied",d.ConversationText="ConversationText",d.UserStartedSpeaking="UserStartedSpeaking",d.AgentThinking="AgentThinking",d.FunctionCallRequest="FunctionCallRequest",d.FunctionCalling="FunctionCalling",d.AgentStartedSpeaking="AgentStartedSpeaking",d.AgentAudioDone="AgentAudioDone",d.InjectionRefused="InjectionRefused",d.InstructionsUpdated="InstructionsUpdated",d.SpeakUpdated="SpeakUpdated",d.Unhandled="Unhandled";var em=n(86300).Buffer;class ev extends z{constructor(e,t="/agent"){var n,r,o,i;super(e),this.namespace="agent",this.baseUrl=null!==(i=null===(o=null===(r=null===(n=e.agent)||void 0===n?void 0:n.websocket)||void 0===r?void 0:r.options)||void 0===o?void 0:o.url)&&void 0!==i?i:D,this.connect({},t)}setupConnection(){this.conn&&(this.conn.onopen=()=>{this.emit(_.Open,this)},this.conn.onclose=e=>{this.emit(_.Close,e)},this.conn.onerror=e=>{this.emit(_.Error,e)},this.conn.onmessage=e=>{this.handleMessage(e)})}handleMessage(e){if("string"==typeof e.data)try{let t=JSON.parse(e.data);this.handleTextMessage(t)}catch(t){this.emit(_.Error,{event:e,message:"Unable to parse `data` as JSON.",error:t})}else e.data instanceof Blob?e.data.arrayBuffer().then(e=>{this.handleBinaryMessage(em.from(e))}):e.data instanceof ArrayBuffer?this.handleBinaryMessage(em.from(e.data)):em.isBuffer(e.data)?this.handleBinaryMessage(e.data):(console.log("Received unknown data type",e.data),this.emit(_.Error,{event:e,message:"Received unknown data type."}))}handleBinaryMessage(e){this.emit(_.Audio,e)}handleTextMessage(e){e.type in _?this.emit(e.type,e):this.emit(_.Unhandled,e)}configure(e){var t,n,o;if(!e.agent.listen.model.startsWith("nova-3")&&(null===(t=e.agent.listen.keyterm)||void 0===t?void 0:t.length))throw new r("Keyterms are only supported with the Nova 3 models.");let i=Object.assign({},e);i.audio.input.sample_rate=null===(n=e.audio.input)||void 0===n?void 0:n.sampleRate,delete i.audio.input.sampleRate,i.audio.output.sample_rate=null===(o=e.audio.output)||void 0===o?void 0:o.sampleRate,delete i.audio.output.sampleRate,this.send(JSON.stringify(Object.assign({type:"SettingsConfiguration"},i)))}updateInstructions(e){this.send(JSON.stringify({type:"UpdateInstructions",instructions:e}))}updateSpeak(e){this.send(JSON.stringify({type:"UpdateSpeak",model:e}))}injectAgentMessage(e){this.send(JSON.stringify({type:"InjectAgentMessage",message:e}))}functionCallResponse(e){this.send(JSON.stringify(Object.assign({type:"FunctionCallResponse"},e)))}keepAlive(){this.send(JSON.stringify({type:"KeepAlive"}))}}class ey extends V{get listen(){return new en(this.options)}get manage(){return new eo(this.options)}get models(){return new es(this.options)}get onprem(){return this.selfhosted}get selfhosted(){return new eu(this.options)}get read(){return new ec(this.options)}get speak(){return new e_(this.options)}agent(e="/agent"){return new ev(this.options,e)}get transcription(){throw new a}get projects(){throw new a}get keys(){throw new a}get members(){throw new a}get scopes(){throw new a}get invitation(){throw new a}get usage(){throw new a}get billing(){throw new a}}function eg(e,t){let n={};return"string"==typeof e||"function"==typeof e?("object"==typeof t&&(n=t),n.key=e):"object"==typeof e&&(n=e),new ey(n)}},61023:function(e,t,n){"use strict";var r,o;n.d(t,{l:function(){return r}}),(o=r||(r={})).Open="open",o.Close="close",o.Error="error",o.Transcript="Results",o.Metadata="Metadata",o.UtteranceEnd="UtteranceEnd",o.SpeechStarted="SpeechStarted",o.Unhandled="Unhandled"},29972:function(e,t,n){var r="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==n.g&&n.g,o=function(){function e(){this.fetch=!1,this.DOMException=r.DOMException}return e.prototype=r,new e}();(function(e){var t=void 0!==o&&o||"undefined"!=typeof self&&self||void 0!==n.g&&n.g||{},r={searchParams:"URLSearchParams"in t,iterable:"Symbol"in t&&"iterator"in Symbol,blob:"FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(e){return!1}}(),formData:"FormData"in t,arrayBuffer:"ArrayBuffer"in t};if(r.arrayBuffer)var i=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],s=ArrayBuffer.isView||function(e){return e&&i.indexOf(Object.prototype.toString.call(e))>-1};function a(e){if("string"!=typeof e&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||""===e)throw TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function u(e){return"string"!=typeof e&&(e=String(e)),e}function l(e){var t={next:function(){var t=e.shift();return{done:void 0===t,value:t}}};return r.iterable&&(t[Symbol.iterator]=function(){return t}),t}function c(e){this.map={},e instanceof c?e.forEach(function(e,t){this.append(t,e)},this):Array.isArray(e)?e.forEach(function(e){if(2!=e.length)throw TypeError("Headers constructor: expected name/value pair to be length 2, found"+e.length);this.append(e[0],e[1])},this):e&&Object.getOwnPropertyNames(e).forEach(function(t){this.append(t,e[t])},this)}function d(e){if(!e._noBody){if(e.bodyUsed)return Promise.reject(TypeError("Already read"));e.bodyUsed=!0}}function h(e){return new Promise(function(t,n){e.onload=function(){t(e.result)},e.onerror=function(){n(e.error)}})}function p(e){var t=new FileReader,n=h(t);return t.readAsArrayBuffer(e),n}function f(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function _(){return this.bodyUsed=!1,this._initBody=function(e){if(this.bodyUsed=this.bodyUsed,this._bodyInit=e,e){if("string"==typeof e)this._bodyText=e;else if(r.blob&&Blob.prototype.isPrototypeOf(e))this._bodyBlob=e;else if(r.formData&&FormData.prototype.isPrototypeOf(e))this._bodyFormData=e;else if(r.searchParams&&URLSearchParams.prototype.isPrototypeOf(e))this._bodyText=e.toString();else{var t;r.arrayBuffer&&r.blob&&(t=e)&&DataView.prototype.isPrototypeOf(t)?(this._bodyArrayBuffer=f(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):r.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(e)||s(e))?this._bodyArrayBuffer=f(e):this._bodyText=e=Object.prototype.toString.call(e)}}else this._noBody=!0,this._bodyText="";!this.headers.get("content-type")&&("string"==typeof e?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},r.blob&&(this.blob=function(){var e=d(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(!this._bodyFormData)return Promise.resolve(new Blob([this._bodyText]));throw Error("could not read FormData body as blob")}),this.arrayBuffer=function(){if(this._bodyArrayBuffer)return d(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer));if(r.blob)return this.blob().then(p);throw Error("could not read as ArrayBuffer")},this.text=function(){var e,t,n,r,o,i=d(this);if(i)return i;if(this._bodyBlob)return e=this._bodyBlob,n=h(t=new FileReader),o=(r=/charset=([A-Za-z0-9_-]+)/.exec(e.type))?r[1]:"utf-8",t.readAsText(e,o),n;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),n=Array(t.length),r=0;r<t.length;r++)n[r]=String.fromCharCode(t[r]);return n.join("")}(this._bodyArrayBuffer));if(!this._bodyFormData)return Promise.resolve(this._bodyText);throw Error("could not read FormData body as text")},r.formData&&(this.formData=function(){return this.text().then(y)}),this.json=function(){return this.text().then(JSON.parse)},this}c.prototype.append=function(e,t){e=a(e),t=u(t);var n=this.map[e];this.map[e]=n?n+", "+t:t},c.prototype.delete=function(e){delete this.map[a(e)]},c.prototype.get=function(e){return e=a(e),this.has(e)?this.map[e]:null},c.prototype.has=function(e){return this.map.hasOwnProperty(a(e))},c.prototype.set=function(e,t){this.map[a(e)]=u(t)},c.prototype.forEach=function(e,t){for(var n in this.map)this.map.hasOwnProperty(n)&&e.call(t,this.map[n],n,this)},c.prototype.keys=function(){var e=[];return this.forEach(function(t,n){e.push(n)}),l(e)},c.prototype.values=function(){var e=[];return this.forEach(function(t){e.push(t)}),l(e)},c.prototype.entries=function(){var e=[];return this.forEach(function(t,n){e.push([n,t])}),l(e)},r.iterable&&(c.prototype[Symbol.iterator]=c.prototype.entries);var m=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function v(e,n){if(!(this instanceof v))throw TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var r,o,i=(n=n||{}).body;if(e instanceof v){if(e.bodyUsed)throw TypeError("Already read");this.url=e.url,this.credentials=e.credentials,n.headers||(this.headers=new c(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,i||null==e._bodyInit||(i=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=n.credentials||this.credentials||"same-origin",(n.headers||!this.headers)&&(this.headers=new c(n.headers)),this.method=(o=(r=n.method||this.method||"GET").toUpperCase(),m.indexOf(o)>-1?o:r),this.mode=n.mode||this.mode||null,this.signal=n.signal||this.signal||function(){if("AbortController"in t)return new AbortController().signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),("GET"===this.method||"HEAD"===this.method)&&("no-store"===n.cache||"no-cache"===n.cache)){var s=/([?&])_=[^&]*/;s.test(this.url)?this.url=this.url.replace(s,"$1_="+new Date().getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+new Date().getTime()}}function y(e){var t=new FormData;return e.trim().split("&").forEach(function(e){if(e){var n=e.split("="),r=n.shift().replace(/\+/g," "),o=n.join("=").replace(/\+/g," ");t.append(decodeURIComponent(r),decodeURIComponent(o))}}),t}function g(e,t){if(!(this instanceof g))throw TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(t||(t={}),this.type="default",this.status=void 0===t.status?200:t.status,this.status<200||this.status>599)throw RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===t.statusText?"":""+t.statusText,this.headers=new c(t.headers),this.url=t.url||"",this._initBody(e)}v.prototype.clone=function(){return new v(this,{body:this._bodyInit})},_.call(v.prototype),_.call(g.prototype),g.prototype.clone=function(){return new g(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new c(this.headers),url:this.url})},g.error=function(){var e=new g(null,{status:200,statusText:""});return e.ok=!1,e.status=0,e.type="error",e};var b=[301,302,303,307,308];g.redirect=function(e,t){if(-1===b.indexOf(t))throw RangeError("Invalid status code");return new g(null,{status:t,headers:{location:e}})},e.DOMException=t.DOMException;try{new e.DOMException}catch(t){e.DOMException=function(e,t){this.message=e,this.name=t;var n=Error(e);this.stack=n.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function w(n,o){return new Promise(function(i,s){var l=new v(n,o);if(l.signal&&l.signal.aborted)return s(new e.DOMException("Aborted","AbortError"));var d=new XMLHttpRequest;function h(){d.abort()}if(d.onload=function(){var e,t,n={statusText:d.statusText,headers:(e=d.getAllResponseHeaders()||"",t=new c,e.replace(/\r?\n[\t ]+/g," ").split("\r").map(function(e){return 0===e.indexOf("\n")?e.substr(1,e.length):e}).forEach(function(e){var n=e.split(":"),r=n.shift().trim();if(r){var o=n.join(":").trim();try{t.append(r,o)}catch(e){console.warn("Response "+e.message)}}}),t)};0===l.url.indexOf("file://")&&(d.status<200||d.status>599)?n.status=200:n.status=d.status,n.url="responseURL"in d?d.responseURL:n.headers.get("X-Request-URL");var r="response"in d?d.response:d.responseText;setTimeout(function(){i(new g(r,n))},0)},d.onerror=function(){setTimeout(function(){s(TypeError("Network request failed"))},0)},d.ontimeout=function(){setTimeout(function(){s(TypeError("Network request timed out"))},0)},d.onabort=function(){setTimeout(function(){s(new e.DOMException("Aborted","AbortError"))},0)},d.open(l.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}(l.url),!0),"include"===l.credentials?d.withCredentials=!0:"omit"===l.credentials&&(d.withCredentials=!1),"responseType"in d&&(r.blob?d.responseType="blob":r.arrayBuffer&&(d.responseType="arraybuffer")),o&&"object"==typeof o.headers&&!(o.headers instanceof c||t.Headers&&o.headers instanceof t.Headers)){var p=[];Object.getOwnPropertyNames(o.headers).forEach(function(e){p.push(a(e)),d.setRequestHeader(e,u(o.headers[e]))}),l.headers.forEach(function(e,t){-1===p.indexOf(t)&&d.setRequestHeader(t,e)})}else l.headers.forEach(function(e,t){d.setRequestHeader(t,e)});l.signal&&(l.signal.addEventListener("abort",h),d.onreadystatechange=function(){4===d.readyState&&l.signal.removeEventListener("abort",h)}),d.send(void 0===l._bodyInit?null:l._bodyInit)})}w.polyfill=!0,t.fetch||(t.fetch=w,t.Headers=c,t.Request=v,t.Response=g),e.Headers=c,e.Request=v,e.Response=g,e.fetch=w,Object.defineProperty(e,"__esModule",{value:!0})})({}),o.fetch.ponyfill=!0,delete o.fetch.polyfill;var i=r.fetch?r:o;(t=i.fetch).default=i.fetch,t.fetch=i.fetch,t.Headers=i.Headers,t.Request=i.Request,t.Response=i.Response,e.exports=t},55189:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.useMicVAD=t.defaultReactRealTimeVADOptions=t.utils=void 0;let s=n(40068),a=i(n(2265));var u=n(40068);Object.defineProperty(t,"utils",{enumerable:!0,get:function(){return u.utils}});let l={startOnLoad:!0,userSpeakingThreshold:.6};t.defaultReactRealTimeVADOptions={...s.defaultRealTimeVADOptions,...l};let c=Object.keys(l),d=Object.keys(s.defaultRealTimeVADOptions),h=(e,t)=>e.reduce((e,n)=>(e[n]=t[n],e),{});function p(e){let t=a.default.useRef(e);return f(()=>{t.current=e}),a.default.useCallback((...e)=>t.current.apply(void 0,e),[])}t.useMicVAD=function(e){var n;let[r,o]=(n=e,[h(c,n={...t.defaultReactRealTimeVADOptions,...n}),h(d,n)]),[i,u]=(0,a.useReducer)((e,t)=>t>r.userSpeakingThreshold,!1),[l,f]=(0,a.useState)(!0),[_,m]=(0,a.useState)(!1),[v,y]=(0,a.useState)(!1),[g,b]=(0,a.useState)(null);p(o.onFrameProcessed),o.onFrameProcessed=p(e=>{u(e.isSpeech)});let{onSpeechEnd:w,onSpeechStart:A,onVADMisfire:S}=o,T=p(w),O=p(A),x=p(S);o.onSpeechEnd=T,o.onSpeechStart=O,o.onVADMisfire=x,(0,a.useEffect)(()=>{let e;let t=!1;return(async()=>{try{if(e=await s.MicVAD.new(o),t){e.destroy();return}}catch(e){f(!1),e instanceof Error?m({message:e.message}):m({message:e});return}b(e),f(!1),r.startOnLoad&&(e?.start(),y(!0))})().catch(e=>{console.log("Well that didn't work")}),function(){e?.destroy(),t=!0,l||_||y(!1)}},[]);let j=()=>{l||_||(g?.pause(),y(!1))},k=()=>{l||_||(g?.start(),y(!0))};return{listening:v,errored:_,loading:l,userSpeaking:i,pause:j,start:k,toggle:()=>{v?j():k()}}};let f="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?a.default.useLayoutEffect:a.default.useEffect},76420:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FrameProcessor=t.validateOptions=t.defaultFrameProcessorOptions=void 0;let r=n(91038),o=n(63271),i=[512,1024,1536];t.defaultFrameProcessorOptions={positiveSpeechThreshold:.5,negativeSpeechThreshold:.35,preSpeechPadFrames:1,redemptionFrames:8,frameSamples:1536,minSpeechFrames:3,submitUserSpeechOnPause:!1},t.validateOptions=function(e){i.includes(e.frameSamples)||o.log.warn("You are using an unusual frame size"),(e.positiveSpeechThreshold<0||e.negativeSpeechThreshold>1)&&o.log.error("postiveSpeechThreshold should be a number between 0 and 1"),(e.negativeSpeechThreshold<0||e.negativeSpeechThreshold>e.positiveSpeechThreshold)&&o.log.error("negativeSpeechThreshold should be between 0 and postiveSpeechThreshold"),e.preSpeechPadFrames<0&&o.log.error("preSpeechPadFrames should be positive"),e.redemptionFrames<0&&o.log.error("preSpeechPadFrames should be positive")};let s=e=>{let t=e.reduce((e,t)=>(e.push(e.at(-1)+t.length),e),[0]),n=new Float32Array(t.at(-1));return e.forEach((e,r)=>{let o=t[r];n.set(e,o)}),n};class a{constructor(e,t,n){this.modelProcessFunc=e,this.modelResetFunc=t,this.options=n,this.speaking=!1,this.redemptionCounter=0,this.active=!1,this.reset=()=>{this.speaking=!1,this.audioBuffer=[],this.modelResetFunc(),this.redemptionCounter=0},this.pause=()=>(this.active=!1,this.options.submitUserSpeechOnPause)?this.endSegment():(this.reset(),{}),this.resume=()=>{this.active=!0},this.endSegment=()=>{let e=this.audioBuffer;this.audioBuffer=[];let t=this.speaking;this.reset();let n=e.reduce((e,t)=>e+ +t.isSpeech,0);if(t){if(!(n>=this.options.minSpeechFrames))return{msg:r.Message.VADMisfire};{let t=s(e.map(e=>e.frame));return{msg:r.Message.SpeechEnd,audio:t}}}return{}},this.process=async e=>{if(!this.active)return{};let t=await this.modelProcessFunc(e);if(this.audioBuffer.push({frame:e,isSpeech:t.isSpeech>=this.options.positiveSpeechThreshold}),t.isSpeech>=this.options.positiveSpeechThreshold&&this.redemptionCounter&&(this.redemptionCounter=0),t.isSpeech>=this.options.positiveSpeechThreshold&&!this.speaking)return this.speaking=!0,{probs:t,msg:r.Message.SpeechStart};if(t.isSpeech<this.options.negativeSpeechThreshold&&this.speaking&&++this.redemptionCounter>=this.options.redemptionFrames){this.redemptionCounter=0,this.speaking=!1;let e=this.audioBuffer;if(this.audioBuffer=[],!(e.reduce((e,t)=>e+ +t.isSpeech,0)>=this.options.minSpeechFrames))return{probs:t,msg:r.Message.VADMisfire};{let n=s(e.map(e=>e.frame));return{probs:t,msg:r.Message.SpeechEnd,audio:n}}}if(!this.speaking)for(;this.audioBuffer.length>this.options.preSpeechPadFrames;)this.audioBuffer.shift();return{probs:t}},this.audioBuffer=[],this.reset()}}t.FrameProcessor=a},36965:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.utils=void 0;let a=i(n(62096));t.utils={minFramesForTargetMS:a.minFramesForTargetMS,arrayBufferToBase64:a.arrayBufferToBase64,encodeWAV:a.encodeWAV},s(n(16024),t),s(n(76420),t),s(n(91038),t),s(n(63271),t),s(n(70208),t),s(n(73629),t)},63271:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.log=t.LOG_PREFIX=void 0,t.LOG_PREFIX="[VAD]";let n=["error","debug","warn"].reduce((e,n)=>(e[n]=(...e)=>{console[n](t.LOG_PREFIX,...e)},e),{});t.log=n},91038:function(e,t){"use strict";var n,r;Object.defineProperty(t,"__esModule",{value:!0}),t.Message=void 0,(r=n||(t.Message=n={})).AudioFrame="AUDIO_FRAME",r.SpeechStart="SPEECH_START",r.VADMisfire="VAD_MISFIRE",r.SpeechEnd="SPEECH_END",r.SpeechStop="SPEECH_STOP"},70208:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Silero=void 0;let r=n(63271);class o{constructor(e,t){this.ort=e,this.modelFetcher=t,this.init=async()=>{r.log.debug("initializing vad");let e=await this.modelFetcher();this._session=await this.ort.InferenceSession.create(e),this._sr=new this.ort.Tensor("int64",[16000n]),this.reset_state(),r.log.debug("vad is initialized")},this.reset_state=()=>{let e=Array(128).fill(0);this._h=new this.ort.Tensor("float32",e,[2,1,64]),this._c=new this.ort.Tensor("float32",e,[2,1,64])},this.process=async e=>{let t={input:new this.ort.Tensor("float32",e,[1,e.length]),h:this._h,c:this._c,sr:this._sr},n=await this._session.run(t);this._h=n.hn,this._c=n.cn;let[r]=n.output.data;return{notSpeech:1-r,isSpeech:r}}}}t.Silero=o,o.new=async(e,t)=>{let n=new o(e,t);return await n.init(),n}},16024:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PlatformAgnosticNonRealTimeVAD=t.defaultNonRealTimeVADOptions=void 0;let r=n(76420),o=n(91038),i=n(70208),s=n(73629);t.defaultNonRealTimeVADOptions={...r.defaultFrameProcessorOptions,ortConfig:void 0};class a{static async _new(e,n,r={}){let o={...t.defaultNonRealTimeVADOptions,...r};void 0!==o.ortConfig&&o.ortConfig(n);let i=new this(e,n,o);return await i.init(),i}constructor(e,t,n){this.modelFetcher=e,this.ort=t,this.options=n,this.init=async()=>{let e=await i.Silero.new(this.ort,this.modelFetcher);this.frameProcessor=new r.FrameProcessor(e.process,e.reset_state,{frameSamples:this.options.frameSamples,positiveSpeechThreshold:this.options.positiveSpeechThreshold,negativeSpeechThreshold:this.options.negativeSpeechThreshold,redemptionFrames:this.options.redemptionFrames,preSpeechPadFrames:this.options.preSpeechPadFrames,minSpeechFrames:this.options.minSpeechFrames,submitUserSpeechOnPause:this.options.submitUserSpeechOnPause}),this.frameProcessor.resume()},this.run=async function*(e,t){let n={nativeSampleRate:t,targetSampleRate:16e3,targetFrameSize:this.options.frameSamples},r=new s.Resampler(n),i=0,a=0,u=0;for await(let t of r.stream(e)){let{msg:e,audio:n}=await this.frameProcessor.process(t);switch(e){case o.Message.SpeechStart:i=u*this.options.frameSamples/16;break;case o.Message.SpeechEnd:a=(u+1)*this.options.frameSamples/16,yield{audio:n,start:i,end:a}}u++}let{msg:l,audio:c}=this.frameProcessor.endSegment();l==o.Message.SpeechEnd&&(yield{audio:c,start:i,end:u*this.options.frameSamples/16})},(0,r.validateOptions)(n)}}t.PlatformAgnosticNonRealTimeVAD=a},73629:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Resampler=void 0;let r=n(63271);class o{constructor(e){this.options=e,this.process=e=>{let t=[];for(this.fillInputBuffer(e);this.hasEnoughDataForFrame();){let e=this.generateOutputFrame();t.push(e)}return t},this.stream=async function*(e){for(this.fillInputBuffer(e);this.hasEnoughDataForFrame();){let e=this.generateOutputFrame();yield e}},e.nativeSampleRate<16e3&&r.log.error("nativeSampleRate is too low. Should have 16000 = targetSampleRate <= nativeSampleRate"),this.inputBuffer=[]}fillInputBuffer(e){for(let t of e)this.inputBuffer.push(t)}hasEnoughDataForFrame(){return this.inputBuffer.length*this.options.targetSampleRate/this.options.nativeSampleRate>=this.options.targetFrameSize}generateOutputFrame(){let e=new Float32Array(this.options.targetFrameSize),t=0,n=0;for(;t<this.options.targetFrameSize;){let r=0,o=0;for(;n<Math.min(this.inputBuffer.length,(t+1)*this.options.nativeSampleRate/this.options.targetSampleRate);){let e=this.inputBuffer[n];void 0!==e&&(r+=e,o++),n++}e[t]=r/o,t++}return this.inputBuffer=this.inputBuffer.slice(n),e}}t.Resampler=o},62096:function(e,t){"use strict";function n(e,t,n){for(var r=0;r<n.length;r++)e.setUint8(t+r,n.charCodeAt(r))}Object.defineProperty(t,"__esModule",{value:!0}),t.encodeWAV=t.arrayBufferToBase64=t.minFramesForTargetMS=void 0,t.minFramesForTargetMS=function(e,t,n=16e3){return Math.ceil(e*n/1e3/t)},t.arrayBufferToBase64=function(e){for(var t="",n=new Uint8Array(e),r=n.byteLength,o=0;o<r;o++)t+=String.fromCharCode(n[o]);return btoa(t)},t.encodeWAV=function(e,t=3,r=16e3,o=1,i=32){var s=i/8,a=o*s,u=new ArrayBuffer(44+e.length*s),l=new DataView(u);return n(l,0,"RIFF"),l.setUint32(4,36+e.length*s,!0),n(l,8,"WAVE"),n(l,12,"fmt "),l.setUint32(16,16,!0),l.setUint16(20,t,!0),l.setUint16(22,o,!0),l.setUint32(24,r,!0),l.setUint32(28,r*a,!0),l.setUint16(32,a,!0),l.setUint16(34,i,!0),n(l,36,"data"),l.setUint32(40,e.length*s,!0),1===t?function(e,t,n){for(var r=0;r<n.length;r++,t+=2){var o=Math.max(-1,Math.min(1,n[r]));e.setInt16(t,o<0?32768*o:32767*o,!0)}}(l,44,e):function(e,t,n){for(var r=0;r<n.length;r++,t+=4)e.setFloat32(t,n[r],!0)}(l,44,e),u}},99531:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assetPath=void 0;let n="undefined"!=typeof window&&void 0!==window.document?window.document.currentScript:null,r="/";n&&(r=n.src.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/")),t.assetPath=e=>r+e},93693:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultModelFetcher=void 0,t.defaultModelFetcher=e=>fetch(e).then(e=>e.arrayBuffer())},40068:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.defaultRealTimeVADOptions=t.AudioNodeVAD=t.MicVAD=t.NonRealTimeVAD=t.Message=t.FrameProcessor=t.utils=t.defaultNonRealTimeVADOptions=void 0;let s=i(n(68888)),a=n(36965);Object.defineProperty(t,"FrameProcessor",{enumerable:!0,get:function(){return a.FrameProcessor}}),Object.defineProperty(t,"Message",{enumerable:!0,get:function(){return a.Message}});let u=n(32410),l=n(93693),c=n(99531);t.defaultNonRealTimeVADOptions={modelURL:(0,c.assetPath)("silero_vad.onnx"),modelFetcher:l.defaultModelFetcher};class d extends a.PlatformAgnosticNonRealTimeVAD{static async new(e={}){let{modelURL:n,modelFetcher:r}={...t.defaultNonRealTimeVADOptions,...e};return await this._new(()=>r(n),s,e)}}t.NonRealTimeVAD=d,t.utils={audioFileToArray:u.audioFileToArray,...a.utils};var h=n(66290);Object.defineProperty(t,"MicVAD",{enumerable:!0,get:function(){return h.MicVAD}}),Object.defineProperty(t,"AudioNodeVAD",{enumerable:!0,get:function(){return h.AudioNodeVAD}}),Object.defineProperty(t,"defaultRealTimeVADOptions",{enumerable:!0,get:function(){return h.defaultRealTimeVADOptions}})},66290:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);(!o||("get"in o?!t.__esModule:o.writable||o.configurable))&&(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.AudioNodeVAD=t.MicVAD=t.defaultRealTimeVADOptions=t.ort=void 0;let s=i(n(68888)),a=n(36965),u=n(99531),l=n(93693);t.ort=s,t.defaultRealTimeVADOptions={...a.defaultFrameProcessorOptions,onFrameProcessed:e=>{},onVADMisfire:()=>{a.log.debug("VAD misfire")},onSpeechStart:()=>{a.log.debug("Detected speech start")},onSpeechEnd:()=>{a.log.debug("Detected speech end")},workletURL:(0,u.assetPath)("vad.worklet.bundle.min.js"),modelURL:(0,u.assetPath)("silero_vad.onnx"),modelFetcher:l.defaultModelFetcher,stream:void 0,ortConfig:void 0};class c{static async new(e={}){let n;let r={...t.defaultRealTimeVADOptions,...e};(0,a.validateOptions)(r),n=void 0===r.stream?await navigator.mediaDevices.getUserMedia({audio:{...r.additionalAudioConstraints,channelCount:1,echoCancellation:!0,autoGainControl:!0,noiseSuppression:!0}}):r.stream;let o=new AudioContext,i=new MediaStreamAudioSourceNode(o,{mediaStream:n}),s=await d.new(o,r);return s.receive(i),new c(r,o,n,s,i)}constructor(e,t,n,r,o,i=!1){this.options=e,this.audioContext=t,this.stream=n,this.audioNodeVAD=r,this.sourceNode=o,this.listening=i,this.pause=()=>{this.audioNodeVAD.pause(),this.listening=!1},this.start=()=>{this.audioNodeVAD.start(),this.listening=!0},this.destroy=()=>{this.listening&&this.pause(),void 0===this.options.stream&&this.stream.getTracks().forEach(e=>e.stop()),this.sourceNode.disconnect(),this.audioNodeVAD.destroy(),this.audioContext.close()}}}t.MicVAD=c;class d{static async new(e,n={}){let r;let o={...t.defaultRealTimeVADOptions,...n};(0,a.validateOptions)(o),void 0!==o.ortConfig&&o.ortConfig(t.ort);try{await e.audioWorklet.addModule(o.workletURL)}catch(e){throw console.error(`Encountered an error while loading worklet. Please make sure the worklet vad.bundle.min.js included with @ricky0123/vad-web is available at the specified path:
        ${o.workletURL}
        If need be, you can customize the worklet file location using the \`workletURL\` option.`),e}let i=new AudioWorkletNode(e,"vad-helper-worklet",{processorOptions:{frameSamples:o.frameSamples}});try{r=await a.Silero.new(t.ort,()=>o.modelFetcher(o.modelURL))}catch(e){throw console.error(`Encountered an error while loading model file. Please make sure silero_vad.onnx, included with @ricky0123/vad-web, is available at the specified path:
      ${o.modelURL}
      If need be, you can customize the model file location using the \`modelsURL\` option.`),e}let s=new a.FrameProcessor(r.process,r.reset_state,{frameSamples:o.frameSamples,positiveSpeechThreshold:o.positiveSpeechThreshold,negativeSpeechThreshold:o.negativeSpeechThreshold,redemptionFrames:o.redemptionFrames,preSpeechPadFrames:o.preSpeechPadFrames,minSpeechFrames:o.minSpeechFrames,submitUserSpeechOnPause:o.submitUserSpeechOnPause}),u=new d(e,o,s,i);return i.port.onmessage=async e=>{if(e.data?.message===a.Message.AudioFrame){let t=new Float32Array(e.data.data);await u.processFrame(t)}},u}constructor(e,t,n,r){this.ctx=e,this.options=t,this.frameProcessor=n,this.entryNode=r,this.pause=()=>{let e=this.frameProcessor.pause();this.handleFrameProcessorEvent(e)},this.start=()=>{this.frameProcessor.resume()},this.receive=e=>{e.connect(this.entryNode)},this.processFrame=async e=>{let t=await this.frameProcessor.process(e);this.handleFrameProcessorEvent(t)},this.handleFrameProcessorEvent=e=>{switch(void 0!==e.probs&&this.options.onFrameProcessed(e.probs),e.msg){case a.Message.SpeechStart:this.options.onSpeechStart();break;case a.Message.VADMisfire:this.options.onVADMisfire();break;case a.Message.SpeechEnd:this.options.onSpeechEnd(e.audio)}},this.destroy=()=>{this.entryNode.port.postMessage({message:a.Message.SpeechStop}),this.entryNode.disconnect()}}}t.AudioNodeVAD=d},32410:function(e,t){"use strict";async function n(e){let t=new OfflineAudioContext(1,1,44100),n=new FileReader,r=null;if(await new Promise(o=>{n.addEventListener("loadend",e=>{let i=n.result;t.decodeAudioData(i,e=>{r=e,t.startRendering().then(e=>{console.log("Rendering completed successfully"),o()}).catch(e=>{console.error(`Rendering failed: ${e}`)})},e=>{console.log(`Error with decoding audio data: ${e}`)})}),n.readAsArrayBuffer(e)}),null===r)throw Error("some shit");let o=r,i=new Float32Array(o.length);for(let e=0;e<o.length;e++)for(let t=0;t<o.numberOfChannels;t++)i[e]+=o.getChannelData(t)[e];return{audio:i,sampleRate:o.sampleRate}}Object.defineProperty(t,"__esModule",{value:!0}),t.audioFileToArray=void 0,t.audioFileToArray=n},9535:function(e){"use strict";var t=function(e){var t;return!!e&&"object"==typeof e&&"[object RegExp]"!==(t=Object.prototype.toString.call(e))&&"[object Date]"!==t&&e.$$typeof!==n},n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function r(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a(Array.isArray(e)?[]:{},e,t):e}function o(e,t,n){return e.concat(t).map(function(e){return r(e,n)})}function i(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function s(e,t){try{return t in e}catch(e){return!1}}function a(e,n,u){(u=u||{}).arrayMerge=u.arrayMerge||o,u.isMergeableObject=u.isMergeableObject||t,u.cloneUnlessOtherwiseSpecified=r;var l,c,d=Array.isArray(n);return d!==Array.isArray(e)?r(n,u):d?u.arrayMerge(e,n,u):(c={},(l=u).isMergeableObject(e)&&i(e).forEach(function(t){c[t]=r(e[t],l)}),i(n).forEach(function(t){(!s(e,t)||Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))&&(s(e,t)&&l.isMergeableObject(n[t])?c[t]=(function(e,t){if(!t.customMerge)return a;var n=t.customMerge(e);return"function"==typeof n?n:a})(t,l)(e[t],n[t],l):c[t]=r(n[t],l))}),c)}a.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,n){return a(e,n,t)},{})},e.exports=a},68885:function(e){"use strict";var t,n="object"==typeof Reflect?Reflect:null,r=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function i(){i.init.call(this)}e.exports=i,e.exports.once=function(e,t){return new Promise(function(n,r){var o;function i(n){e.removeListener(t,s),r(n)}function s(){"function"==typeof e.removeListener&&e.removeListener("error",i),n([].slice.call(arguments))}_(e,t,s,{once:!0}),"error"!==t&&(o={once:!0},"function"==typeof e.on&&_(e,"error",i,o))})},i.EventEmitter=i,i.prototype._events=void 0,i.prototype._eventsCount=0,i.prototype._maxListeners=void 0;var s=10;function a(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function u(e){return void 0===e._maxListeners?i.defaultMaxListeners:e._maxListeners}function l(e,t,n,r){if(a(n),void 0===(i=e._events)?(i=e._events=Object.create(null),e._eventsCount=0):(void 0!==i.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),i=e._events),s=i[t]),void 0===s)s=i[t]=n,++e._eventsCount;else if("function"==typeof s?s=i[t]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),(o=u(e))>0&&s.length>o&&!s.warned){s.warned=!0;var o,i,s,l=Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=s.length,console&&console.warn&&console.warn(l)}return e}function c(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},o=c.bind(r);return o.listener=n,r.wrapFn=o,o}function h(e,t,n){var r=e._events;if(void 0===r)return[];var o=r[t];return void 0===o?[]:"function"==typeof o?n?[o.listener||o]:[o]:n?function(e){for(var t=Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(o):f(o,o.length)}function p(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function f(e,t){for(var n=Array(t),r=0;r<t;++r)n[r]=e[r];return n}function _(e,t,n,r){if("function"==typeof e.on)r.once?e.once(t,n):e.on(t,n);else if("function"==typeof e.addEventListener)e.addEventListener(t,function o(i){r.once&&e.removeEventListener(t,o),n(i)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}Object.defineProperty(i,"defaultMaxListeners",{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");s=e}}),i.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},i.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},i.prototype.getMaxListeners=function(){return u(this)},i.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var o="error"===e,i=this._events;if(void 0!==i)o=o&&void 0===i.error;else if(!o)return!1;if(o){if(t.length>0&&(s=t[0]),s instanceof Error)throw s;var s,a=Error("Unhandled error."+(s?" ("+s.message+")":""));throw a.context=s,a}var u=i[e];if(void 0===u)return!1;if("function"==typeof u)r(u,this,t);else for(var l=u.length,c=f(u,l),n=0;n<l;++n)r(c[n],this,t);return!0},i.prototype.addListener=function(e,t){return l(this,e,t,!1)},i.prototype.on=i.prototype.addListener,i.prototype.prependListener=function(e,t){return l(this,e,t,!0)},i.prototype.once=function(e,t){return a(t),this.on(e,d(this,e,t)),this},i.prototype.prependOnceListener=function(e,t){return a(t),this.prependListener(e,d(this,e,t)),this},i.prototype.removeListener=function(e,t){var n,r,o,i,s;if(a(t),void 0===(r=this._events)||void 0===(n=r[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(o=-1,i=n.length-1;i>=0;i--)if(n[i]===t||n[i].listener===t){s=n[i].listener,o=i;break}if(o<0)return this;0===o?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,o),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,s||t)}return this},i.prototype.off=i.prototype.removeListener,i.prototype.removeAllListeners=function(e){var t,n,r;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0==arguments.length){var o,i=Object.keys(n);for(r=0;r<i.length;++r)"removeListener"!==(o=i[r])&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},i.prototype.listeners=function(e){return h(this,e,!0)},i.prototype.rawListeners=function(e){return h(this,e,!1)},i.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},i.prototype.listenerCount=p,i.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},28798:function(e,t,n){var r,o,i,s,a,u,l,c,d,h,p,f,_,m,v;(o=function(){this.init()}).prototype={init:function(){var e=this||i;return e._counter=1e3,e._html5AudioPool=[],e.html5PoolSize=10,e._codecs={},e._howls=[],e._muted=!1,e._volume=1,e._canPlayEvent="canplaythrough",e._navigator="undefined"!=typeof window&&window.navigator?window.navigator:null,e.masterGain=null,e.noAudio=!1,e.usingWebAudio=!0,e.autoSuspend=!0,e.ctx=null,e.autoUnlock=!0,e._setup(),e},volume:function(e){var t=this||i;if(e=parseFloat(e),t.ctx||p(),void 0!==e&&e>=0&&e<=1){if(t._volume=e,t._muted)return t;t.usingWebAudio&&t.masterGain.gain.setValueAtTime(e,i.ctx.currentTime);for(var n=0;n<t._howls.length;n++)if(!t._howls[n]._webAudio)for(var r=t._howls[n]._getSoundIds(),o=0;o<r.length;o++){var s=t._howls[n]._soundById(r[o]);s&&s._node&&(s._node.volume=s._volume*e)}return t}return t._volume},mute:function(e){var t=this||i;t.ctx||p(),t._muted=e,t.usingWebAudio&&t.masterGain.gain.setValueAtTime(e?0:t._volume,i.ctx.currentTime);for(var n=0;n<t._howls.length;n++)if(!t._howls[n]._webAudio)for(var r=t._howls[n]._getSoundIds(),o=0;o<r.length;o++){var s=t._howls[n]._soundById(r[o]);s&&s._node&&(s._node.muted=!!e||s._muted)}return t},stop:function(){for(var e=this||i,t=0;t<e._howls.length;t++)e._howls[t].stop();return e},unload:function(){for(var e=this||i,t=e._howls.length-1;t>=0;t--)e._howls[t].unload();return e.usingWebAudio&&e.ctx&&void 0!==e.ctx.close&&(e.ctx.close(),e.ctx=null,p()),e},codecs:function(e){return(this||i)._codecs[e.replace(/^x-/,"")]},_setup:function(){var e=this||i;if(e.state=e.ctx&&e.ctx.state||"suspended",e._autoSuspend(),!e.usingWebAudio){if("undefined"!=typeof Audio)try{var t=new Audio;void 0===t.oncanplaythrough&&(e._canPlayEvent="canplay")}catch(t){e.noAudio=!0}else e.noAudio=!0}try{var t=new Audio;t.muted&&(e.noAudio=!0)}catch(e){}return e.noAudio||e._setupCodecs(),e},_setupCodecs:function(){var e=this||i,t=null;try{t="undefined"!=typeof Audio?new Audio:null}catch(t){return e}if(!t||"function"!=typeof t.canPlayType)return e;var n=t.canPlayType("audio/mpeg;").replace(/^no$/,""),r=e._navigator?e._navigator.userAgent:"",o=r.match(/OPR\/(\d+)/g),s=o&&33>parseInt(o[0].split("/")[1],10),a=-1!==r.indexOf("Safari")&&-1===r.indexOf("Chrome"),u=r.match(/Version\/(.*?) /),l=a&&u&&15>parseInt(u[1],10);return e._codecs={mp3:!!(!s&&(n||t.canPlayType("audio/mp3;").replace(/^no$/,""))),mpeg:!!n,opus:!!t.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!t.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!t.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),wav:!!(t.canPlayType('audio/wav; codecs="1"')||t.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!t.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!t.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(t.canPlayType("audio/x-m4a;")||t.canPlayType("audio/m4a;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(t.canPlayType("audio/x-m4b;")||t.canPlayType("audio/m4b;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(t.canPlayType("audio/x-mp4;")||t.canPlayType("audio/mp4;")||t.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!!(!l&&t.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!!(!l&&t.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!t.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(t.canPlayType("audio/x-flac;")||t.canPlayType("audio/flac;")).replace(/^no$/,"")},e},_unlockAudio:function(){var e=this||i;if(!e._audioUnlocked&&e.ctx){e._audioUnlocked=!1,e.autoUnlock=!1,e._mobileUnloaded||44100===e.ctx.sampleRate||(e._mobileUnloaded=!0,e.unload()),e._scratchBuffer=e.ctx.createBuffer(1,1,22050);var t=function(n){for(;e._html5AudioPool.length<e.html5PoolSize;)try{var r=new Audio;r._unlocked=!0,e._releaseHtml5Audio(r)}catch(t){e.noAudio=!0;break}for(var o=0;o<e._howls.length;o++)if(!e._howls[o]._webAudio)for(var i=e._howls[o]._getSoundIds(),s=0;s<i.length;s++){var a=e._howls[o]._soundById(i[s]);a&&a._node&&!a._node._unlocked&&(a._node._unlocked=!0,a._node.load())}e._autoResume();var u=e.ctx.createBufferSource();u.buffer=e._scratchBuffer,u.connect(e.ctx.destination),void 0===u.start?u.noteOn(0):u.start(0),"function"==typeof e.ctx.resume&&e.ctx.resume(),u.onended=function(){u.disconnect(0),e._audioUnlocked=!0,document.removeEventListener("touchstart",t,!0),document.removeEventListener("touchend",t,!0),document.removeEventListener("click",t,!0),document.removeEventListener("keydown",t,!0);for(var n=0;n<e._howls.length;n++)e._howls[n]._emit("unlock")}};return document.addEventListener("touchstart",t,!0),document.addEventListener("touchend",t,!0),document.addEventListener("click",t,!0),document.addEventListener("keydown",t,!0),e}},_obtainHtml5Audio:function(){var e=this||i;if(e._html5AudioPool.length)return e._html5AudioPool.pop();var t=new Audio().play();return t&&"undefined"!=typeof Promise&&(t instanceof Promise||"function"==typeof t.then)&&t.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")}),new Audio},_releaseHtml5Audio:function(e){var t=this||i;return e._unlocked&&t._html5AudioPool.push(e),t},_autoSuspend:function(){var e=this;if(e.autoSuspend&&e.ctx&&void 0!==e.ctx.suspend&&i.usingWebAudio){for(var t=0;t<e._howls.length;t++)if(e._howls[t]._webAudio){for(var n=0;n<e._howls[t]._sounds.length;n++)if(!e._howls[t]._sounds[n]._paused)return e}return e._suspendTimer&&clearTimeout(e._suspendTimer),e._suspendTimer=setTimeout(function(){if(e.autoSuspend){e._suspendTimer=null,e.state="suspending";var t=function(){e.state="suspended",e._resumeAfterSuspend&&(delete e._resumeAfterSuspend,e._autoResume())};e.ctx.suspend().then(t,t)}},3e4),e}},_autoResume:function(){var e=this;if(e.ctx&&void 0!==e.ctx.resume&&i.usingWebAudio)return"running"===e.state&&"interrupted"!==e.ctx.state&&e._suspendTimer?(clearTimeout(e._suspendTimer),e._suspendTimer=null):"suspended"===e.state||"running"===e.state&&"interrupted"===e.ctx.state?(e.ctx.resume().then(function(){e.state="running";for(var t=0;t<e._howls.length;t++)e._howls[t]._emit("resume")}),e._suspendTimer&&(clearTimeout(e._suspendTimer),e._suspendTimer=null)):"suspending"===e.state&&(e._resumeAfterSuspend=!0),e}},i=new o,(s=function(e){if(!e.src||0===e.src.length){console.error("An array of source files must be passed with any new Howl.");return}this.init(e)}).prototype={init:function(e){var t=this;return i.ctx||p(),t._autoplay=e.autoplay||!1,t._format="string"!=typeof e.format?e.format:[e.format],t._html5=e.html5||!1,t._muted=e.mute||!1,t._loop=e.loop||!1,t._pool=e.pool||5,t._preload="boolean"!=typeof e.preload&&"metadata"!==e.preload||e.preload,t._rate=e.rate||1,t._sprite=e.sprite||{},t._src="string"!=typeof e.src?e.src:[e.src],t._volume=void 0!==e.volume?e.volume:1,t._xhr={method:e.xhr&&e.xhr.method?e.xhr.method:"GET",headers:e.xhr&&e.xhr.headers?e.xhr.headers:null,withCredentials:!!e.xhr&&!!e.xhr.withCredentials&&e.xhr.withCredentials},t._duration=0,t._state="unloaded",t._sounds=[],t._endTimers={},t._queue=[],t._playLock=!1,t._onend=e.onend?[{fn:e.onend}]:[],t._onfade=e.onfade?[{fn:e.onfade}]:[],t._onload=e.onload?[{fn:e.onload}]:[],t._onloaderror=e.onloaderror?[{fn:e.onloaderror}]:[],t._onplayerror=e.onplayerror?[{fn:e.onplayerror}]:[],t._onpause=e.onpause?[{fn:e.onpause}]:[],t._onplay=e.onplay?[{fn:e.onplay}]:[],t._onstop=e.onstop?[{fn:e.onstop}]:[],t._onmute=e.onmute?[{fn:e.onmute}]:[],t._onvolume=e.onvolume?[{fn:e.onvolume}]:[],t._onrate=e.onrate?[{fn:e.onrate}]:[],t._onseek=e.onseek?[{fn:e.onseek}]:[],t._onunlock=e.onunlock?[{fn:e.onunlock}]:[],t._onresume=[],t._webAudio=i.usingWebAudio&&!t._html5,void 0!==i.ctx&&i.ctx&&i.autoUnlock&&i._unlockAudio(),i._howls.push(t),t._autoplay&&t._queue.push({event:"play",action:function(){t.play()}}),t._preload&&"none"!==t._preload&&t.load(),t},load:function(){var e,t,n=null;if(i.noAudio){this._emit("loaderror",null,"No audio support.");return}"string"==typeof this._src&&(this._src=[this._src]);for(var r=0;r<this._src.length;r++){if(this._format&&this._format[r])e=this._format[r];else{if("string"!=typeof(t=this._src[r])){this._emit("loaderror",null,"Non-string found in selected audio sources - ignoring.");continue}(e=/^data:audio\/([^;,]+);/i.exec(t))||(e=/\.([^.]+)$/.exec(t.split("?",1)[0])),e&&(e=e[1].toLowerCase())}if(e||console.warn('No file extension was found. Consider using the "format" property or specify an extension.'),e&&i.codecs(e)){n=this._src[r];break}}if(!n){this._emit("loaderror",null,"No codec support for selected audio sources.");return}return this._src=n,this._state="loading","https:"===window.location.protocol&&"http:"===n.slice(0,5)&&(this._html5=!0,this._webAudio=!1),new a(this),this._webAudio&&l(this),this},play:function(e,t){var n=this,r=null;if("number"==typeof e)r=e,e=null;else if("string"==typeof e&&"loaded"===n._state&&!n._sprite[e])return null;else if(void 0===e&&(e="__default",!n._playLock)){for(var o=0,s=0;s<n._sounds.length;s++)n._sounds[s]._paused&&!n._sounds[s]._ended&&(o++,r=n._sounds[s]._id);1===o?e=null:r=null}var a=r?n._soundById(r):n._inactiveSound();if(!a)return null;if(r&&!e&&(e=a._sprite||"__default"),"loaded"!==n._state){a._sprite=e,a._ended=!1;var u=a._id;return n._queue.push({event:"play",action:function(){n.play(u)}}),u}if(r&&!a._paused)return t||n._loadQueue("play"),a._id;n._webAudio&&i._autoResume();var l=Math.max(0,a._seek>0?a._seek:n._sprite[e][0]/1e3),c=Math.max(0,(n._sprite[e][0]+n._sprite[e][1])/1e3-l),d=1e3*c/Math.abs(a._rate),h=n._sprite[e][0]/1e3,p=(n._sprite[e][0]+n._sprite[e][1])/1e3;a._sprite=e,a._ended=!1;var f=function(){a._paused=!1,a._seek=l,a._start=h,a._stop=p,a._loop=!!(a._loop||n._sprite[e][2])};if(l>=p){n._ended(a);return}var _=a._node;if(n._webAudio){var m=function(){n._playLock=!1,f(),n._refreshBuffer(a);var e=a._muted||n._muted?0:a._volume;_.gain.setValueAtTime(e,i.ctx.currentTime),a._playStart=i.ctx.currentTime,void 0===_.bufferSource.start?a._loop?_.bufferSource.noteGrainOn(0,l,86400):_.bufferSource.noteGrainOn(0,l,c):a._loop?_.bufferSource.start(0,l,86400):_.bufferSource.start(0,l,c),d!==1/0&&(n._endTimers[a._id]=setTimeout(n._ended.bind(n,a),d)),t||setTimeout(function(){n._emit("play",a._id),n._loadQueue()},0)};"running"===i.state&&"interrupted"!==i.ctx.state?m():(n._playLock=!0,n.once("resume",m),n._clearTimer(a._id))}else{var v=function(){_.currentTime=l,_.muted=a._muted||n._muted||i._muted||_.muted,_.volume=a._volume*i.volume(),_.playbackRate=a._rate;try{var r=_.play();if(r&&"undefined"!=typeof Promise&&(r instanceof Promise||"function"==typeof r.then)?(n._playLock=!0,f(),r.then(function(){n._playLock=!1,_._unlocked=!0,t?n._loadQueue():n._emit("play",a._id)}).catch(function(){n._playLock=!1,n._emit("playerror",a._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."),a._ended=!0,a._paused=!0})):t||(n._playLock=!1,f(),n._emit("play",a._id)),_.playbackRate=a._rate,_.paused){n._emit("playerror",a._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");return}"__default"!==e||a._loop?n._endTimers[a._id]=setTimeout(n._ended.bind(n,a),d):(n._endTimers[a._id]=function(){n._ended(a),_.removeEventListener("ended",n._endTimers[a._id],!1)},_.addEventListener("ended",n._endTimers[a._id],!1))}catch(e){n._emit("playerror",a._id,e)}};"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"===_.src&&(_.src=n._src,_.load());var y=window&&window.ejecta||!_.readyState&&i._navigator.isCocoonJS;if(_.readyState>=3||y)v();else{n._playLock=!0,n._state="loading";var g=function(){n._state="loaded",v(),_.removeEventListener(i._canPlayEvent,g,!1)};_.addEventListener(i._canPlayEvent,g,!1),n._clearTimer(a._id)}}return a._id},pause:function(e){var t=this;if("loaded"!==t._state||t._playLock)return t._queue.push({event:"pause",action:function(){t.pause(e)}}),t;for(var n=t._getSoundIds(e),r=0;r<n.length;r++){t._clearTimer(n[r]);var o=t._soundById(n[r]);if(o&&!o._paused&&(o._seek=t.seek(n[r]),o._rateSeek=0,o._paused=!0,t._stopFade(n[r]),o._node)){if(t._webAudio){if(!o._node.bufferSource)continue;void 0===o._node.bufferSource.stop?o._node.bufferSource.noteOff(0):o._node.bufferSource.stop(0),t._cleanBuffer(o._node)}else isNaN(o._node.duration)&&o._node.duration!==1/0||o._node.pause()}arguments[1]||t._emit("pause",o?o._id:null)}return t},stop:function(e,t){var n=this;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"stop",action:function(){n.stop(e)}}),n;for(var r=n._getSoundIds(e),o=0;o<r.length;o++){n._clearTimer(r[o]);var i=n._soundById(r[o]);i&&(i._seek=i._start||0,i._rateSeek=0,i._paused=!0,i._ended=!0,n._stopFade(r[o]),i._node&&(n._webAudio?i._node.bufferSource&&(void 0===i._node.bufferSource.stop?i._node.bufferSource.noteOff(0):i._node.bufferSource.stop(0),n._cleanBuffer(i._node)):isNaN(i._node.duration)&&i._node.duration!==1/0||(i._node.currentTime=i._start||0,i._node.pause(),i._node.duration===1/0&&n._clearSound(i._node))),t||n._emit("stop",i._id))}return n},mute:function(e,t){var n=this;if("loaded"!==n._state||n._playLock)return n._queue.push({event:"mute",action:function(){n.mute(e,t)}}),n;if(void 0===t){if("boolean"!=typeof e)return n._muted;n._muted=e}for(var r=n._getSoundIds(t),o=0;o<r.length;o++){var s=n._soundById(r[o]);s&&(s._muted=e,s._interval&&n._stopFade(s._id),n._webAudio&&s._node?s._node.gain.setValueAtTime(e?0:s._volume,i.ctx.currentTime):s._node&&(s._node.muted=!!i._muted||e),n._emit("mute",s._id))}return n},volume:function(){var e,t,n,r=this,o=arguments;if(0===o.length)return r._volume;if(1===o.length||2===o.length&&void 0===o[1]?r._getSoundIds().indexOf(o[0])>=0?t=parseInt(o[0],10):e=parseFloat(o[0]):o.length>=2&&(e=parseFloat(o[0]),t=parseInt(o[1],10)),void 0===e||!(e>=0)||!(e<=1))return(n=t?r._soundById(t):r._sounds[0])?n._volume:0;if("loaded"!==r._state||r._playLock)return r._queue.push({event:"volume",action:function(){r.volume.apply(r,o)}}),r;void 0===t&&(r._volume=e),t=r._getSoundIds(t);for(var s=0;s<t.length;s++)(n=r._soundById(t[s]))&&(n._volume=e,o[2]||r._stopFade(t[s]),r._webAudio&&n._node&&!n._muted?n._node.gain.setValueAtTime(e,i.ctx.currentTime):n._node&&!n._muted&&(n._node.volume=e*i.volume()),r._emit("volume",n._id));return r},fade:function(e,t,n,r){var o=this;if("loaded"!==o._state||o._playLock)return o._queue.push({event:"fade",action:function(){o.fade(e,t,n,r)}}),o;e=Math.min(Math.max(0,parseFloat(e)),1),t=Math.min(Math.max(0,parseFloat(t)),1),n=parseFloat(n),o.volume(e,r);for(var s=o._getSoundIds(r),a=0;a<s.length;a++){var u=o._soundById(s[a]);if(u){if(r||o._stopFade(s[a]),o._webAudio&&!u._muted){var l=i.ctx.currentTime,c=l+n/1e3;u._volume=e,u._node.gain.setValueAtTime(e,l),u._node.gain.linearRampToValueAtTime(t,c)}o._startFadeInterval(u,e,t,n,s[a],void 0===r)}}return o},_startFadeInterval:function(e,t,n,r,o,i){var s=this,a=t,u=n-t,l=Math.abs(u/.01),c=Date.now();e._fadeTo=n,e._interval=setInterval(function(){var o=(Date.now()-c)/r;c=Date.now(),a+=u*o,a=Math.round(100*a)/100,a=u<0?Math.max(n,a):Math.min(n,a),s._webAudio?e._volume=a:s.volume(a,e._id,!0),i&&(s._volume=a),(n<t&&a<=n||n>t&&a>=n)&&(clearInterval(e._interval),e._interval=null,e._fadeTo=null,s.volume(n,e._id),s._emit("fade",e._id))},Math.max(4,l>0?r/l:r))},_stopFade:function(e){var t=this._soundById(e);return t&&t._interval&&(this._webAudio&&t._node.gain.cancelScheduledValues(i.ctx.currentTime),clearInterval(t._interval),t._interval=null,this.volume(t._fadeTo,e),t._fadeTo=null,this._emit("fade",e)),this},loop:function(){var e,t,n,r=arguments;if(0===r.length)return this._loop;if(1===r.length){if("boolean"!=typeof r[0])return!!(n=this._soundById(parseInt(r[0],10)))&&n._loop;e=r[0],this._loop=e}else 2===r.length&&(e=r[0],t=parseInt(r[1],10));for(var o=this._getSoundIds(t),i=0;i<o.length;i++)(n=this._soundById(o[i]))&&(n._loop=e,this._webAudio&&n._node&&n._node.bufferSource&&(n._node.bufferSource.loop=e,e&&(n._node.bufferSource.loopStart=n._start||0,n._node.bufferSource.loopEnd=n._stop,this.playing(o[i])&&(this.pause(o[i],!0),this.play(o[i],!0)))));return this},rate:function(){var e,t,n,r=this,o=arguments;if(0===o.length?t=r._sounds[0]._id:1===o.length?r._getSoundIds().indexOf(o[0])>=0?t=parseInt(o[0],10):e=parseFloat(o[0]):2===o.length&&(e=parseFloat(o[0]),t=parseInt(o[1],10)),"number"!=typeof e)return(n=r._soundById(t))?n._rate:r._rate;if("loaded"!==r._state||r._playLock)return r._queue.push({event:"rate",action:function(){r.rate.apply(r,o)}}),r;void 0===t&&(r._rate=e),t=r._getSoundIds(t);for(var s=0;s<t.length;s++)if(n=r._soundById(t[s])){r.playing(t[s])&&(n._rateSeek=r.seek(t[s]),n._playStart=r._webAudio?i.ctx.currentTime:n._playStart),n._rate=e,r._webAudio&&n._node&&n._node.bufferSource?n._node.bufferSource.playbackRate.setValueAtTime(e,i.ctx.currentTime):n._node&&(n._node.playbackRate=e);var a=r.seek(t[s]),u=1e3*((r._sprite[n._sprite][0]+r._sprite[n._sprite][1])/1e3-a)/Math.abs(n._rate);(r._endTimers[t[s]]||!n._paused)&&(r._clearTimer(t[s]),r._endTimers[t[s]]=setTimeout(r._ended.bind(r,n),u)),r._emit("rate",n._id)}return r},seek:function(){var e,t,n=this,r=arguments;if(0===r.length?n._sounds.length&&(t=n._sounds[0]._id):1===r.length?n._getSoundIds().indexOf(r[0])>=0?t=parseInt(r[0],10):n._sounds.length&&(t=n._sounds[0]._id,e=parseFloat(r[0])):2===r.length&&(e=parseFloat(r[0]),t=parseInt(r[1],10)),void 0===t)return 0;if("number"==typeof e&&("loaded"!==n._state||n._playLock))return n._queue.push({event:"seek",action:function(){n.seek.apply(n,r)}}),n;var o=n._soundById(t);if(o){if("number"==typeof e&&e>=0){var s=n.playing(t);s&&n.pause(t,!0),o._seek=e,o._ended=!1,n._clearTimer(t),n._webAudio||!o._node||isNaN(o._node.duration)||(o._node.currentTime=e);var a=function(){s&&n.play(t,!0),n._emit("seek",t)};if(s&&!n._webAudio){var u=function(){n._playLock?setTimeout(u,0):a()};setTimeout(u,0)}else a()}else{if(!n._webAudio)return o._node.currentTime;var l=n.playing(t)?i.ctx.currentTime-o._playStart:0,c=o._rateSeek?o._rateSeek-o._seek:0;return o._seek+(c+l*Math.abs(o._rate))}}return n},playing:function(e){if("number"==typeof e){var t=this._soundById(e);return!!t&&!t._paused}for(var n=0;n<this._sounds.length;n++)if(!this._sounds[n]._paused)return!0;return!1},duration:function(e){var t=this._duration,n=this._soundById(e);return n&&(t=this._sprite[n._sprite][1]/1e3),t},state:function(){return this._state},unload:function(){for(var e=this,t=e._sounds,n=0;n<t.length;n++)t[n]._paused||e.stop(t[n]._id),e._webAudio||(e._clearSound(t[n]._node),t[n]._node.removeEventListener("error",t[n]._errorFn,!1),t[n]._node.removeEventListener(i._canPlayEvent,t[n]._loadFn,!1),t[n]._node.removeEventListener("ended",t[n]._endFn,!1),i._releaseHtml5Audio(t[n]._node)),delete t[n]._node,e._clearTimer(t[n]._id);var r=i._howls.indexOf(e);r>=0&&i._howls.splice(r,1);var o=!0;for(n=0;n<i._howls.length;n++)if(i._howls[n]._src===e._src||e._src.indexOf(i._howls[n]._src)>=0){o=!1;break}return u&&o&&delete u[e._src],i.noAudio=!1,e._state="unloaded",e._sounds=[],e=null,null},on:function(e,t,n,r){var o=this["_on"+e];return"function"==typeof t&&o.push(r?{id:n,fn:t,once:r}:{id:n,fn:t}),this},off:function(e,t,n){var r=this["_on"+e],o=0;if("number"==typeof t&&(n=t,t=null),t||n)for(o=0;o<r.length;o++){var i=n===r[o].id;if(t===r[o].fn&&i||!t&&i){r.splice(o,1);break}}else if(e)this["_on"+e]=[];else{var s=Object.keys(this);for(o=0;o<s.length;o++)0===s[o].indexOf("_on")&&Array.isArray(this[s[o]])&&(this[s[o]]=[])}return this},once:function(e,t,n){return this.on(e,t,n,1),this},_emit:function(e,t,n){for(var r=this["_on"+e],o=r.length-1;o>=0;o--)(!r[o].id||r[o].id===t||"load"===e)&&(setTimeout((function(e){e.call(this,t,n)}).bind(this,r[o].fn),0),r[o].once&&this.off(e,r[o].fn,r[o].id));return this._loadQueue(e),this},_loadQueue:function(e){if(this._queue.length>0){var t=this._queue[0];t.event===e&&(this._queue.shift(),this._loadQueue()),e||t.action()}return this},_ended:function(e){var t=e._sprite;if(!this._webAudio&&e._node&&!e._node.paused&&!e._node.ended&&e._node.currentTime<e._stop)return setTimeout(this._ended.bind(this,e),100),this;var n=!!(e._loop||this._sprite[t][2]);if(this._emit("end",e._id),!this._webAudio&&n&&this.stop(e._id,!0).play(e._id),this._webAudio&&n){this._emit("play",e._id),e._seek=e._start||0,e._rateSeek=0,e._playStart=i.ctx.currentTime;var r=(e._stop-e._start)*1e3/Math.abs(e._rate);this._endTimers[e._id]=setTimeout(this._ended.bind(this,e),r)}return this._webAudio&&!n&&(e._paused=!0,e._ended=!0,e._seek=e._start||0,e._rateSeek=0,this._clearTimer(e._id),this._cleanBuffer(e._node),i._autoSuspend()),this._webAudio||n||this.stop(e._id,!0),this},_clearTimer:function(e){if(this._endTimers[e]){if("function"!=typeof this._endTimers[e])clearTimeout(this._endTimers[e]);else{var t=this._soundById(e);t&&t._node&&t._node.removeEventListener("ended",this._endTimers[e],!1)}delete this._endTimers[e]}return this},_soundById:function(e){for(var t=0;t<this._sounds.length;t++)if(e===this._sounds[t]._id)return this._sounds[t];return null},_inactiveSound:function(){this._drain();for(var e=0;e<this._sounds.length;e++)if(this._sounds[e]._ended)return this._sounds[e].reset();return new a(this)},_drain:function(){var e=this._pool,t=0,n=0;if(!(this._sounds.length<e)){for(n=0;n<this._sounds.length;n++)this._sounds[n]._ended&&t++;for(n=this._sounds.length-1;n>=0;n--){if(t<=e)return;this._sounds[n]._ended&&(this._webAudio&&this._sounds[n]._node&&this._sounds[n]._node.disconnect(0),this._sounds.splice(n,1),t--)}}},_getSoundIds:function(e){if(void 0!==e)return[e];for(var t=[],n=0;n<this._sounds.length;n++)t.push(this._sounds[n]._id);return t},_refreshBuffer:function(e){return e._node.bufferSource=i.ctx.createBufferSource(),e._node.bufferSource.buffer=u[this._src],e._panner?e._node.bufferSource.connect(e._panner):e._node.bufferSource.connect(e._node),e._node.bufferSource.loop=e._loop,e._loop&&(e._node.bufferSource.loopStart=e._start||0,e._node.bufferSource.loopEnd=e._stop||0),e._node.bufferSource.playbackRate.setValueAtTime(e._rate,i.ctx.currentTime),this},_cleanBuffer:function(e){var t=i._navigator&&i._navigator.vendor.indexOf("Apple")>=0;if(!e.bufferSource)return this;if(i._scratchBuffer&&e.bufferSource&&(e.bufferSource.onended=null,e.bufferSource.disconnect(0),t))try{e.bufferSource.buffer=i._scratchBuffer}catch(e){}return e.bufferSource=null,this},_clearSound:function(e){/MSIE |Trident\//.test(i._navigator&&i._navigator.userAgent)||(e.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}},(a=function(e){this._parent=e,this.init()}).prototype={init:function(){var e=this._parent;return this._muted=e._muted,this._loop=e._loop,this._volume=e._volume,this._rate=e._rate,this._seek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++i._counter,e._sounds.push(this),this.create(),this},create:function(){var e=this._parent,t=i._muted||this._muted||this._parent._muted?0:this._volume;return e._webAudio?(this._node=void 0===i.ctx.createGain?i.ctx.createGainNode():i.ctx.createGain(),this._node.gain.setValueAtTime(t,i.ctx.currentTime),this._node.paused=!0,this._node.connect(i.masterGain)):i.noAudio||(this._node=i._obtainHtml5Audio(),this._errorFn=this._errorListener.bind(this),this._node.addEventListener("error",this._errorFn,!1),this._loadFn=this._loadListener.bind(this),this._node.addEventListener(i._canPlayEvent,this._loadFn,!1),this._endFn=this._endListener.bind(this),this._node.addEventListener("ended",this._endFn,!1),this._node.src=e._src,this._node.preload=!0===e._preload?"auto":e._preload,this._node.volume=t*i.volume(),this._node.load()),this},reset:function(){var e=this._parent;return this._muted=e._muted,this._loop=e._loop,this._volume=e._volume,this._rate=e._rate,this._seek=0,this._rateSeek=0,this._paused=!0,this._ended=!0,this._sprite="__default",this._id=++i._counter,this},_errorListener:function(){this._parent._emit("loaderror",this._id,this._node.error?this._node.error.code:0),this._node.removeEventListener("error",this._errorFn,!1)},_loadListener:function(){var e=this._parent;e._duration=Math.ceil(10*this._node.duration)/10,0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),"loaded"!==e._state&&(e._state="loaded",e._emit("load"),e._loadQueue()),this._node.removeEventListener(i._canPlayEvent,this._loadFn,!1)},_endListener:function(){var e=this._parent;e._duration===1/0&&(e._duration=Math.ceil(10*this._node.duration)/10,e._sprite.__default[1]===1/0&&(e._sprite.__default[1]=1e3*e._duration),e._ended(this)),this._node.removeEventListener("ended",this._endFn,!1)}},u={},l=function(e){var t=e._src;if(u[t]){e._duration=u[t].duration,h(e);return}if(/^data:[^;]+;base64,/.test(t)){for(var n=atob(t.split(",")[1]),r=new Uint8Array(n.length),o=0;o<n.length;++o)r[o]=n.charCodeAt(o);d(r.buffer,e)}else{var i=new XMLHttpRequest;i.open(e._xhr.method,t,!0),i.withCredentials=e._xhr.withCredentials,i.responseType="arraybuffer",e._xhr.headers&&Object.keys(e._xhr.headers).forEach(function(t){i.setRequestHeader(t,e._xhr.headers[t])}),i.onload=function(){var t=(i.status+"")[0];if("0"!==t&&"2"!==t&&"3"!==t){e._emit("loaderror",null,"Failed loading audio file with status: "+i.status+".");return}d(i.response,e)},i.onerror=function(){e._webAudio&&(e._html5=!0,e._webAudio=!1,e._sounds=[],delete u[t],e.load())},c(i)}},c=function(e){try{e.send()}catch(t){e.onerror()}},d=function(e,t){var n=function(){t._emit("loaderror",null,"Decoding audio data failed.")},r=function(e){e&&t._sounds.length>0?(u[t._src]=e,h(t,e)):n()};"undefined"!=typeof Promise&&1===i.ctx.decodeAudioData.length?i.ctx.decodeAudioData(e).then(r).catch(n):i.ctx.decodeAudioData(e,r,n)},h=function(e,t){t&&!e._duration&&(e._duration=t.duration),0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),"loaded"!==e._state&&(e._state="loaded",e._emit("load"),e._loadQueue())},p=function(){if(i.usingWebAudio){try{"undefined"!=typeof AudioContext?i.ctx=new AudioContext:"undefined"!=typeof webkitAudioContext?i.ctx=new webkitAudioContext:i.usingWebAudio=!1}catch(e){i.usingWebAudio=!1}i.ctx||(i.usingWebAudio=!1);var e=/iP(hone|od|ad)/.test(i._navigator&&i._navigator.platform),t=i._navigator&&i._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),n=t?parseInt(t[1],10):null;if(e&&n&&n<9){var r=/safari/.test(i._navigator&&i._navigator.userAgent.toLowerCase());i._navigator&&!r&&(i.usingWebAudio=!1)}i.usingWebAudio&&(i.masterGain=void 0===i.ctx.createGain?i.ctx.createGainNode():i.ctx.createGain(),i.masterGain.gain.setValueAtTime(i._muted?0:i._volume,i.ctx.currentTime),i.masterGain.connect(i.ctx.destination)),i._setup()}},void 0!==(r=(function(){return{Howler:i,Howl:s}}).apply(t,[]))&&(e.exports=r),t.Howler=i,t.Howl=s,void 0!==n.g?(n.g.HowlerGlobal=o,n.g.Howler=i,n.g.Howl=s,n.g.Sound=a):"undefined"!=typeof window&&(window.HowlerGlobal=o,window.Howler=i,window.Howl=s,window.Sound=a),HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(e){if(!this.ctx||!this.ctx.listener)return this;for(var t=this._howls.length-1;t>=0;t--)this._howls[t].stereo(e);return this},HowlerGlobal.prototype.pos=function(e,t,n){return this.ctx&&this.ctx.listener?(t="number"!=typeof t?this._pos[1]:t,n="number"!=typeof n?this._pos[2]:n,"number"!=typeof e)?this._pos:(this._pos=[e,t,n],void 0!==this.ctx.listener.positionX?(this.ctx.listener.positionX.setTargetAtTime(this._pos[0],Howler.ctx.currentTime,.1),this.ctx.listener.positionY.setTargetAtTime(this._pos[1],Howler.ctx.currentTime,.1),this.ctx.listener.positionZ.setTargetAtTime(this._pos[2],Howler.ctx.currentTime,.1)):this.ctx.listener.setPosition(this._pos[0],this._pos[1],this._pos[2]),this):this},HowlerGlobal.prototype.orientation=function(e,t,n,r,o,i){if(!this.ctx||!this.ctx.listener)return this;var s=this._orientation;return(t="number"!=typeof t?s[1]:t,n="number"!=typeof n?s[2]:n,r="number"!=typeof r?s[3]:r,o="number"!=typeof o?s[4]:o,i="number"!=typeof i?s[5]:i,"number"!=typeof e)?s:(this._orientation=[e,t,n,r,o,i],void 0!==this.ctx.listener.forwardX?(this.ctx.listener.forwardX.setTargetAtTime(e,Howler.ctx.currentTime,.1),this.ctx.listener.forwardY.setTargetAtTime(t,Howler.ctx.currentTime,.1),this.ctx.listener.forwardZ.setTargetAtTime(n,Howler.ctx.currentTime,.1),this.ctx.listener.upX.setTargetAtTime(r,Howler.ctx.currentTime,.1),this.ctx.listener.upY.setTargetAtTime(o,Howler.ctx.currentTime,.1),this.ctx.listener.upZ.setTargetAtTime(i,Howler.ctx.currentTime,.1)):this.ctx.listener.setOrientation(e,t,n,r,o,i),this)},Howl.prototype.init=(f=Howl.prototype.init,function(e){return this._orientation=e.orientation||[1,0,0],this._stereo=e.stereo||null,this._pos=e.pos||null,this._pannerAttr={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:360,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:360,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:0,distanceModel:void 0!==e.distanceModel?e.distanceModel:"inverse",maxDistance:void 0!==e.maxDistance?e.maxDistance:1e4,panningModel:void 0!==e.panningModel?e.panningModel:"HRTF",refDistance:void 0!==e.refDistance?e.refDistance:1,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:1},this._onstereo=e.onstereo?[{fn:e.onstereo}]:[],this._onpos=e.onpos?[{fn:e.onpos}]:[],this._onorientation=e.onorientation?[{fn:e.onorientation}]:[],f.call(this,e)}),Howl.prototype.stereo=function(e,t){var n=this;if(!n._webAudio)return n;if("loaded"!==n._state)return n._queue.push({event:"stereo",action:function(){n.stereo(e,t)}}),n;var r=void 0===Howler.ctx.createStereoPanner?"spatial":"stereo";if(void 0===t){if("number"!=typeof e)return n._stereo;n._stereo=e,n._pos=[e,0,0]}for(var o=n._getSoundIds(t),i=0;i<o.length;i++){var s=n._soundById(o[i]);if(s){if("number"!=typeof e)return s._stereo;s._stereo=e,s._pos=[e,0,0],s._node&&(s._pannerAttr.panningModel="equalpower",s._panner&&s._panner.pan||v(s,r),"spatial"===r?void 0!==s._panner.positionX?(s._panner.positionX.setValueAtTime(e,Howler.ctx.currentTime),s._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),s._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):s._panner.setPosition(e,0,0):s._panner.pan.setValueAtTime(e,Howler.ctx.currentTime)),n._emit("stereo",s._id)}}return n},Howl.prototype.pos=function(e,t,n,r){var o=this;if(!o._webAudio)return o;if("loaded"!==o._state)return o._queue.push({event:"pos",action:function(){o.pos(e,t,n,r)}}),o;if(t="number"!=typeof t?0:t,n="number"!=typeof n?-.5:n,void 0===r){if("number"!=typeof e)return o._pos;o._pos=[e,t,n]}for(var i=o._getSoundIds(r),s=0;s<i.length;s++){var a=o._soundById(i[s]);if(a){if("number"!=typeof e)return a._pos;a._pos=[e,t,n],a._node&&((!a._panner||a._panner.pan)&&v(a,"spatial"),void 0!==a._panner.positionX?(a._panner.positionX.setValueAtTime(e,Howler.ctx.currentTime),a._panner.positionY.setValueAtTime(t,Howler.ctx.currentTime),a._panner.positionZ.setValueAtTime(n,Howler.ctx.currentTime)):a._panner.setPosition(e,t,n)),o._emit("pos",a._id)}}return o},Howl.prototype.orientation=function(e,t,n,r){var o=this;if(!o._webAudio)return o;if("loaded"!==o._state)return o._queue.push({event:"orientation",action:function(){o.orientation(e,t,n,r)}}),o;if(t="number"!=typeof t?o._orientation[1]:t,n="number"!=typeof n?o._orientation[2]:n,void 0===r){if("number"!=typeof e)return o._orientation;o._orientation=[e,t,n]}for(var i=o._getSoundIds(r),s=0;s<i.length;s++){var a=o._soundById(i[s]);if(a){if("number"!=typeof e)return a._orientation;a._orientation=[e,t,n],a._node&&(a._panner||(a._pos||(a._pos=o._pos||[0,0,-.5]),v(a,"spatial")),void 0!==a._panner.orientationX?(a._panner.orientationX.setValueAtTime(e,Howler.ctx.currentTime),a._panner.orientationY.setValueAtTime(t,Howler.ctx.currentTime),a._panner.orientationZ.setValueAtTime(n,Howler.ctx.currentTime)):a._panner.setOrientation(e,t,n)),o._emit("orientation",a._id)}}return o},Howl.prototype.pannerAttr=function(){var e,t,n,r=arguments;if(!this._webAudio)return this;if(0===r.length)return this._pannerAttr;if(1===r.length){if("object"!=typeof r[0])return(n=this._soundById(parseInt(r[0],10)))?n._pannerAttr:this._pannerAttr;e=r[0],void 0===t&&(e.pannerAttr||(e.pannerAttr={coneInnerAngle:e.coneInnerAngle,coneOuterAngle:e.coneOuterAngle,coneOuterGain:e.coneOuterGain,distanceModel:e.distanceModel,maxDistance:e.maxDistance,refDistance:e.refDistance,rolloffFactor:e.rolloffFactor,panningModel:e.panningModel}),this._pannerAttr={coneInnerAngle:void 0!==e.pannerAttr.coneInnerAngle?e.pannerAttr.coneInnerAngle:this._coneInnerAngle,coneOuterAngle:void 0!==e.pannerAttr.coneOuterAngle?e.pannerAttr.coneOuterAngle:this._coneOuterAngle,coneOuterGain:void 0!==e.pannerAttr.coneOuterGain?e.pannerAttr.coneOuterGain:this._coneOuterGain,distanceModel:void 0!==e.pannerAttr.distanceModel?e.pannerAttr.distanceModel:this._distanceModel,maxDistance:void 0!==e.pannerAttr.maxDistance?e.pannerAttr.maxDistance:this._maxDistance,refDistance:void 0!==e.pannerAttr.refDistance?e.pannerAttr.refDistance:this._refDistance,rolloffFactor:void 0!==e.pannerAttr.rolloffFactor?e.pannerAttr.rolloffFactor:this._rolloffFactor,panningModel:void 0!==e.pannerAttr.panningModel?e.pannerAttr.panningModel:this._panningModel})}else 2===r.length&&(e=r[0],t=parseInt(r[1],10));for(var o=this._getSoundIds(t),i=0;i<o.length;i++)if(n=this._soundById(o[i])){var s=n._pannerAttr;s={coneInnerAngle:void 0!==e.coneInnerAngle?e.coneInnerAngle:s.coneInnerAngle,coneOuterAngle:void 0!==e.coneOuterAngle?e.coneOuterAngle:s.coneOuterAngle,coneOuterGain:void 0!==e.coneOuterGain?e.coneOuterGain:s.coneOuterGain,distanceModel:void 0!==e.distanceModel?e.distanceModel:s.distanceModel,maxDistance:void 0!==e.maxDistance?e.maxDistance:s.maxDistance,refDistance:void 0!==e.refDistance?e.refDistance:s.refDistance,rolloffFactor:void 0!==e.rolloffFactor?e.rolloffFactor:s.rolloffFactor,panningModel:void 0!==e.panningModel?e.panningModel:s.panningModel};var a=n._panner;a||(n._pos||(n._pos=this._pos||[0,0,-.5]),v(n,"spatial"),a=n._panner),a.coneInnerAngle=s.coneInnerAngle,a.coneOuterAngle=s.coneOuterAngle,a.coneOuterGain=s.coneOuterGain,a.distanceModel=s.distanceModel,a.maxDistance=s.maxDistance,a.refDistance=s.refDistance,a.rolloffFactor=s.rolloffFactor,a.panningModel=s.panningModel}return this},Sound.prototype.init=(_=Sound.prototype.init,function(){var e=this._parent;this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,_.call(this),this._stereo?e.stereo(this._stereo):this._pos&&e.pos(this._pos[0],this._pos[1],this._pos[2],this._id)}),Sound.prototype.reset=(m=Sound.prototype.reset,function(){var e=this._parent;return this._orientation=e._orientation,this._stereo=e._stereo,this._pos=e._pos,this._pannerAttr=e._pannerAttr,this._stereo?e.stereo(this._stereo):this._pos?e.pos(this._pos[0],this._pos[1],this._pos[2],this._id):this._panner&&(this._panner.disconnect(0),this._panner=void 0,e._refreshBuffer(this)),m.call(this)}),v=function(e,t){"spatial"===(t=t||"spatial")?(e._panner=Howler.ctx.createPanner(),e._panner.coneInnerAngle=e._pannerAttr.coneInnerAngle,e._panner.coneOuterAngle=e._pannerAttr.coneOuterAngle,e._panner.coneOuterGain=e._pannerAttr.coneOuterGain,e._panner.distanceModel=e._pannerAttr.distanceModel,e._panner.maxDistance=e._pannerAttr.maxDistance,e._panner.refDistance=e._pannerAttr.refDistance,e._panner.rolloffFactor=e._pannerAttr.rolloffFactor,e._panner.panningModel=e._pannerAttr.panningModel,void 0!==e._panner.positionX?(e._panner.positionX.setValueAtTime(e._pos[0],Howler.ctx.currentTime),e._panner.positionY.setValueAtTime(e._pos[1],Howler.ctx.currentTime),e._panner.positionZ.setValueAtTime(e._pos[2],Howler.ctx.currentTime)):e._panner.setPosition(e._pos[0],e._pos[1],e._pos[2]),void 0!==e._panner.orientationX?(e._panner.orientationX.setValueAtTime(e._orientation[0],Howler.ctx.currentTime),e._panner.orientationY.setValueAtTime(e._orientation[1],Howler.ctx.currentTime),e._panner.orientationZ.setValueAtTime(e._orientation[2],Howler.ctx.currentTime)):e._panner.setOrientation(e._orientation[0],e._orientation[1],e._orientation[2])):(e._panner=Howler.ctx.createStereoPanner(),e._panner.pan.setValueAtTime(e._stereo,Howler.ctx.currentTime)),e._panner.connect(e._node),e._paused||e._parent.pause(e._id,!0).play(e._id,!0)}},90622:function(){},82912:function(e,t,n){"use strict";t.setRecoil=t.$l=void 0;var r=n(99441),o=n(99441),i={};t.ZP=function(){i.get=(0,o.useRecoilCallback)(function(e){var t=e.snapshot;return function(e){return t.getLoadable(e).contents}},[]),i.getPromise=(0,o.useRecoilCallback)(function(e){var t=e.snapshot;return function(e){return t.getPromise(e)}},[]);var e=(0,r.useGetRecoilValueInfo_UNSTABLE)(),t=(0,o.useRecoilTransaction_UNSTABLE)(function(e){return e.set});return i.set=(0,o.useRecoilCallback)(function(n){var r=n.set;return function(n,o){(0,({atom:t,selector:r})[e(n).type])(n,o)}},[]),i.reset=(0,o.useRecoilCallback)(function(e){return e.reset},[]),null},t.$l=function(e){return i.get(e)},t.setRecoil=function(e,t){i.set(e,t)}},43398:function(e,t,n){"use strict";var r=n(20357);n(90622);var o=n(2265),i=o&&"object"==typeof o&&"default"in o?o:{default:o},s=void 0!==r&&r.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},u=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,o=t.optimizeForSpeed,i=void 0===o?s:o;l(a(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",l("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var u="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=u?u.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(l(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];l(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,n){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var o=document.head||document.getElementsByTagName("head")[0];return n?o.insertBefore(r,n):o.appendChild(r),r},function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},d={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return d[r]||(d[r]="jsx-"+c(e+"-"+n)),d[r]}function p(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return d[n]||(d[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[n]}var f=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,o=t.optimizeForSpeed,i=void 0!==o&&o;this._sheet=r||new u({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),r&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,o=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var i=o.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=i,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var o=h(r,n);return{styleId:o,rules:Array.isArray(t)?t.map(function(e){return p(o,e)}):[p(o,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),_=o.createContext(null);_.displayName="StyleSheetContext";var m=i.default.useInsertionEffect||i.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function y(e){var t=v||o.useContext(_);return t&&("undefined"==typeof window?t.add(e):m(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=y},48059:function(e,t,n){"use strict";e.exports=n(43398).style},4905:function(e,t,n){"use strict";n.d(t,{c:function(){return i}});var r=n(83667),o=n(50836),i=class extends r.z{constructor(e,t){super(e,t)}bindMethods(){super.bindMethods(),this.fetchNextPage=this.fetchNextPage.bind(this),this.fetchPreviousPage=this.fetchPreviousPage.bind(this)}setOptions(e,t){super.setOptions({...e,behavior:(0,o.Gm)()},t)}getOptimisticResult(e){return e.behavior=(0,o.Gm)(),super.getOptimisticResult(e)}fetchNextPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"forward"}}})}fetchPreviousPage(e){return this.fetch({...e,meta:{fetchMore:{direction:"backward"}}})}createResult(e,t){let{state:n}=e,r=super.createResult(e,t),{isFetching:i,isRefetching:s,isError:a,isRefetchError:u}=r,l=n.fetchMeta?.fetchMore?.direction,c=a&&"forward"===l,d=i&&"forward"===l,h=a&&"backward"===l,p=i&&"backward"===l;return{...r,fetchNextPage:this.fetchNextPage,fetchPreviousPage:this.fetchPreviousPage,hasNextPage:(0,o.Qy)(t,n.data),hasPreviousPage:(0,o.ZF)(t,n.data),isFetchNextPageError:c,isFetchingNextPage:d,isFetchPreviousPageError:h,isFetchingPreviousPage:p,isRefetchError:u&&!c&&!h,isRefetching:s&&!d&&!p}}}},54252:function(e,t,n){"use strict";n.d(t,{useInfiniteQuery:function(){return i}});var r=n(4905),o=n(91235);function i(e,t){return(0,o.r)(e,r.c,t)}}}]);