"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[691],{99441:function(e,t,n){let r;n.r(t),n.d(t,{DefaultValue:function(){return iN},RecoilEnv:function(){return iV},RecoilLoadable:function(){return ik},RecoilRoot:function(){return iC},atom:function(){return iI},atomFamily:function(){return iD},constSelector:function(){return iP},errorSelector:function(){return ix},isRecoilValue:function(){return iL},noWait:function(){return iz},readOnlySelector:function(){return iF},retentionZone:function(){return ae},selector:function(){return iB},selectorFamily:function(){return iO},snapshot_UNSTABLE:function(){return i6},useGetRecoilValueInfo_UNSTABLE:function(){return iX},useGotoRecoilSnapshot:function(){return i3},useRecoilBridgeAcrossReactRoots_UNSTABLE:function(){return iU},useRecoilCallback:function(){return i5},useRecoilRefresher_UNSTABLE:function(){return iQ},useRecoilSnapshot:function(){return i8},useRecoilState:function(){return iq},useRecoilStateLoadable:function(){return iZ},useRecoilState_TRANSITION_SUPPORT_UNSTABLE:function(){return i2},useRecoilStoreID:function(){return iM},useRecoilTransactionObserver_UNSTABLE:function(){return i9},useRecoilTransaction_UNSTABLE:function(){return i4},useRecoilValue:function(){return ij},useRecoilValueLoadable:function(){return iH},useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:function(){return i0},useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:function(){return i1},useResetRecoilState:function(){return iJ},useRetain:function(){return i7},useSetRecoilState:function(){return iY},waitForAll:function(){return iW},waitForAllSettled:function(){return iK},waitForAny:function(){return i$},waitForNone:function(){return iG}});var o,i,a,l,s=n(2265),u=n(54887),c=n(20357),d=function(e){let t=Error(e);if(void 0===t.stack)try{throw t}catch(e){}return t},f=function(e){return!!e&&"function"==typeof e.then},h=function(e,t){if(null!=e)return e;throw d(null!=t?t:"Got unexpected null or undefined")};function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class v{getValue(){throw d("BaseLoadable")}toPromise(){throw d("BaseLoadable")}valueMaybe(){throw d("BaseLoadable")}valueOrThrow(){throw d(`Loadable expected value, but in "${this.state}" state`)}promiseMaybe(){throw d("BaseLoadable")}promiseOrThrow(){throw d(`Loadable expected promise, but in "${this.state}" state`)}errorMaybe(){throw d("BaseLoadable")}errorOrThrow(){throw d(`Loadable expected error, but in "${this.state}" state`)}is(e){return e.state===this.state&&e.contents===this.contents}map(e){throw d("BaseLoadable")}}class _ extends v{constructor(e){super(),p(this,"state","hasValue"),p(this,"contents",void 0),this.contents=e}getValue(){return this.contents}toPromise(){return Promise.resolve(this.contents)}valueMaybe(){return this.contents}valueOrThrow(){return this.contents}promiseMaybe(){}errorMaybe(){}map(e){try{let t=e(this.contents);return f(t)?R(t):w(t)?t:S(t)}catch(t){return f(t)?R(t.next(()=>this.map(e))):g(t)}}}class y extends v{constructor(e){super(),p(this,"state","hasError"),p(this,"contents",void 0),this.contents=e}getValue(){throw this.contents}toPromise(){return Promise.reject(this.contents)}valueMaybe(){}promiseMaybe(){}errorMaybe(){return this.contents}errorOrThrow(){return this.contents}map(e){return this}}class m extends v{constructor(e){super(),p(this,"state","loading"),p(this,"contents",void 0),this.contents=e}getValue(){throw this.contents}toPromise(){return this.contents}valueMaybe(){}promiseMaybe(){return this.contents}promiseOrThrow(){return this.contents}errorMaybe(){}map(e){return R(this.contents.then(t=>{let n=e(t);if(w(n))switch(n.state){case"hasValue":case"loading":return n.contents;case"hasError":throw n.contents}return n}).catch(t=>{if(f(t))return t.then(()=>this.map(e).contents);throw t}))}}function S(e){return Object.freeze(new _(e))}function g(e){return Object.freeze(new y(e))}function R(e){return Object.freeze(new m(e))}function T(){return Object.freeze(new m(new Promise(()=>{})))}function b(e){var t;let n=(t=(Array.isArray(e)?e:Object.getOwnPropertyNames(e).map(t=>e[t])).map(e=>w(e)?e:f(e)?R(e):S(e))).every(e=>"hasValue"===e.state)?S(t.map(e=>e.contents)):t.some(e=>"hasError"===e.state)?g(h(t.find(e=>"hasError"===e.state),"Invalid loadable passed to loadableAll").contents):R(Promise.all(t.map(e=>e.contents)));return Array.isArray(e)?n:n.map(t=>Object.getOwnPropertyNames(e).reduce((e,n,r)=>({...e,[n]:t[r]}),{}))}function w(e){return e instanceof v}var A=Object.freeze({__proto__:null,loadableWithValue:S,loadableWithError:g,loadableWithPromise:R,loadableLoading:T,loadableAll:b,isLoadable:w,RecoilLoadable:{of:e=>f(e)?R(e):w(e)?e:S(e),error:e=>g(e),loading:()=>T(),all:b,isLoadable:w}});let E={RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED:!0,RECOIL_GKS_ENABLED:new Set(["recoil_hamt_2020","recoil_sync_external_store","recoil_suppress_rerender_in_callback","recoil_memory_managament_2020"])};function N(e){return E.RECOIL_GKS_ENABLED.has(e)}void 0!==c&&(null==c?void 0:c.env)!=null&&(function(e,t){var n,r;let o=null===(n=c.env[e])||void 0===n?void 0:null===(r=n.toLowerCase())||void 0===r?void 0:r.trim();if(null!=o&&""!==o){if(!["true","false"].includes(o))throw d(`process.env.${e} value must be 'true', 'false', or empty: ${o}`);t("true"===o)}}("RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED",e=>{E.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED=e}),function(e,t){var n;let r=null===(n=c.env[e])||void 0===n?void 0:n.trim();null!=r&&""!==r&&t(r.split(/\s*,\s*|\s+/))}("RECOIL_GKS_ENABLED",e=>{e.forEach(e=>{E.RECOIL_GKS_ENABLED.add(e)})})),N.setPass=e=>{E.RECOIL_GKS_ENABLED.add(e)},N.setFail=e=>{E.RECOIL_GKS_ENABLED.delete(e)},N.clear=()=>{E.RECOIL_GKS_ENABLED.clear()};var L=function(e,t,{error:n}={}){return null};let k=null!==(i=s.createMutableSource)&&void 0!==i?i:s.unstable_createMutableSource,V=null!==(a=s.useMutableSource)&&void 0!==a?a:s.unstable_useMutableSource,C=null!==(l=s.useSyncExternalStore)&&void 0!==l?l:s.unstable_useSyncExternalStore,M=!1;var U={createMutableSource:k,useMutableSource:V,useSyncExternalStore:C,currentRendererSupportsUseSyncExternalStore:function(){var e;let{ReactCurrentDispatcher:t,ReactCurrentOwner:n}=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,r=null!=(null!==(e=null==t?void 0:t.current)&&void 0!==e?e:n.currentDispatcher).useSyncExternalStore;return!C||r||M||(M=!0,L("A React renderer without React 18+ API support is being used with React 18+.")),r},reactMode:function(){return N("recoil_transition_support")?{mode:"TRANSITION_SUPPORT",early:!0,concurrent:!0}:N("recoil_sync_external_store")&&null!=C?{mode:"SYNC_EXTERNAL_STORE",early:!0,concurrent:!1}:N("recoil_mutable_source")&&null!=V&&"undefined"!=typeof window&&!window.$disableRecoilValueMutableSource_TEMP_HACK_DO_NOT_USE?N("recoil_suppress_rerender_in_callback")?{mode:"MUTABLE_SOURCE",early:!0,concurrent:!0}:{mode:"MUTABLE_SOURCE",early:!1,concurrent:!1}:N("recoil_suppress_rerender_in_callback")?{mode:"LEGACY",early:!0,concurrent:!1}:{mode:"LEGACY",early:!1,concurrent:!1}},isFastRefreshEnabled:function(){return!1}};class I{constructor(e){p(this,"key",void 0),this.key=e}toJSON(){return{key:this.key}}}class B extends I{}class D extends I{}var O=Object.freeze({__proto__:null,AbstractRecoilValue:I,RecoilState:B,RecoilValueReadOnly:D,isRecoilValue:function(e){return e instanceof B||e instanceof D}}),P=function(e,...t){},x=function(e,t){return function*(){let n=0;for(let r of e)yield t(r,n++)}()};let{isFastRefreshEnabled:F}=U;class z{}let G=new z,$=new Map,W=new Map;class K extends Error{}let j=new Map;function H(e){return j.get(e)}var q={nodes:$,recoilValues:W,registerNode:function(e){var t;E.RECOIL_DUPLICATE_ATOM_KEY_CHECKING_ENABLED&&(t=e.key,$.has(t)&&console.warn(`Duplicate atom key "${t}". This is a FATAL ERROR in
      production. But it is safe to ignore this warning if it occurred because of
      hot module replacement.`)),$.set(e.key,e);let n=null==e.set?new O.RecoilValueReadOnly(e.key):new O.RecoilState(e.key);return W.set(e.key,n),n},getNode:function(e){let t=$.get(e);if(null==t)throw new K(`Missing definition for RecoilValue: "${e}""`);return t},getNodeMaybe:function(e){return $.get(e)},deleteNodeConfigIfPossible:function(e){var t,n;if(!N("recoil_memory_managament_2020"))return;let r=$.get(e);null!=r&&null!==(t=r.shouldDeleteConfigOnRelease)&&void 0!==t&&t.call(r)&&($.delete(e),null===(n=H(e))||void 0===n||n(),j.delete(e))},setConfigDeletionHandler:function(e,t){N("recoil_memory_managament_2020")&&(void 0===t?j.delete(e):j.set(e,t))},getConfigDeletionHandler:H,recoilValuesForKeys:function(e){return x(e,e=>h(W.get(e)))},NodeMissingError:K,DefaultValue:z,DEFAULT_VALUE:G},Z=function(e,t){t()},Y=(function(e){var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n={},r={},o=function(e){return function(){return e}},i=n.hash=function(e){var n=void 0===e?"undefined":t(e);if("number"===n)return e;"string"!==n&&(e+="");for(var r=0,o=0,i=e.length;o<i;++o)r=(r<<5)-r+e.charCodeAt(o)|0;return r},a=function(e,t){return t>>>e&31},l=function(e){return 1<<e},s=function(e,t){var n;return n=e&t-1,n-=n>>1&1431655765,n=(n=(858993459&n)+(n>>2&858993459))+(n>>4)&252645135,n+=n>>8,127&(n+=n>>16)},u=function(e,t,n,r){var o=r;if(!e){var i=r.length;o=Array(i);for(var a=0;a<i;++a)o[a]=r[a]}return o[t]=n,o},c=function(e,t,n){var r=n.length-1,o=0,i=0,a=n;if(e)o=i=t;else for(a=Array(r);o<t;)a[i++]=n[o++];for(++o;o<=r;)a[i++]=n[o++];return e&&(a.length=r),a},d=function(e,t,n,r){var o=r.length;if(e){for(var i=o;i>=t;)r[i--]=r[i];return r[t]=n,r}for(var a=0,l=0,s=Array(o+1);a<t;)s[l++]=r[a++];for(s[t]=n;a<o;)s[++l]=r[a++];return s},f={__hamt_isEmpty:!0},h=function(e){return e===f||e&&e.__hamt_isEmpty},p=function(e,t,n,r){return{type:1,edit:e,hash:t,key:n,value:r,_modify:b}},v=function(e,t,n){return{type:2,edit:e,hash:t,children:n,_modify:w}},_=function(e,t,n){return{type:3,edit:e,mask:t,children:n,_modify:A}},y=function(e,t,n){return{type:4,edit:e,size:t,children:n,_modify:E}},m=function(e,t,n,r,o){for(var i=[],a=r,l=0,s=0;a;++s)1&a&&(i[s]=o[l++]),a>>>=1;return i[t]=n,y(e,l+1,i)},S=function(e,t,n,r){for(var o=Array(t-1),i=0,a=0,l=0,s=r.length;l<s;++l)if(l!==n){var u=r[l];u&&!h(u)&&(o[i++]=u,a|=1<<l)}return _(e,a,o)},g=function e(t,n,r,o,i,s){if(r===i)return v(t,r,[s,o]);var u=a(n,r),c=a(n,i);return _(t,l(u)|l(c),u===c?[e(t,n+5,r,o,i,s)]:u<c?[o,s]:[s,o])},R=function(e,t,n,o,i,a,l,s){for(var d=i.length,f=0;f<d;++f){var h=i[f];if(n(l,h.key)){var v=h.value,_=a(v);if(_===v)return i;if(_===r)return--s.value,c(e,f,i);return u(e,f,p(t,o,l,_),i)}}var y=a();return y===r?i:(++s.value,u(e,d,p(t,o,l,y),i))},T=function(e,t){return e===t.edit},b=function(e,t,n,o,i,a,l){if(t(a,this.key)){var s=o(this.value);return s===this.value?this:s===r?(--l.value,f):T(e,this)?(this.value=s,this):p(e,i,a,s)}var u=o();return u===r?this:(++l.value,g(e,n,this.hash,this,i,p(e,i,a,u)))},w=function(e,t,n,o,i,a,l){if(i===this.hash){var s=R(T(e,this),e,t,this.hash,this.children,o,a,l);return s===this.children?this:s.length>1?v(e,this.hash,s):s[0]}var u=o();return u===r?this:(++l.value,g(e,n,this.hash,this,i,p(e,i,a,u)))},A=function(e,t,n,r,o,i,p){var v,y=this.mask,S=this.children,g=a(n,o),R=l(g),b=s(y,R),w=y&R,A=w?S[b]:f,E=A._modify(e,t,n+5,r,o,i,p);if(A===E)return this;var N=T(e,this),L=y,k=void 0;if(w&&h(E)){if(!(L&=~R))return f;if(S.length<=2&&((v=S[1^b])===f||1===v.type||2===v.type))return S[1^b];k=c(N,b,S)}else if(w||h(E))k=u(N,b,E,S);else{if(S.length>=16)return m(e,g,E,y,S);L|=R,k=d(N,b,E,S)}return N?(this.mask=L,this.children=k,this):_(e,L,k)},E=function(e,t,n,r,o,i,l){var s=this.size,c=this.children,d=a(n,o),p=c[d],v=(p||f)._modify(e,t,n+5,r,o,i,l);if(p===v)return this;var _=T(e,this),m=void 0;if(h(p)&&!h(v))++s,m=u(_,d,v,c);else if(!h(p)&&h(v)){if(--s<=8)return S(e,s,d,c);m=u(_,d,f,c)}else m=u(_,d,v,c);return _?(this.size=s,this.children=m,this):y(e,s,m)};function N(e,t,n,r,o){this._editable=e,this._edit=t,this._config=n,this._root=r,this._size=o}f._modify=function(e,t,n,o,i,a,l){var s=o();return s===r?f:(++l.value,p(e,i,a,s))},N.prototype.setTree=function(e,t){return this._editable?(this._root=e,this._size=t,this):e===this._root?this:new N(this._editable,this._edit,this._config,e,t)};var L=n.tryGetHash=function(e,t,n,r){for(var o=r._root,i=0,u=r._config.keyEq;;)switch(o.type){case 1:return u(n,o.key)?o.value:e;case 2:if(t===o.hash)for(var c=o.children,d=0,f=c.length;d<f;++d){var h=c[d];if(u(n,h.key))return h.value}return e;case 3:var p=l(a(i,t));if(o.mask&p){o=o.children[s(o.mask,p)],i+=5;break}return e;case 4:if(o=o.children[a(i,t)]){i+=5;break}return e;default:return e}};N.prototype.tryGetHash=function(e,t,n){return L(e,t,n,this)};var k=n.tryGet=function(e,t,n){return L(e,n._config.hash(t),t,n)};N.prototype.tryGet=function(e,t){return k(e,t,this)};var V=n.getHash=function(e,t,n){return L(void 0,e,t,n)};N.prototype.getHash=function(e,t){return V(e,t,this)},n.get=function(e,t){return L(void 0,t._config.hash(e),e,t)},N.prototype.get=function(e,t){return k(t,e,this)};var C=n.has=function(e,t,n){return L(r,e,t,n)!==r};N.prototype.hasHash=function(e,t){return C(e,t,this)};var M=n.has=function(e,t){return C(t._config.hash(e),e,t)};N.prototype.has=function(e){return M(e,this)};var U=function(e,t){return e===t};n.make=function(e){return new N(0,0,{keyEq:e&&e.keyEq||U,hash:e&&e.hash||i},f,0)},n.empty=n.make();var I=n.isEmpty=function(e){return e&&!!h(e._root)};N.prototype.isEmpty=function(){return I(this)};var B=n.modifyHash=function(e,t,n,r){var o={value:r._size},i=r._root._modify(r._editable?r._edit:NaN,r._config.keyEq,0,e,t,n,o);return r.setTree(i,o.value)};N.prototype.modifyHash=function(e,t,n){return B(n,e,t,this)};var D=n.modify=function(e,t,n){return B(e,n._config.hash(t),t,n)};N.prototype.modify=function(e,t){return D(t,e,this)};var O=n.setHash=function(e,t,n,r){return B(o(n),e,t,r)};N.prototype.setHash=function(e,t,n){return O(e,t,n,this)};var P=n.set=function(e,t,n){return O(n._config.hash(e),e,t,n)};N.prototype.set=function(e,t){return P(e,t,this)};var x=o(r),F=n.removeHash=function(e,t,n){return B(x,e,t,n)};N.prototype.removeHash=N.prototype.deleteHash=function(e,t){return F(e,t,this)};var z=n.remove=function(e,t){return F(t._config.hash(e),e,t)};N.prototype.remove=N.prototype.delete=function(e){return z(e,this)};var G=n.beginMutation=function(e){return new N(e._editable+1,e._edit+1,e._config,e._root,e._size)};N.prototype.beginMutation=function(){return G(this)};var $=n.endMutation=function(e){return e._editable=e._editable&&e._editable-1,e};N.prototype.endMutation=function(){return $(this)};var W=n.mutate=function(e,t){var n=G(t);return e(n),$(n)};N.prototype.mutate=function(e){return W(e,this)};var K=function(e){return e&&j(e[0],e[1],e[2],e[3],e[4])},j=function(e,t,n,r,o){for(;n<e;){var i=t[n++];if(i&&!h(i))return H(i,r,[e,t,n,r,o])}return K(o)},H=function(e,t,n){switch(e.type){case 1:return{value:t(e),rest:n};case 2:case 4:case 3:var r=e.children;return j(r.length,r,0,t,n);default:return K(n)}},q={done:!0};function Z(e){this.v=e}Z.prototype.next=function(){if(!this.v)return q;var e=this.v;return this.v=K(e.rest),e},Z.prototype[Symbol.iterator]=function(){return this};var Y=function(e,t){return new Z(H(e._root,t))},J=function(e){return[e.key,e.value]},X=n.entries=function(e){return Y(e,J)};N.prototype.entries=N.prototype[Symbol.iterator]=function(){return X(this)};var Q=function(e){return e.key},ee=n.keys=function(e){return Y(e,Q)};N.prototype.keys=function(){return ee(this)};var et=function(e){return e.value},en=n.values=N.prototype.values=function(e){return Y(e,et)};N.prototype.values=function(){return en(this)};var er=n.fold=function(e,t,n){var r=n._root;if(1===r.type)return e(t,r.value,r.key);for(var o=[r.children],i=void 0;i=o.pop();)for(var a=0,l=i.length;a<l;){var s=i[a++];s&&s.type&&(1===s.type?t=e(t,s.value,s.key):o.push(s.children))}return t};N.prototype.fold=function(e,t){return er(e,t,this)};var eo=n.forEach=function(e,t){return er(function(n,r,o){return e(r,o,t)},null,t)};N.prototype.forEach=function(e){return eo(e,this)};var ei=n.count=function(e){return e._size};N.prototype.count=function(){return ei(this)},Object.defineProperty(N.prototype,"size",{get:N.prototype.count}),e.exports?e.exports=n:undefined.hamt=n}(o={exports:{}},o.exports),o.exports);class J{constructor(e){p(this,"_map",void 0),this._map=new Map(null==e?void 0:e.entries())}keys(){return this._map.keys()}entries(){return this._map.entries()}get(e){return this._map.get(e)}has(e){return this._map.has(e)}set(e,t){return this._map.set(e,t),this}delete(e){return this._map.delete(e),this}clone(){return Q(this)}toMap(){return new Map(this._map)}}class X{constructor(e){if(p(this,"_hamt",Y.empty.beginMutation()),e instanceof X){let t=e._hamt.endMutation();e._hamt=t.beginMutation(),this._hamt=t.beginMutation()}else if(e)for(let[t,n]of e.entries())this._hamt.set(t,n)}keys(){return this._hamt.keys()}entries(){return this._hamt.entries()}get(e){return this._hamt.get(e)}has(e){return this._hamt.has(e)}set(e,t){return this._hamt.set(e,t),this}delete(e){return this._hamt.delete(e),this}clone(){return Q(this)}toMap(){return new Map(this._hamt)}}function Q(e){return N("recoil_hamt_2020")?new X(e):new J(e)}var ee=Object.freeze({__proto__:null,persistentMap:{persistentMap:Q}.persistentMap}),et=function(e,...t){let n=new Set;e:for(let r of e){for(let e of t)if(e.has(r))continue e;n.add(r)}return n},en=function(e,t){let n=new Map;return e.forEach((e,r)=>{n.set(r,t(e,r))}),n};function er(e,t,n,r){let{nodeDeps:o,nodeToNodeSubscriptions:i}=n,a=o.get(e);if(!a||!r||a===r.nodeDeps.get(e)){for(let n of(o.set(e,t),null==a?t:et(t,a)))i.has(n)||i.set(n,new Set),h(i.get(n)).add(e);if(a)for(let n of et(a,t)){if(!i.has(n))return;let t=h(i.get(n));t.delete(e),0===t.size&&i.delete(n)}}}var eo={cloneGraph:function(e){return{nodeDeps:en(e.nodeDeps,e=>new Set(e)),nodeToNodeSubscriptions:en(e.nodeToNodeSubscriptions,e=>new Set(e))}},graph:function(){return{nodeDeps:new Map,nodeToNodeSubscriptions:new Map}},saveDepsToStore:function(e,t,n,r){var o,i,a,l,s;let u=n.getState();r===u.currentTree.version||r===(null===(o=u.nextTree)||void 0===o?void 0:o.version)||r===(null===(i=u.previousTree)||void 0===i?void 0:i.version)||L("Tried to save dependencies to a discarded tree");let c=n.getGraph(r);if(er(e,t,c),r===(null===(a=u.previousTree)||void 0===a?void 0:a.version)&&er(e,t,n.getGraph(u.currentTree.version),c),r===(null===(l=u.previousTree)||void 0===l?void 0:l.version)||r===u.currentTree.version){let r=null===(s=u.nextTree)||void 0===s?void 0:s.version;void 0!==r&&er(e,t,n.getGraph(r),c)}}};let ei=0,ea=0,el=0;var es={getNextTreeStateVersion:()=>ei++,getNextStoreID:()=>ea++,getNextComponentID:()=>el++};let{persistentMap:eu}=ee,{graph:ec}=eo,{getNextTreeStateVersion:ed}=es;function ef(){let e=ed();return{version:e,stateID:e,transactionMetadata:{},dirtyAtoms:new Set,atomValues:eu(),nonvalidatedAtoms:eu()}}var eh={makeEmptyTreeState:ef,makeEmptyStoreState:function(){let e=ef();return{currentTree:e,nextTree:null,previousTree:null,commitDepth:0,knownAtoms:new Set,knownSelectors:new Set,transactionSubscriptions:new Map,nodeTransactionSubscriptions:new Map,nodeToComponentSubscriptions:new Map,queuedComponentCallbacks_DEPRECATED:[],suspendedComponentResolvers:new Set,graphsByVersion:new Map().set(e.version,ec()),retention:{referenceCounts:new Map,nodesRetainedByZone:new Map,retainablesToCheckForRelease:new Set},nodeCleanupFunctions:new Map}},getNextTreeStateVersion:ed};class ep{}var ev={RetentionZone:ep,retentionZone:function(){return new ep}},e_={setByAddingToSet:function(e,t){let n=new Set(e);return n.add(t),n},setByDeletingFromSet:function(e,t){let n=new Set(e);return n.delete(t),n},mapBySettingInMap:function(e,t,n){let r=new Map(e);return r.set(t,n),r},mapByUpdatingInMap:function(e,t,n){let r=new Map(e);return r.set(t,n(r.get(t))),r},mapByDeletingFromMap:function(e,t){let n=new Map(e);return n.delete(t),n},mapByDeletingMultipleFromMap:function(e,t){let n=new Map(e);return t.forEach(e=>n.delete(e)),n}},ey=function*(e,t){let n=0;for(let r of e)t(r,n++)&&(yield r)},em=function(e,t){return new Proxy(e,{get:(e,n)=>(!(n in e)&&n in t&&(e[n]=t[n]()),e[n]),ownKeys:e=>Object.keys(e)})};let{getNode:eS,getNodeMaybe:eg,recoilValuesForKeys:eR}=q,{RetentionZone:eT}=ev,{setByAddingToSet:eb}=e_,ew=Object.freeze(new Set);class eA extends Error{}function eE(e,t,n,r){let o=e.getState();if(o.nodeCleanupFunctions.has(n))return;let i=eS(n),a=function(e,t,n){if(!N("recoil_memory_managament_2020"))return()=>void 0;let{nodesRetainedByZone:r}=e.getState().retention;function o(e){let n=r.get(e);n||r.set(e,n=new Set),n.add(t)}if(n instanceof eT)o(n);else if(Array.isArray(n))for(let e of n)o(e);return()=>{if(!N("recoil_memory_managament_2020"))return;let{retention:r}=e.getState();function o(e){let n=r.nodesRetainedByZone.get(e);null==n||n.delete(t),n&&0===n.size&&r.nodesRetainedByZone.delete(e)}if(n instanceof eT)o(n);else if(Array.isArray(n))for(let e of n)o(e)}}(e,n,i.retainedBy),l=i.init(e,t,r);o.nodeCleanupFunctions.set(n,()=>{l(),a()})}function eN(e,t,n){return eS(n).peek(e,t)}function eL(e,t,n){let r=new Set,o=Array.from(n),i=e.getGraph(t.version);for(let e=o.pop();e;e=o.pop()){var a;for(let t of(r.add(e),null!==(a=i.nodeToNodeSubscriptions.get(e))&&void 0!==a?a:ew))r.has(t)||o.push(t)}return r}var ek={getNodeLoadable:function(e,t,n){return eE(e,t,n,"get"),eS(n).get(e,t)},peekNodeLoadable:eN,setNodeValue:function(e,t,n,r){let o=eS(n);if(null==o.set)throw new eA(`Attempt to set read-only RecoilValue: ${n}`);let i=o.set;return eE(e,t,n,"set"),i(e,t,r)},initializeNode:function(e,t,n){eE(e,e.getState().currentTree,t,n)},cleanUpNode:function(e,t){var n;let r=e.getState();null===(n=r.nodeCleanupFunctions.get(t))||void 0===n||n(),r.nodeCleanupFunctions.delete(t)},setUnvalidatedAtomValue_DEPRECATED:function(e,t,n){var r;let o=eg(t);return null==o||null===(r=o.invalidate)||void 0===r||r.call(o,e),{...e,atomValues:e.atomValues.clone().delete(t),nonvalidatedAtoms:e.nonvalidatedAtoms.clone().set(t,n),dirtyAtoms:eb(e.dirtyAtoms,t)}},peekNodeInfo:function(e,t,n){let r=e.getState(),o=e.getGraph(t.version),i=eS(n).nodeType;return em({type:i},{loadable:()=>eN(e,t,n),isActive:()=>r.knownAtoms.has(n)||r.knownSelectors.has(n),isSet:()=>"selector"!==i&&t.atomValues.has(n),isModified:()=>t.dirtyAtoms.has(n),deps:()=>{var e;return eR(null!==(e=o.nodeDeps.get(n))&&void 0!==e?e:[])},subscribers:()=>{var o,i;return{nodes:eR(ey(eL(e,t,new Set([n])),e=>e!==n)),components:x(null!==(o=null===(i=r.nodeToComponentSubscriptions.get(n))||void 0===i?void 0:i.values())&&void 0!==o?o:[],([e])=>({name:e}))}}})},getDownstreamNodes:eL};let eV=null;var eC={setInvalidateMemoizedSnapshot:function(e){eV=e},invalidateMemoizedSnapshot:function(){var e;null===(e=eV)||void 0===e||e()}};let{getDownstreamNodes:eM,getNodeLoadable:eU,setNodeValue:eI}=ek,{getNextComponentID:eB}=es,{getNode:eD,getNodeMaybe:eO}=q,{DefaultValue:eP}=q,{reactMode:ex}=U,{AbstractRecoilValue:eF,RecoilState:ez,RecoilValueReadOnly:eG,isRecoilValue:e$}=O,{invalidateMemoizedSnapshot:eW}=eC;function eK(e,t,n){"hasValue"===n.state&&n.contents instanceof eP?e.atomValues.delete(t):e.atomValues.set(t,n),e.dirtyAtoms.add(t),e.nonvalidatedAtoms.delete(t)}function ej(e,t){e.replaceState(n=>{let r=eZ(n);for(let n of t)!function(e,t,n){if("set"===n.type){let{recoilValue:r,valueOrUpdater:o}=n,i=function(e,t,{key:n},r){if("function"!=typeof r)return r;{let o=eU(e,t,n);if("loading"===o.state){let e=`Tried to set atom or selector "${n}" using an updater function while the current state is pending, this is not currently supported.`;throw L(e),d(e)}if("hasError"===o.state)throw o.contents;return r(o.contents)}}(e,t,r,o);for(let[n,o]of eI(e,t,r.key,i).entries())eK(t,n,o)}else if("setLoadable"===n.type){let{recoilValue:{key:e},loadable:r}=n;eK(t,e,r)}else if("markModified"===n.type){let{recoilValue:{key:e}}=n;t.dirtyAtoms.add(e)}else if("setUnvalidated"===n.type){var r;let{recoilValue:{key:e},unvalidatedValue:o}=n,i=eO(e);null==i||null===(r=i.invalidate)||void 0===r||r.call(i,t),t.atomValues.delete(e),t.nonvalidatedAtoms.set(e,o),t.dirtyAtoms.add(e)}else L(`Unknown action ${n.type}`)}(e,r,n);return eY(e,r),eW(),r})}function eH(e,t){if(eq.length){let n=eq[eq.length-1],r=n.get(e);r||n.set(e,r=[]),r.push(t)}else ej(e,[t])}let eq=[];function eZ(e){return{...e,atomValues:e.atomValues.clone(),nonvalidatedAtoms:e.nonvalidatedAtoms.clone(),dirtyAtoms:new Set(e.dirtyAtoms)}}function eY(e,t){for(let o of eM(e,t,t.dirtyAtoms)){var n,r;null===(n=eO(o))||void 0===n||null===(r=n.invalidate)||void 0===r||r.call(n,t)}}function eJ(e,t,n){eH(e,{type:"set",recoilValue:t,valueOrUpdater:n})}var eX={RecoilValueReadOnly:eG,AbstractRecoilValue:eF,RecoilState:ez,getRecoilValueAsLoadable:function(e,{key:t},n=e.getState().currentTree){var r,o;let i=e.getState();n.version===i.currentTree.version||n.version===(null===(r=i.nextTree)||void 0===r?void 0:r.version)||n.version===(null===(o=i.previousTree)||void 0===o?void 0:o.version)||L("Tried to read from a discarded tree");let a=eU(e,n,t);return"loading"===a.state&&a.contents.catch(()=>{}),a},setRecoilValue:eJ,setRecoilValueLoadable:function(e,t,n){if(n instanceof eP)return eJ(e,t,n);eH(e,{type:"setLoadable",recoilValue:t,loadable:n})},markRecoilValueModified:function(e,t){eH(e,{type:"markModified",recoilValue:t})},setUnvalidatedRecoilValue:function(e,t,n){eH(e,{type:"setUnvalidated",recoilValue:t,unvalidatedValue:n})},subscribeToRecoilValue:function(e,{key:t},n,r=null){let o=eB(),i=e.getState();i.nodeToComponentSubscriptions.has(t)||i.nodeToComponentSubscriptions.set(t,new Map),h(i.nodeToComponentSubscriptions.get(t)).set(o,[null!=r?r:"<not captured>",n]);let a=ex();if(a.early&&("LEGACY"===a.mode||"MUTABLE_SOURCE"===a.mode)){let r=e.getState().nextTree;r&&r.dirtyAtoms.has(t)&&n(r)}return{release:()=>{let n=e.getState(),r=n.nodeToComponentSubscriptions.get(t);if(void 0===r||!r.has(o)){L(`Subscription missing at release time for atom ${t}. This is a bug in Recoil.`);return}r.delete(o),0===r.size&&n.nodeToComponentSubscriptions.delete(t)}}},isRecoilValue:e$,applyAtomValueWrites:function(e,t){let n=e.clone();return t.forEach((e,t)=>{"hasValue"===e.state&&e.contents instanceof eP?n.delete(t):n.set(t,e)}),n},batchStart:function(){let e=new Map;return eq.push(e),()=>{for(let[t,n]of e)ej(t,n);eq.pop()!==e&&L("Incorrect order of batch popping")}},writeLoadableToTreeState:eK,invalidateDownstreams:eY,copyTreeState:eZ,refreshRecoilValue:function(e,t){var n;let{currentTree:r}=e.getState(),o=eD(t.key);null===(n=o.clearCache)||void 0===n||n.call(o,e,r)}},eQ=function(e,t,n){let r=e.entries(),o=r.next();for(;!o.done;){let i=o.value;if(t.call(n,i[1],i[0],e))return!0;o=r.next()}return!1};let{cleanUpNode:e0}=ek,{deleteNodeConfigIfPossible:e1,getNode:e2}=q,{RetentionZone:e5}=ev,e4=new Set;function e3(e,t){let n=e.getState(),r=n.currentTree;if(n.nextTree){L("releaseNodesNowOnCurrentTree should only be called at the end of a batch");return}let o=new Set;for(let e of t)if(e instanceof e5)for(let t of function(e,t){var n;return null!==(n=e.retention.nodesRetainedByZone.get(t))&&void 0!==n?n:e4}(n,e))o.add(t);else o.add(e);for(let t of function(e,t){let n=e.getState(),r=n.currentTree,o=e.getGraph(r.version),i=new Set,a=new Set;return function t(l){var s,u;let c=new Set;for(let t of function(e,t,n,r,o){let i=e.getGraph(t.version),a=[],l=new Set;for(;n.size>0;)(function e(t){if(r.has(t)||o.has(t)){n.delete(t);return}if(l.has(t))return;let s=i.nodeToNodeSubscriptions.get(t);if(s)for(let t of s)e(t);l.add(t),n.delete(t),a.push(t)})(h(n.values().next().value));return a}(e,r,l,i,a)){if("recoilRoot"===e2(t).retainedBy||(null!==(s=n.retention.referenceCounts.get(t))&&void 0!==s?s:0)>0||e8(t).some(e=>n.retention.referenceCounts.get(e))){a.add(t);continue}let e=o.nodeToNodeSubscriptions.get(t);if(e&&eQ(e,e=>a.has(e))){a.add(t);continue}i.add(t),c.add(t)}let d=new Set;for(let e of c)for(let t of null!==(u=o.nodeDeps.get(e))&&void 0!==u?u:e4)i.has(t)||d.add(t);d.size&&t(d)}(t),i}(e,o))!function(e,t,n){var r,o;if(!N("recoil_memory_managament_2020"))return;e0(e,n);let i=e.getState();for(let e of(i.knownAtoms.delete(n),i.knownSelectors.delete(n),i.nodeTransactionSubscriptions.delete(n),i.retention.referenceCounts.delete(n),e8(n)))null===(r=i.retention.nodesRetainedByZone.get(e))||void 0===r||r.delete(n);t.atomValues.delete(n),t.dirtyAtoms.delete(n),t.nonvalidatedAtoms.delete(n);let a=i.graphsByVersion.get(t.version);if(a){let e=a.nodeDeps.get(n);if(void 0!==e)for(let t of(a.nodeDeps.delete(n),e))null===(o=a.nodeToNodeSubscriptions.get(t))||void 0===o||o.delete(n);a.nodeToNodeSubscriptions.delete(n)}e1(n)}(e,r,t)}function e8(e){let t=e2(e).retainedBy;return void 0===t||"components"===t||"recoilRoot"===t?[]:t instanceof e5?[t]:t}function e9(e,t){N("recoil_memory_managament_2020")&&(e.getState().retention.referenceCounts.delete(t),function(e,t){let n=e.getState();n.nextTree?n.retention.retainablesToCheckForRelease.add(t):e3(e,new Set([t]))}(e,t))}var e6={SUSPENSE_TIMEOUT_MS:12e4,updateRetainCount:function(e,t,n){var r;if(!N("recoil_memory_managament_2020"))return;let o=e.getState().retention.referenceCounts,i=(null!==(r=o.get(t))&&void 0!==r?r:0)+n;0===i?e9(e,t):o.set(t,i)},updateRetainCountToZero:e9,releaseScheduledRetainablesNow:function(e){if(!N("recoil_memory_managament_2020"))return;let t=e.getState();e3(e,t.retention.retainablesToCheckForRelease),t.retention.retainablesToCheckForRelease.clear()},retainedByOptionWithDefault:function(e){return void 0===e?"recoilRoot":e}};let{unstable_batchedUpdates:e7}=u,{unstable_batchedUpdates:te}={unstable_batchedUpdates:e7},{batchStart:tt}=eX,{unstable_batchedUpdates:tn}={unstable_batchedUpdates:te},tr=tn||(e=>e());var to={getBatcher:()=>tr,setBatcher:e=>{tr=e},batchUpdates:e=>{tr(()=>{let t=()=>void 0;try{t=tt(),e()}finally{t()}})}},ti=function*(e){for(let t of e)for(let e of t)yield e};let ta="undefined"==typeof Window||"undefined"==typeof window;var tl={isSSR:ta,isReactNative:"undefined"!=typeof navigator&&"ReactNative"===navigator.product,isWindow:e=>!ta&&(e===window||e instanceof Window)};let{batchUpdates:ts}=to,{initializeNode:tu,peekNodeInfo:tc}=ek,{graph:td}=eo,{getNextStoreID:tf}=es,{DEFAULT_VALUE:th,recoilValues:tp,recoilValuesForKeys:tv}=q,{AbstractRecoilValue:t_,getRecoilValueAsLoadable:ty,setRecoilValue:tm,setUnvalidatedRecoilValue:tS}=eX,{updateRetainCount:tg}=e6,{setInvalidateMemoizedSnapshot:tR}=eC,{getNextTreeStateVersion:tT,makeEmptyStoreState:tb}=eh,{isSSR:tw}=tl,{memoizeOneWithArgsHashAndInvalidation:tA}={memoizeWithArgsHash:function(e,t){let n;return(...r)=>{n||(n={});let o=t(...r);return Object.hasOwnProperty.call(n,o)||(n[o]=e(...r)),n[o]}},memoizeOneWithArgsHash:function(e,t){let n,r;return(...o)=>{let i=t(...o);return n===i?r:(n=i,r=e(...o))}},memoizeOneWithArgsHashAndInvalidation:function(e,t){let n,r;return[(...o)=>{let i=t(...o);return n===i?r:(n=i,r=e(...o))},()=>{n=null}]}};class tE{constructor(e,t){for(let n of(p(this,"_store",void 0),p(this,"_refCount",1),p(this,"getLoadable",e=>(this.checkRefCount_INTERNAL(),ty(this._store,e))),p(this,"getPromise",e=>(this.checkRefCount_INTERNAL(),this.getLoadable(e).toPromise())),p(this,"getNodes_UNSTABLE",e=>{if(this.checkRefCount_INTERNAL(),(null==e?void 0:e.isModified)===!0)return(null==e?void 0:e.isInitialized)===!1?[]:tv(this._store.getState().currentTree.dirtyAtoms);let t=this._store.getState().knownAtoms,n=this._store.getState().knownSelectors;return(null==e?void 0:e.isInitialized)==null?tp.values():!0===e.isInitialized?tv(ti([t,n])):ey(tp.values(),({key:e})=>!t.has(e)&&!n.has(e))}),p(this,"getInfo_UNSTABLE",({key:e})=>(this.checkRefCount_INTERNAL(),tc(this._store,this._store.getState().currentTree,e))),p(this,"map",e=>{this.checkRefCount_INTERNAL();let t=new tV(this,ts);return e(t),t}),p(this,"asyncMap",async e=>{this.checkRefCount_INTERNAL();let t=new tV(this,ts);return t.retain(),await e(t),t.autoRelease_INTERNAL(),t}),this._store={storeID:tf(),parentStoreID:t,getState:()=>e,replaceState:t=>{e.currentTree=t(e.currentTree)},getGraph:t=>{let n=e.graphsByVersion;if(n.has(t))return h(n.get(t));let r=td();return n.set(t,r),r},subscribeToTransactions:()=>({release:()=>{}}),addTransactionMetadata:()=>{throw d("Cannot subscribe to Snapshots")}},this._store.getState().knownAtoms))tu(this._store,n,"get"),tg(this._store,n,1);this.autoRelease_INTERNAL()}retain(){this._refCount<=0&&L("Attempt to retain() Snapshot that was already released."),this._refCount++;let e=!1;return()=>{e||(e=!0,this._release())}}autoRelease_INTERNAL(){tw||window.setTimeout(()=>this._release(),10)}_release(){if(this._refCount--,0===this._refCount){if(this._store.getState().nodeCleanupFunctions.forEach(e=>e()),this._store.getState().nodeCleanupFunctions.clear(),!N("recoil_memory_managament_2020"))return}else this._refCount}isRetained(){return this._refCount>0}checkRefCount_INTERNAL(){N("recoil_memory_managament_2020")&&this._refCount}getStore_INTERNAL(){return this.checkRefCount_INTERNAL(),this._store}getID(){return this.checkRefCount_INTERNAL(),this._store.getState().currentTree.stateID}getStoreID(){return this.checkRefCount_INTERNAL(),this._store.storeID}}function tN(e,t,n=!1){let r=e.getState(),o=n?tT():t.version;return{currentTree:{version:n?o:t.version,stateID:n?o:t.stateID,transactionMetadata:{...t.transactionMetadata},dirtyAtoms:new Set(t.dirtyAtoms),atomValues:t.atomValues.clone(),nonvalidatedAtoms:t.nonvalidatedAtoms.clone()},commitDepth:0,nextTree:null,previousTree:null,knownAtoms:new Set(r.knownAtoms),knownSelectors:new Set(r.knownSelectors),transactionSubscriptions:new Map,nodeTransactionSubscriptions:new Map,nodeToComponentSubscriptions:new Map,queuedComponentCallbacks_DEPRECATED:[],suspendedComponentResolvers:new Set,graphsByVersion:new Map().set(o,e.getGraph(t.version)),retention:{referenceCounts:new Map,nodesRetainedByZone:new Map,retainablesToCheckForRelease:new Set},nodeCleanupFunctions:new Map(x(r.nodeCleanupFunctions.entries(),([e])=>[e,()=>{}]))}}let[tL,tk]=tA((e,t)=>{var n;let r=e.getState();return new tE(tN(e,"latest"===t?null!==(n=r.nextTree)&&void 0!==n?n:r.currentTree:h(r.previousTree)),e.storeID)},(e,t)=>{var n,r;return String(t)+String(e.storeID)+String(null===(n=e.getState().nextTree)||void 0===n?void 0:n.version)+String(e.getState().currentTree.version)+String(null===(r=e.getState().previousTree)||void 0===r?void 0:r.version)});tR(tk);class tV extends tE{constructor(e,t){super(tN(e.getStore_INTERNAL(),e.getStore_INTERNAL().getState().currentTree,!0),e.getStoreID()),p(this,"_batch",void 0),p(this,"set",(e,t)=>{this.checkRefCount_INTERNAL();let n=this.getStore_INTERNAL();this._batch(()=>{tg(n,e.key,1),tm(this.getStore_INTERNAL(),e,t)})}),p(this,"reset",e=>{this.checkRefCount_INTERNAL();let t=this.getStore_INTERNAL();this._batch(()=>{tg(t,e.key,1),tm(this.getStore_INTERNAL(),e,th)})}),p(this,"setUnvalidatedAtomValues_DEPRECATED",e=>{this.checkRefCount_INTERNAL();let t=this.getStore_INTERNAL();ts(()=>{for(let[n,r]of e.entries())tg(t,n,1),tS(t,new t_(n),r)})}),this._batch=t}}var tC=Object.freeze({__proto__:null,Snapshot:tE,MutableSnapshot:tV,freshSnapshot:function(e){let t=new tE(tb());return null!=e?t.map(e):t},cloneSnapshot:function(e,t="latest"){let n=tL(e,t);return n.isRetained()?n:(tk(),tL(e,t))}}),tM=function(...e){let t=new Set;for(let n of e)for(let e of n)t.add(e);return t};let{useRef:tU}=s;var tI=function(e){let t=tU(e);return t.current===e&&"function"==typeof e&&(t.current=e()),t};let{getNextTreeStateVersion:tB,makeEmptyStoreState:tD}=eh,{cleanUpNode:tO,getDownstreamNodes:tP,initializeNode:tx,setNodeValue:tF,setUnvalidatedAtomValue_DEPRECATED:tz}=ek,{graph:tG}=eo,{cloneGraph:t$}=eo,{getNextStoreID:tW}=es,{createMutableSource:tK,reactMode:tj}=U,{applyAtomValueWrites:tH}=eX,{releaseScheduledRetainablesNow:tq}=e6,{freshSnapshot:tZ}=tC,{useCallback:tY,useContext:tJ,useEffect:tX,useMemo:tQ,useRef:t0,useState:t1}=s;function t2(){throw d("This component must be used inside a <RecoilRoot> component.")}let t5=Object.freeze({storeID:tW(),getState:t2,replaceState:t2,getGraph:t2,subscribeToTransactions:t2,addTransactionMetadata:t2}),t4=!1;function t3(e){if(t4)throw d("An atom update was triggered within the execution of a state updater function. State updater functions provided to Recoil must be pure functions.");let t=e.getState();if(null===t.nextTree){N("recoil_memory_managament_2020")&&N("recoil_release_on_cascading_update_killswitch_2021")&&t.commitDepth>0&&tq(e);let n=t.currentTree.version,r=tB();t.nextTree={...t.currentTree,version:r,stateID:r,dirtyAtoms:new Set,transactionMetadata:{}},t.graphsByVersion.set(r,t$(h(t.graphsByVersion.get(n))))}}let t8=s.createContext({current:t5}),t9=()=>tJ(t8),t6=s.createContext(null);function t7(e,t,n){for(let r of tP(e,n,n.dirtyAtoms)){let e=t.nodeToComponentSubscriptions.get(r);if(e)for(let[t,[r,o]]of e)o(n)}}function ne(e){let t=e.getState(),n=t.currentTree,r=n.dirtyAtoms;if(r.size){for(let[n,o]of t.nodeTransactionSubscriptions)if(r.has(n))for(let[t,n]of o)n(e);for(let[n,r]of t.transactionSubscriptions)r(e);(!tj().early||t.suspendedComponentResolvers.size>0)&&(t7(e,t,n),t.suspendedComponentResolvers.forEach(e=>e()),t.suspendedComponentResolvers.clear())}t.queuedComponentCallbacks_DEPRECATED.forEach(e=>e(n)),t.queuedComponentCallbacks_DEPRECATED.splice(0,t.queuedComponentCallbacks_DEPRECATED.length)}function nt({setNotifyBatcherOfChange:e}){let t=t9(),[,n]=t1([]);return e(()=>n({})),tX(()=>(e(()=>n({})),()=>{e(()=>{})}),[e]),tX(()=>{Z("Batcher",()=>{!function(e){let t=e.getState();t.commitDepth++;try{let{nextTree:n}=t;if(null==n)return;t.previousTree=t.currentTree,t.currentTree=n,t.nextTree=null,ne(e),null!=t.previousTree?t.graphsByVersion.delete(t.previousTree.version):L("Ended batch with no previous state, which is unexpected","recoil"),t.previousTree=null,N("recoil_memory_managament_2020")&&null==n&&tq(e)}finally{t.commitDepth--}}(t.current)})}),null}let nn=0;function nr({initializeState_DEPRECATED:e,initializeState:t,store_INTERNAL:n,children:r}){let o;let i=e=>{let t=o.current.graphsByVersion;if(t.has(e))return h(t.get(e));let n=tG();return t.set(e,n),n},a=(e,t)=>{if(null==t){let{transactionSubscriptions:t}=f.current.getState(),n=nn++;return t.set(n,e),{release:()=>{t.delete(n)}}}{let{nodeTransactionSubscriptions:n}=f.current.getState();n.has(t)||n.set(t,new Map);let r=nn++;return h(n.get(t)).set(r,e),{release:()=>{let e=n.get(t);e&&(e.delete(r),0===e.size&&n.delete(t))}}}},l=e=>{for(let t of(t3(f.current),Object.keys(e)))h(f.current.getState().nextTree).transactionMetadata[t]=e[t]},u=e=>{let t;t3(f.current);let n=h(o.current.nextTree);try{t4=!0,t=e(n)}finally{t4=!1}t!==n&&(o.current.nextTree=t,tj().early&&t7(f.current,o.current,t),h(c.current)())},c=t0(null),d=tY(e=>{c.current=e},[c]),f=tI(()=>null!=n?n:{storeID:tW(),getState:()=>o.current,replaceState:u,getGraph:i,subscribeToTransactions:a,addTransactionMetadata:l});null!=n&&(f.current=n);let p=tQ(()=>null==tK?void 0:tK(o,()=>o.current.currentTree.version),[o=tI(()=>null!=e?function(e,t){let n=tD();return t({set:(t,r)=>{let o=n.currentTree,i=tF(e,o,t.key,r),a=new Set(i.keys()),l=o.nonvalidatedAtoms.clone();for(let e of a)l.delete(e);n.currentTree={...o,dirtyAtoms:tM(o.dirtyAtoms,a),atomValues:tH(o.atomValues,i),nonvalidatedAtoms:l}},setUnvalidatedAtomValues:e=>{e.forEach((e,t)=>{n.currentTree=tz(n.currentTree,t,e)})}}),n}(f.current,e):null!=t?function(e){let t=tZ(e),n=t.getStore_INTERNAL().getState();return t.retain(),n.nodeCleanupFunctions.forEach(e=>e()),n.nodeCleanupFunctions.clear(),n}(t):tD())]);return tX(()=>{let e=f.current;for(let t of new Set(e.getState().knownAtoms))tx(e,t,"get");return()=>{for(let t of e.getState().knownAtoms)tO(e,t)}},[f]),s.createElement(t8.Provider,{value:f},s.createElement(t6.Provider,{value:p},s.createElement(nt,{setNotifyBatcherOfChange:d}),r))}var no={RecoilRoot:function(e){let{override:t,...n}=e,r=t9();return!1===t&&r.current!==t5?e.children:s.createElement(nr,n)},useStoreRef:t9,useRecoilMutableSource:function(){let e=tJ(t6);return null==e&&P("Attempted to use a Recoil hook outside of a <RecoilRoot>. <RecoilRoot> must be an ancestor of any component that uses Recoil hooks."),e},useRecoilStoreID:function(){return t9().current.storeID},notifyComponents_FOR_TESTING:t7,sendEndOfBatchNotifications_FOR_TESTING:ne},ni=function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0};let{useEffect:na,useRef:nl}=s;var ns=function(e){let t=nl();return na(()=>{t.current=e}),t.current};let{useStoreRef:nu}=no,{SUSPENSE_TIMEOUT_MS:nc}=e6,{updateRetainCount:nd}=e6,{RetentionZone:nf}=ev,{useEffect:nh,useRef:np}=s,{isSSR:nv}=tl;var n_=function(e){if(N("recoil_memory_managament_2020"))return function(e){let t=(Array.isArray(e)?e:[e]).map(e=>e instanceof nf?e:e.key),n=nu();nh(()=>{if(!N("recoil_memory_managament_2020"))return;let e=n.current;if(r.current&&!nv)window.clearTimeout(r.current),r.current=null;else for(let n of t)nd(e,n,1);return()=>{for(let n of t)nd(e,n,-1)}},[n,...t]);let r=np(),o=ns(t);if(!nv&&(void 0===o||!ni(o,t))){let e=n.current;for(let n of t)nd(e,n,1);if(o)for(let t of o)nd(e,t,-1);r.current&&window.clearTimeout(r.current),r.current=window.setTimeout(()=>{for(let n of(r.current=null,t))nd(e,n,-1)},nc)}}(e)},ny=function(){return"<component name not available>"};let{batchUpdates:nm}=to,{DEFAULT_VALUE:nS}=q,{currentRendererSupportsUseSyncExternalStore:ng,reactMode:nR,useMutableSource:nT,useSyncExternalStore:nb}=U,{useRecoilMutableSource:nw,useStoreRef:nA}=no,{isRecoilValue:nE}=O,{AbstractRecoilValue:nN,getRecoilValueAsLoadable:nL,setRecoilValue:nk,setUnvalidatedRecoilValue:nV,subscribeToRecoilValue:nC}=eX,{useCallback:nM,useEffect:nU,useMemo:nI,useRef:nB,useState:nD}=s,{setByAddingToSet:nO}=e_,{isSSR:nP}=tl;function nx(e,t,n){if("hasValue"===e.state)return e.contents;if("loading"===e.state)throw new Promise(t=>{let r=n.current.getState().suspendedComponentResolvers;r.add(t),nP&&f(e.contents)&&e.contents.finally(()=>{r.delete(t)})});if("hasError"===e.state)throw e.contents;throw d(`Invalid value of loadable atom "${t.key}"`)}function nF(e){let t=nA(),n=ny(),r=nM(()=>{var n;let r=t.current,o=r.getState();return{loadable:nL(r,e,nR().early&&null!==(n=o.nextTree)&&void 0!==n?n:o.currentTree),key:e.key}},[t,e]),o=nM(e=>{let t;return()=>{var n,r;let o=e();return null!==(n=t)&&void 0!==n&&n.loadable.is(o.loadable)&&(null===(r=t)||void 0===r?void 0:r.key)===o.key?t:(t=o,o)}},[]),i=nI(()=>o(r),[r,o]);return nb(nM(r=>nC(t.current,e,r,n).release,[t,e,n]),i,i).loadable}function nz(e){let t=nA(),n=nM(()=>{var n;let r=t.current,o=r.getState();return nL(r,e,nR().early&&null!==(n=o.nextTree)&&void 0!==n?n:o.currentTree)},[t,e]),r=nM(()=>n(),[n]),o=ny(),i=nM((r,i)=>nC(t.current,e,()=>{if(!N("recoil_suppress_rerender_in_callback"))return i();let e=n();s.current.is(e)||i(),s.current=e},o).release,[t,e,o,n]),a=nw();if(null==a)throw d("Recoil hooks must be used in components contained within a <RecoilRoot> component.");let l=nT(a,r,i),s=nB(l);return nU(()=>{s.current=l}),l}function nG(e){let t=nA(),n=ny(),r=nM(()=>{var n;let r=t.current,o=r.getState();return nL(r,e,nR().early&&null!==(n=o.nextTree)&&void 0!==n?n:o.currentTree)},[t,e]),o=nM(()=>({loadable:r(),key:e.key}),[r,e.key]),i=nM(e=>{let t=o();return e.loadable.is(t.loadable)&&e.key===t.key?e:t},[o]);nU(()=>{let r=nC(t.current,e,e=>{l(i)},n);return l(i),r.release},[n,e,t,i]);let[a,l]=nD(o);return a.key!==e.key?o().loadable:a.loadable}function n$(e){let t=nA(),[,n]=nD([]),r=ny(),o=nM(()=>{var n;let r=t.current,o=r.getState();return nL(r,e,nR().early&&null!==(n=o.nextTree)&&void 0!==n?n:o.currentTree)},[t,e]),i=o(),a=nB(i);return nU(()=>{a.current=i}),nU(()=>{let i=t.current,l=i.getState(),s=nC(i,e,e=>{var t;if(!N("recoil_suppress_rerender_in_callback"))return n([]);let r=o();null!==(t=a.current)&&void 0!==t&&t.is(r)||n(r),a.current=r},r);if(l.nextTree)i.getState().queuedComponentCallbacks_DEPRECATED.push(()=>{a.current=null,n([])});else{var u;if(!N("recoil_suppress_rerender_in_callback"))return n([]);let e=o();null!==(u=a.current)&&void 0!==u&&u.is(e)||n(e),a.current=e}return s.release},[r,o,e,t]),i}function nW(e){return N("recoil_memory_managament_2020")&&n_(e),({TRANSITION_SUPPORT:nG,SYNC_EXTERNAL_STORE:ng()?nF:nG,MUTABLE_SOURCE:nz,LEGACY:n$})[nR().mode](e)}function nK(e){let t=nA();return nx(nW(e),e,t)}function nj(e){let t=nA();return nM(n=>{nk(t.current,e,n)},[t,e])}function nH(e){return N("recoil_memory_managament_2020")&&n_(e),nG(e)}function nq(e){let t=nA();return nx(nH(e),e,t)}var nZ=function(e,t){let n=new Map;for(let[r,o]of e)t(o,r)&&n.set(r,o);return n},nY=function(e,t){let n=new Set;for(let r of e)t(r)&&n.add(r);return n},nJ=function(...e){let t=new Map;for(let n=0;n<e.length;n++){let r;let o=e[n].keys();for(;!(r=o.next()).done;)t.set(r.value,e[n].get(r.value))}return t};let{batchUpdates:nX}=to,{DEFAULT_VALUE:nQ,getNode:n0,nodes:n1}=q,{useStoreRef:n2}=no,{AbstractRecoilValue:n5,setRecoilValueLoadable:n4}=eX,{SUSPENSE_TIMEOUT_MS:n3}=e6,{cloneSnapshot:n8}=tC,{useCallback:n9,useEffect:n6,useRef:n7,useState:re}=s,{isSSR:rt}=tl;function rn(e){let t=n2();n6(()=>t.current.subscribeToTransactions(e).release,[e,t])}function rr(e){let t=en(nZ(e.atomValues.toMap(),(e,t)=>{let n=n0(t).persistence_UNSTABLE;return null!=n&&"none"!==n.type&&"hasValue"===e.state}),e=>e.contents);return nJ(e.nonvalidatedAtoms.toMap(),t)}function ro(e,t){var n;let r=e.getState(),o=null!==(n=r.nextTree)&&void 0!==n?n:r.currentTree,i=t.getStore_INTERNAL().getState().currentTree;nX(()=>{let n=new Set;for(let e of[o.atomValues.keys(),i.atomValues.keys()])for(let t of e){var r,a;(null===(r=o.atomValues.get(t))||void 0===r?void 0:r.contents)!==(null===(a=i.atomValues.get(t))||void 0===a?void 0:a.contents)&&n0(t).shouldRestoreFromSnapshots&&n.add(t)}n.forEach(t=>{n4(e,new n5(t),i.atomValues.has(t)?h(i.atomValues.get(t)):nQ)}),e.replaceState(e=>({...e,stateID:t.getID()}))})}var ri={useRecoilSnapshot:function(){let e=n2(),[t,n]=re(()=>n8(e.current)),r=ns(t),o=n7(),i=n7();if(rn(n9(e=>n(n8(e)),[])),n6(()=>{let e=t.retain();if(o.current&&!rt){var n;window.clearTimeout(o.current),o.current=null,null===(n=i.current)||void 0===n||n.call(i),i.current=null}return()=>{window.setTimeout(e,10)}},[t]),r!==t&&!rt){if(o.current){var a;window.clearTimeout(o.current),o.current=null,null===(a=i.current)||void 0===a||a.call(i),i.current=null}i.current=t.retain(),o.current=window.setTimeout(()=>{var e;o.current=null,null===(e=i.current)||void 0===e||e.call(i),i.current=null},n3)}return t},gotoSnapshot:ro,useGotoRecoilSnapshot:function(){let e=n2();return n9(t=>ro(e.current,t),[e])},useRecoilTransactionObserver:function(e){rn(n9(t=>{e({snapshot:n8(t,"latest"),previousSnapshot:n8(t,"previous")})},[e]))},useTransactionObservation_DEPRECATED:function(e){rn(n9(t=>{let n=t.getState().previousTree,r=t.getState().currentTree;n||(L("Transaction subscribers notified without a previous tree being present -- this is a bug in Recoil"),n=t.getState().currentTree);let o=rr(r),i=rr(n),a=en(n1,e=>{var t,n,r,o;return{persistence_UNSTABLE:{type:null!==(t=null===(n=e.persistence_UNSTABLE)||void 0===n?void 0:n.type)&&void 0!==t?t:"none",backButton:null!==(r=null===(o=e.persistence_UNSTABLE)||void 0===o?void 0:o.backButton)&&void 0!==r&&r}}}),l=nY(r.dirtyAtoms,e=>o.has(e)||i.has(e));e({atomValues:o,previousAtomValues:i,atomInfo:a,modifiedAtoms:l,transactionMetadata:{...r.transactionMetadata}})},[e]))},useTransactionSubscription_DEPRECATED:rn};let{peekNodeInfo:ra}=ek,{useStoreRef:rl}=no,{reactMode:rs}=U,{RecoilRoot:ru,useStoreRef:rc}=no,{useMemo:rd}=s,{loadableWithValue:rf}=A,{initializeNode:rh}=ek,{DEFAULT_VALUE:rp,getNode:rv}=q,{copyTreeState:r_,getRecoilValueAsLoadable:ry,invalidateDownstreams:rm,writeLoadableToTreeState:rS}=eX;function rg(e){return"atom"===rv(e.key).nodeType}class rR{constructor(e,t){p(this,"_store",void 0),p(this,"_treeState",void 0),p(this,"_changes",void 0),p(this,"get",e=>{if(this._changes.has(e.key))return this._changes.get(e.key);if(!rg(e))throw d("Reading selectors within atomicUpdate is not supported");let t=ry(this._store,e,this._treeState);if("hasValue"===t.state)return t.contents;if("hasError"===t.state)throw t.contents;throw d(`Expected Recoil atom ${e.key} to have a value, but it is in a loading state.`)}),p(this,"set",(e,t)=>{if(!rg(e))throw d("Setting selectors within atomicUpdate is not supported");if("function"==typeof t){let n=this.get(e);this._changes.set(e.key,t(n))}else rh(this._store,e.key,"set"),this._changes.set(e.key,t)}),p(this,"reset",e=>{this.set(e,rp)}),this._store=e,this._treeState=t,this._changes=new Map}newTreeState_INTERNAL(){if(0===this._changes.size)return this._treeState;let e=r_(this._treeState);for(let[t,n]of this._changes)rS(e,t,rf(n));return rm(this._store,e),e}}var rT=Object.freeze({__proto__:null,atomicUpdater:function(e){return t=>{e.replaceState(n=>{let r=new rR(e,n);return t(r),r.newTreeState_INTERNAL()})}}}),rb=function(e,t){if(!e)throw Error(t)};let{atomicUpdater:rw}=rT,{batchUpdates:rA}=to,{DEFAULT_VALUE:rE}=q,{useStoreRef:rN}=no,{refreshRecoilValue:rL,setRecoilValue:rk}=eX,{cloneSnapshot:rV}=tC,{gotoSnapshot:rC}=ri,{useCallback:rM}=s;class rU{}let rI=new rU;function rB(e,t,n,r){let o,i=rI;if(rA(()=>{let a="useRecoilCallback() expects a function that returns a function: it accepts a function of the type (RecoilInterface) => (Args) => ReturnType and returns a callback function (Args) => ReturnType, where RecoilInterface is an object {snapshot, set, ...} and Args and ReturnType are the argument and return types of the callback you want to create.  Please see the docs at recoiljs.org for details.";if("function"!=typeof t)throw d(a);let l=t(em({...null!=r?r:{},set:(t,n)=>rk(e,t,n),reset:t=>rk(e,t,rE),refresh:t=>rL(e,t),gotoSnapshot:t=>rC(e,t),transact_UNSTABLE:t=>rw(e)(t)},{snapshot:()=>{let t=rV(e);return o=t.retain(),t}}));if("function"!=typeof l)throw d(a);i=l(...n)}),i instanceof rU&&rb(!1),f(i))i=i.finally(()=>{var e;null===(e=o)||void 0===e||e()});else{var a;null===(a=o)||void 0===a||a()}return i}var rD={recoilCallback:rB,useRecoilCallback:function(e,t){let n=rN();return rM((...t)=>rB(n.current,e,t),null!=t?[...t,n]:void 0)}};let{useStoreRef:rO}=no,{refreshRecoilValue:rP}=eX,{useCallback:rx}=s,{atomicUpdater:rF}=rT,{useStoreRef:rz}=no,{useMemo:rG}=s;class r${constructor(e){p(this,"value",void 0),this.value=e}}var rW=Object.freeze({__proto__:null,WrappedValue:{WrappedValue:r$}.WrappedValue});let{isFastRefreshEnabled:rK}=U;class rj extends Error{}class rH{constructor(e){var t,n,r;p(this,"_name",void 0),p(this,"_numLeafs",void 0),p(this,"_root",void 0),p(this,"_onHit",void 0),p(this,"_onSet",void 0),p(this,"_mapNodeValue",void 0),this._name=null==e?void 0:e.name,this._numLeafs=0,this._root=null,this._onHit=null!==(t=null==e?void 0:e.onHit)&&void 0!==t?t:()=>{},this._onSet=null!==(n=null==e?void 0:e.onSet)&&void 0!==n?n:()=>{},this._mapNodeValue=null!==(r=null==e?void 0:e.mapNodeValue)&&void 0!==r?r:e=>e}size(){return this._numLeafs}root(){return this._root}get(e,t){var n;return null===(n=this.getLeafNode(e,t))||void 0===n?void 0:n.value}getLeafNode(e,t){if(null==this._root)return;let n=this._root;for(;n;){if(null==t||t.onNodeVisit(n),"leaf"===n.type)return this._onHit(n),n;let r=this._mapNodeValue(e(n.nodeKey));n=n.branches.get(r)}}set(e,t,n){let r=()=>{var r,o,i,a,l,s,u;let c,d;for(let[t,r]of e){let e=this._root;if((null==e?void 0:e.type)==="leaf")throw this.invalidCacheError();let o=c;if("branch"!==(c=null!==(l=c=o?o.branches.get(d):e)&&void 0!==l?l:{type:"branch",nodeKey:t,parent:o,branches:new Map,branchKey:d}).type||c.nodeKey!==t)throw this.invalidCacheError();null==o||o.branches.set(d,c),null==n||null===(s=n.onNodeVisit)||void 0===s||s.call(n,c),d=this._mapNodeValue(r),this._root=null!==(u=this._root)&&void 0!==u?u:c}let f=c?null===(r=c)||void 0===r?void 0:r.branches.get(d):this._root;if(null!=f&&("leaf"!==f.type||f.branchKey!==d))throw this.invalidCacheError();let h={type:"leaf",value:t,parent:c,branchKey:d};null===(o=c)||void 0===o||o.branches.set(d,h),this._root=null!==(i=this._root)&&void 0!==i?i:h,this._numLeafs++,this._onSet(h),null==n||null===(a=n.onNodeVisit)||void 0===a||a.call(n,h)};try{r()}catch(e){if(e instanceof rj)this.clear(),r();else throw e}}delete(e){let t=this.root();if(!t)return!1;if(e===t)return this._root=null,this._numLeafs=0,!0;let n=e.parent,r=e.branchKey;for(;n;){var o;if(n.branches.delete(r),n===t)return 0===n.branches.size?(this._root=null,this._numLeafs=0):this._numLeafs--,!0;if(n.branches.size>0)break;r=null===(o=n)||void 0===o?void 0:o.branchKey,n=n.parent}for(;n!==t;n=n.parent)if(null==n)return!1;return this._numLeafs--,!0}clear(){this._numLeafs=0,this._root=null}invalidCacheError(){throw L((rK()?"Possible Fast Refresh module reload detected.  This may also be caused by an selector returning inconsistent values. Resetting cache.":"Invalid cache values.  This happens when selectors do not return consistent values for the same input dependency values.  That may also be caused when using Fast Refresh to change a selector implementation.  Resetting cache.")+(null!=this._name?` - ${this._name}`:"")),new rj}}var rq=Object.freeze({__proto__:null,TreeCache:{TreeCache:rH}.TreeCache});class rZ{constructor(e){var t;p(this,"_maxSize",void 0),p(this,"_size",void 0),p(this,"_head",void 0),p(this,"_tail",void 0),p(this,"_map",void 0),p(this,"_keyMapper",void 0),this._maxSize=e.maxSize,this._size=0,this._head=null,this._tail=null,this._map=new Map,this._keyMapper=null!==(t=e.mapKey)&&void 0!==t?t:e=>e}head(){return this._head}tail(){return this._tail}size(){return this._size}maxSize(){return this._maxSize}has(e){return this._map.has(this._keyMapper(e))}get(e){let t=this._keyMapper(e),n=this._map.get(t);if(n)return this.set(e,n.value),n.value}set(e,t){let n=this._keyMapper(e);this._map.get(n)&&this.delete(e);let r=this.head(),o={key:e,right:r,left:null,value:t};r?r.left=o:this._tail=o,this._map.set(n,o),this._head=o,this._size++,this._maybeDeleteLRU()}_maybeDeleteLRU(){this.size()>this.maxSize()&&this.deleteLru()}deleteLru(){let e=this.tail();e&&this.delete(e.key)}delete(e){let t=this._keyMapper(e);if(!this._size||!this._map.has(t))return;let n=h(this._map.get(t)),r=n.right,o=n.left;r&&(r.left=n.left),o&&(o.right=n.right),n===this.head()&&(this._head=r),n===this.tail()&&(this._tail=o),this._map.delete(t),this._size--}clear(){this._size=0,this._head=null,this._tail=null,this._map=new Map}}var rY=Object.freeze({__proto__:null,LRUCache:{LRUCache:rZ}.LRUCache});let{LRUCache:rJ}=rY,{TreeCache:rX}=rq;var rQ=function({name:e,maxSize:t,mapNodeValue:n=e=>e}){let r=new rJ({maxSize:t}),o=new rX({name:e,mapNodeValue:n,onHit:e=>{r.set(e,!0)},onSet:e=>{let n=r.tail();r.set(e,!0),n&&o.size()>t&&o.delete(n.key)}});return o},r0=function(e,t={allowFunctions:!1}){return function e(t,n,r){if("string"==typeof t&&!t.includes('"')&&!t.includes("\\"))return`"${t}"`;switch(typeof t){case"undefined":return"";case"boolean":return t?"true":"false";case"number":case"symbol":return String(t);case"string":return JSON.stringify(t);case"function":if((null==n?void 0:n.allowFunctions)!==!0)throw d("Attempt to serialize function in a Recoil cache key");return`__FUNCTION(${t.name})__`}if(null===t)return"null";if("object"!=typeof t){var o;return null!==(o=JSON.stringify(t))&&void 0!==o?o:""}if(f(t))return"__PROMISE__";if(Array.isArray(t))return`[${t.map((t,r)=>e(t,n,r.toString()))}]`;if("function"==typeof t.toJSON)return e(t.toJSON(r),n,r);if(t instanceof Map){let o={};for(let[r,i]of t)o["string"==typeof r?r:e(r,n)]=i;return e(o,n,r)}return t instanceof Set?e(Array.from(t).sort((t,r)=>e(t,n).localeCompare(e(r,n))),n,r):void 0!==Symbol&&null!=t[Symbol.iterator]&&"function"==typeof t[Symbol.iterator]?e(Array.from(t),n,r):`{${Object.keys(t).filter(e=>void 0!==t[e]).sort().map(r=>`${e(r,n)}:${e(t[r],n,r)}`).join(",")}}`}(e,t)};let{TreeCache:r1}=rq,r2={equality:"reference",eviction:"keep-all",maxSize:1/0},{isReactNative:r5,isWindow:r4}=tl,{isLoadable:r3,loadableWithError:r8,loadableWithPromise:r9,loadableWithValue:r6}=A,{WrappedValue:r7}=rW,{getNodeLoadable:oe,peekNodeLoadable:ot,setNodeValue:on}=ek,{saveDepsToStore:or}=eo,{DEFAULT_VALUE:oo,getConfigDeletionHandler:oi,getNode:oa,registerNode:ol}=q,{isRecoilValue:os}=O,{markRecoilValueModified:ou}=eX,{retainedByOptionWithDefault:oc}=e6,{recoilCallback:od}=rD,{startPerfBlock:of}={startPerfBlock:function(e){return()=>null}};class oh{}let op=new oh,ov=[],o_=new Map,oy=(r=0,()=>r++);function om(e){let t=null,{key:n,get:r,cachePolicy_UNSTABLE:o}=e,i=null!=e.set?e.set:void 0,a=new Set,l=function({equality:e=r2.equality,eviction:t=r2.eviction,maxSize:n=r2.maxSize}=r2,r){return function(e,t,n,r){switch(e){case"keep-all":return new r1({name:r,mapNodeValue:n});case"lru":return rQ({name:r,maxSize:h(t),mapNodeValue:n});case"most-recent":return rQ({name:r,maxSize:1,mapNodeValue:n})}throw d(`Unrecognized eviction policy ${e}`)}(t,n,function(e){switch(e){case"reference":return e=>e;case"value":return e=>r0(e)}throw d(`Unrecognized equality policy ${e}`)}(e),r)}(null!=o?o:{equality:"reference",eviction:"keep-all"},n),s=oc(e.retainedBy_UNSTABLE),u=new Map,c=0;function p(){return!N("recoil_memory_managament_2020")||c>0}function v(e){return e.getState().knownSelectors.add(n),c++,()=>{c--}}function _(){return void 0!==oi(n)&&!p()}function y(e,t){k(e,t)&&E(e),m(t,!0)}function m(e,n){let r=o_.get(e);if(null!=r){for(let e of r)ou(e,h(t));n&&o_.delete(e)}}function S(e,t){let n=o_.get(t);null==n&&o_.set(t,n=new Set),n.add(e)}function g(e,t,n,r,o,i){return t.then(r=>{if(!p())throw E(e),op;null!=i.loadingDepKey&&i.loadingDepPromise===t?n.atomValues.set(i.loadingDepKey,r6(r)):e.getState().knownSelectors.forEach(e=>{n.atomValues.delete(e)});let a=b(e,n);if(a&&"loading"!==a.state){if((k(e,o)||null==A(e))&&y(e,o),"hasValue"===a.state)return a.contents;throw a.contents}if(!k(e,o)){let t=w(e,n);if(null!=t)return t.loadingLoadable.contents}let[l,s]=T(e,n,o);if("loading"!==l.state&&(V(n,l,s),y(e,o)),"hasError"===l.state)throw l.contents;return l.contents}).catch(t=>{if(t instanceof oh)throw op;if(!p())throw E(e),op;throw V(n,r8(t),r),y(e,o),t})}function R(e,t,r,o){var i,l,s,u,c,d,f;for(let h of((k(e,o)||t.version===(null===(i=e.getState())||void 0===i?void 0:null===(l=i.currentTree)||void 0===l?void 0:l.version)||t.version===(null===(s=e.getState())||void 0===s?void 0:null===(u=s.nextTree)||void 0===u?void 0:u.version))&&or(n,r,e,null!==(c=null===(d=e.getState())||void 0===d?void 0:null===(f=d.nextTree)||void 0===f?void 0:f.version)&&void 0!==c?c:e.getState().currentTree.version),r))a.add(h)}function T(e,o,i){let a,l;let s=of(n),u=!0,c=!0,v=()=>{s(),c=!1},_=!1,S={loadingDepKey:null,loadingDepPromise:null},T=new Map;function b({key:t}){let n=oe(e,o,t);switch(T.set(t,n),!u&&(R(e,o,new Set(T.keys()),i),k(e,i)&&(h(A(e)).stateVersions.clear(),m(i,!1))),n.state){case"hasValue":return n.contents;case"hasError":throw n.contents;case"loading":throw S.loadingDepKey=t,S.loadingDepPromise=n.contents,n.contents}throw d("Invalid Loadable state")}try{(a=r({get:b,getCallback:n=>(...r)=>{if(c)throw d("Callbacks from getCallback() should only be called asynchronously after the selector is evalutated.  It can be used for selectors to return objects with callbacks that can work with Recoil state without a subscription.");return null!=t||rb(!1),od(e,n,r,{node:t})}}),a=os(a)?b(a):a,r3(a)&&("hasError"===a.state&&(_=!0),a=a.contents),f(a))?a=a.then(t=>{if(!p())throw E(e),op;let n=r6(t);return V(o,n,T),y(e,i),t}).catch(t=>{if(!p())throw E(e),op;if(f(t))return g(e,t,o,T,i,S);let n=r8(t);throw V(o,n,T),y(e,i),t}).finally(v):v(),a=a instanceof r7?a.value:a}catch(t){f(a=t)?a=g(e,a,o,T,i,S).finally(v):(_=!0,v())}return l=_?r8(a):f(a)?r9(a):r6(a),u=!1,function(e,t,n){if(k(e,t)){let t=A(e);null!=t&&(t.depValuesDiscoveredSoFarDuringAsyncWork=n)}}(e,i,T),R(e,o,new Set(T.keys()),i),[l,T]}function b(e,t){let r=t.atomValues.get(n);if(null!=r)return r;let o=new Set;try{r=l.get(n=>("string"!=typeof n&&rb(!1),oe(e,t,n).contents),{onNodeVisit:e=>{"branch"===e.type&&e.nodeKey!==n&&o.add(e.nodeKey)}})}catch(e){throw d(`Problem with cache lookup for selector "${n}": ${e.message}`)}if(r){var i;t.atomValues.set(n,r),R(e,t,o,null===(i=A(e))||void 0===i?void 0:i.executionID)}return r}function w(e,t){for(let n of ti([u.has(e)?[h(u.get(e))]:[],x(ey(u,([t])=>t!==e),([,e])=>e)])){if(n.stateVersions.get(t.version)||!function(n){for(let[r,o]of n)if(!oe(e,t,r).is(o))return!0;return!1}(n.depValuesDiscoveredSoFarDuringAsyncWork))return n.stateVersions.set(t.version,!0),n;n.stateVersions.set(t.version,!1)}}function A(e){return u.get(e)}function E(e){u.delete(e)}function k(e,t){var n;return t===(null===(n=A(e))||void 0===n?void 0:n.executionID)}function V(e,t,r){e.atomValues.set(n,t);try{l.set(Array.from(r.entries()).map(([e,t])=>[e,t.contents]),t)}catch(e){throw d(`Problem with setting cache for selector "${n}": ${e.message}`)}}function C(e,t){let r=t.atomValues.get(n);return null!=r?r:l.get(n=>{var r;return"string"!=typeof n&&rb(!1),null===(r=ot(e,t,n))||void 0===r?void 0:r.contents})}function M(e,t){return function(e){if(ov.includes(n))return r8(d(`Recoil selector has circular dependencies: ${ov.slice(ov.indexOf(n)).join(" → ")}`));ov.push(n);try{return e()}finally{ov.pop()}}(()=>(function(e,t){var n;let r=b(e,t);if(null!=r)return E(e),r;let o=w(e,t);if(null!=o)return(null===(n=o.loadingLoadable)||void 0===n?void 0:n.state)==="loading"&&S(e,o.executionID),o.loadingLoadable;let i=oy(),[a,l]=T(e,t,i);return"loading"===a.state?(u.set(e,{depValuesDiscoveredSoFarDuringAsyncWork:l,executionID:i,loadingLoadable:a,stateVersions:new Map([[t.version,!0]])}),S(e,i)):(E(e),V(t,a,l)),a})(e,t))}function U(e){e.atomValues.delete(n)}function I(e,n){for(let o of(null!=t||rb(!1),a)){var r;let t=oa(o);null===(r=t.clearCache)||void 0===r||r.call(t,e,n)}a.clear(),U(n),l.clear(),ou(e,t)}return t=null!=i?ol({key:n,nodeType:"selector",peek:C,get:M,set:(e,t,r)=>{let o=!1,a=new Map;function l({key:r}){if(o)throw d("Recoil: Async selector sets are not currently supported.");let i=oe(e,t,r);if("hasValue"===i.state)return i.contents;if("loading"===i.state){let e=`Getting value of asynchronous atom or selector "${r}" in a pending state while setting selector "${n}" is not yet supported.`;throw L(e),d(e)}throw i.contents}function s(n,r){if(o){let e="Recoil: Async selector sets are not currently supported.";throw L(e),d(e)}let i="function"==typeof r?r(l(n)):r;on(e,t,n.key,i).forEach((e,t)=>a.set(t,e))}let u=i({set:s,get:l,reset:function(e){s(e,oo)}},r);if(void 0!==u)throw f(u)?d("Recoil: Async selector sets are not currently supported."):d("Recoil: selector set should be a void function.");return o=!0,a},init:v,invalidate:U,clearCache:I,shouldDeleteConfigOnRelease:_,dangerouslyAllowMutability:e.dangerouslyAllowMutability,shouldRestoreFromSnapshots:!1,retainedBy:s}):ol({key:n,nodeType:"selector",peek:C,get:M,init:v,invalidate:U,clearCache:I,shouldDeleteConfigOnRelease:_,dangerouslyAllowMutability:e.dangerouslyAllowMutability,shouldRestoreFromSnapshots:!1,retainedBy:s})}om.value=e=>new r7(e);let{isLoadable:oS,loadableWithError:og,loadableWithPromise:oR,loadableWithValue:oT}=A,{WrappedValue:ob}=rW,{peekNodeInfo:ow}=ek,{DEFAULT_VALUE:oA,DefaultValue:oE,getConfigDeletionHandler:oN,registerNode:oL,setConfigDeletionHandler:ok}=q,{isRecoilValue:oV}=O,{getRecoilValueAsLoadable:oC,markRecoilValueModified:oM,setRecoilValue:oU,setRecoilValueLoadable:oI}=eX,{retainedByOptionWithDefault:oB}=e6,oD=e=>e instanceof ob?e.value:e;function oO(e){let{...t}=e,n="default"in e?e.default:new Promise(()=>{});return oV(n)?function(e){let t=oO({...e,default:oA,persistence_UNSTABLE:void 0===e.persistence_UNSTABLE?void 0:{...e.persistence_UNSTABLE,validator:t=>t instanceof oE?t:h(e.persistence_UNSTABLE).validator(t,oA)},effects:e.effects,effects_UNSTABLE:e.effects_UNSTABLE}),n=om({key:`${e.key}__withFallback`,get:({get:n})=>{let r=n(t);return r instanceof oE?e.default:r},set:({set:e},n)=>e(t,n),cachePolicy_UNSTABLE:{eviction:"most-recent"},dangerouslyAllowMutability:e.dangerouslyAllowMutability});return ok(n.key,oN(e.key)),n}({...t,default:n}):function(e){var t;let n;let{key:r,persistence_UNSTABLE:o}=e,i=oB(e.retainedBy_UNSTABLE),a=0;function l(e){return oR(e.then(e=>(s=oT(e),e)).catch(e=>{throw s=og(e),e}))}let s=f(e.default)?l(e.default):oS(e.default)?"loading"===e.default.state?l(e.default.contents):e.default:oT(oD(e.default));s.contents;let u=new Map;function c(e,t){var o,i;return null!==(o=null!==(i=t.atomValues.get(r))&&void 0!==i?i:n)&&void 0!==o?o:s}let p=oL({key:r,nodeType:"atom",peek:c,get:function(e,t){if(t.atomValues.has(r))return h(t.atomValues.get(r));if(!t.nonvalidatedAtoms.has(r))return s;{if(null!=n)return n;if(null==o)return P(`Tried to restore a persisted value for atom ${r} but it has no persistence settings.`),s;let e=t.nonvalidatedAtoms.get(r),i=o.validator(e,oA);return n=i instanceof oE?s:oT(i)}},set:function(e,t,o){if(t.atomValues.has(r)){let e=h(t.atomValues.get(r));if("hasValue"===e.state&&o===e.contents)return new Map}else if(!t.nonvalidatedAtoms.has(r)&&o instanceof oE)return new Map;return n=void 0,new Map().set(r,oT(o))},init:function(t,n,o){var i,l,h;a++,t.getState().knownAtoms.add(r),"loading"===s.state&&s.contents.finally(()=>{var e;(null!==(e=t.getState().nextTree)&&void 0!==e?e:t.getState().currentTree).atomValues.has(r)||oM(t,p)});let v=null!==(i=e.effects)&&void 0!==i?i:e.effects_UNSTABLE;if(null!=v){let e=oA,i=!0,a=!1,S=null;function _(o){if(i&&o.key===r){let r=e;return r instanceof oE?c(t,n):f(r)?oR(r.then(e=>e instanceof oE?s.toPromise():e)):oT(r)}return oC(t,o)}function y(e){return _(e).toPromise()}function m(n){var o;let a=ow(t,null!==(o=t.getState().nextTree)&&void 0!==o?o:t.getState().currentTree,n.key);return!i||n.key!==r||e instanceof oE?a:{...a,isSet:!0,loadable:_(n)}}let g=n=>r=>{if(i){let t=_(p),o="hasValue"===t.state?t.contents:oA;f(e="function"==typeof r?r(o):r)&&(e=e.then(e=>(S={effect:n,value:e},e)))}else{if(f(r))throw d("Setting atoms to async values is not implemented.");"function"!=typeof r&&(S={effect:n,value:oD(r)}),oU(t,p,"function"==typeof r?e=>{let t=oD(r(e));return S={effect:n,value:t},t}:oD(r))}},R=e=>()=>g(e)(oA),T=e=>n=>{var o;let{release:i}=t.subscribeToTransactions(t=>{var o,i,a,l,u;let{currentTree:c,previousTree:d}=t.getState();d||(L("Transaction subscribers notified without a next tree being present -- this is a bug in Recoil"),d=c);let f=null!==(o=c.atomValues.get(r))&&void 0!==o?o:s;if("hasValue"===f.state){let t=f.contents,o=null!==(i=d.atomValues.get(r))&&void 0!==i?i:s,h="hasValue"===o.state?o.contents:oA;(null===(a=S)||void 0===a?void 0:a.effect)!==e||(null===(l=S)||void 0===l?void 0:l.value)!==t?n(t,h,!c.atomValues.has(r)):(null===(u=S)||void 0===u?void 0:u.effect)===e&&(S=null)}},r);u.set(t,[...null!==(o=u.get(t))&&void 0!==o?o:[],i])};for(let n of v)try{let e=n({node:p,storeID:t.storeID,parentStoreID_UNSTABLE:t.parentStoreID,trigger:o,setSelf:g(n),resetSelf:R(n),onSet:T(n),getPromise:y,getLoadable:_,getInfo_UNSTABLE:m});null!=e&&u.set(t,[...null!==(l=u.get(t))&&void 0!==l?l:[],e])}catch(t){e=t,a=!0}if(i=!1,!(e instanceof oE)){let o=a?og(e):f(e)?oR(function(e,t){let n=t.then(t=>{var o,i;return(null===(i=(null!==(o=e.getState().nextTree)&&void 0!==o?o:e.getState().currentTree).atomValues.get(r))||void 0===i?void 0:i.contents)===n&&oU(e,p,t),t}).catch(t=>{var o,i;throw(null===(i=(null!==(o=e.getState().nextTree)&&void 0!==o?o:e.getState().currentTree).atomValues.get(r))||void 0===i?void 0:i.contents)===n&&oI(e,p,og(t)),t});return n}(t,e)):oT(oD(e));o.contents,n.atomValues.set(r,o),null===(h=t.getState().nextTree)||void 0===h||h.atomValues.set(r,o)}}return()=>{var e;a--,null===(e=u.get(t))||void 0===e||e.forEach(e=>e()),u.delete(t)}},invalidate:function(){n=void 0},shouldDeleteConfigOnRelease:function(){return void 0!==oN(r)&&a<=0},dangerouslyAllowMutability:e.dangerouslyAllowMutability,persistence_UNSTABLE:e.persistence_UNSTABLE?{type:e.persistence_UNSTABLE.type,backButton:e.persistence_UNSTABLE.backButton}:void 0,shouldRestoreFromSnapshots:!0,retainedBy:i});return p}({...t,default:n})}oO.value=e=>new ob(e);class oP{constructor(e){var t;p(this,"_map",void 0),p(this,"_keyMapper",void 0),this._map=new Map,this._keyMapper=null!==(t=null==e?void 0:e.mapKey)&&void 0!==t?t:e=>e}size(){return this._map.size}has(e){return this._map.has(this._keyMapper(e))}get(e){return this._map.get(this._keyMapper(e))}set(e,t){this._map.set(this._keyMapper(e),t)}delete(e){this._map.delete(this._keyMapper(e))}clear(){this._map.clear()}}var ox=Object.freeze({__proto__:null,MapCache:{MapCache:oP}.MapCache});let{LRUCache:oF}=rY,{MapCache:oz}=ox,oG={equality:"reference",eviction:"none",maxSize:1/0};var o$=function({equality:e=oG.equality,eviction:t=oG.eviction,maxSize:n=oG.maxSize}=oG){return function(e,t,n){switch(e){case"keep-all":return new oz({mapKey:n});case"lru":return new oF({mapKey:n,maxSize:h(t)});case"most-recent":return new oF({mapKey:n,maxSize:1})}throw d(`Unrecognized eviction policy ${e}`)}(t,n,function(e){switch(e){case"reference":return e=>e;case"value":return e=>r0(e)}throw d(`Unrecognized equality policy ${e}`)}(e))};let{setConfigDeletionHandler:oW}=q,{setConfigDeletionHandler:oK}=q,oj=0;var oH=function(e){var t,n;let r=o$({equality:null!==(t=null===(n=e.cachePolicyForParams_UNSTABLE)||void 0===n?void 0:n.equality)&&void 0!==t?t:"value",eviction:"keep-all"});return t=>{var n;let o,i;try{o=r.get(t)}catch(t){throw d(`Problem with cache lookup for selector ${e.key}: ${t.message}`)}if(null!=o)return o;let a=`${e.key}__selectorFamily/${null!==(n=r0(t,{allowFunctions:!0}))&&void 0!==n?n:"void"}/${oj++}`,l=n=>e.get(t)(n),s=e.cachePolicy_UNSTABLE,u="function"==typeof e.retainedBy_UNSTABLE?e.retainedBy_UNSTABLE(t):e.retainedBy_UNSTABLE;if(null!=e.set){let n=e.set;i=om({key:a,get:l,set:(e,r)=>n(t)(e,r),cachePolicy_UNSTABLE:s,dangerouslyAllowMutability:e.dangerouslyAllowMutability,retainedBy_UNSTABLE:u})}else i=om({key:a,get:l,cachePolicy_UNSTABLE:s,dangerouslyAllowMutability:e.dangerouslyAllowMutability,retainedBy_UNSTABLE:u});return r.set(t,i),oK(i.key,()=>{r.delete(t)}),i}};let oq=oH({key:"__constant",get:e=>()=>e,cachePolicyForParams_UNSTABLE:{equality:"reference"}}),oZ=oH({key:"__error",get:e=>()=>{throw d(e)},cachePolicyForParams_UNSTABLE:{equality:"reference"}}),{loadableWithError:oY,loadableWithPromise:oJ,loadableWithValue:oX}=A;function oQ(e,t){let n=Array(t.length).fill(void 0),r=Array(t.length).fill(void 0);for(let[o,i]of t.entries())try{n[o]=e(i)}catch(e){r[o]=e}return[n,r]}function o0(e){return null!=e&&!f(e)}function o1(e){return Array.isArray(e)?e:Object.getOwnPropertyNames(e).map(t=>e[t])}function o2(e,t){return Array.isArray(e)?t:Object.getOwnPropertyNames(e).reduce((e,n,r)=>({...e,[n]:t[r]}),{})}function o5(e,t,n){return o2(e,n.map((e,n)=>null==e?oX(t[n]):f(e)?oJ(e):oY(e)))}let o4=oH({key:"__waitForNone",get:e=>({get:t})=>{let[n,r]=oQ(t,o1(e));return o5(e,n,r)},dangerouslyAllowMutability:!0}),o3=oH({key:"__waitForAny",get:e=>({get:t})=>{let[n,r]=oQ(t,o1(e));return r.some(e=>!f(e))?o5(e,n,r):new Promise(t=>{for(let[o,i]of r.entries())f(i)&&i.then(i=>{n[o]=i,r[o]=void 0,t(o5(e,n,r))}).catch(i=>{r[o]=i,t(o5(e,n,r))})})},dangerouslyAllowMutability:!0}),o8=oH({key:"__waitForAll",get:e=>({get:t})=>{let[n,r]=oQ(t,o1(e));if(r.every(e=>null==e))return o2(e,n);let o=r.find(o0);if(null!=o)throw o;return Promise.all(r).then(t=>o2(e,t.map((e,t)=>void 0===e?n[t]:e)))},dangerouslyAllowMutability:!0}),o9=oH({key:"__waitForAllSettled",get:e=>({get:t})=>{let[n,r]=oQ(t,o1(e));return r.every(e=>!f(e))?o5(e,n,r):Promise.all(r.map((e,t)=>f(e)?e.then(e=>{n[t]=e,r[t]=void 0}).catch(e=>{n[t]=void 0,r[t]=e}):null)).then(()=>o5(e,n,r))},dangerouslyAllowMutability:!0}),o6=oH({key:"__noWait",get:e=>({get:t})=>{try{return om.value(oX(t(e)))}catch(e){return om.value(f(e)?oJ(e):oY(e))}},dangerouslyAllowMutability:!0}),{RecoilLoadable:o7}=A,{DefaultValue:ie}=q,{RecoilRoot:it,useRecoilStoreID:ir}=no,{isRecoilValue:io}=O,{retentionZone:ii}=ev,{freshSnapshot:ia}=tC,{useRecoilState:il,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:is,useRecoilStateLoadable:iu,useRecoilValue:ic,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:id,useRecoilValueLoadable:ih,useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:ip,useResetRecoilState:iv,useSetRecoilState:i_}={recoilComponentGetRecoilValueCount_FOR_TESTING:{current:0},useRecoilInterface:function(){let e=ny(),t=nA(),[,n]=nD([]),r=nB(new Set);r.current=new Set;let o=nB(new Set),i=nB(new Map),a=nM(e=>{let t=i.current.get(e);t&&(t.release(),i.current.delete(e))},[i]),l=nM((e,t)=>{i.current.has(t)&&n([])},[]);return nU(()=>{let n=t.current;et(r.current,o.current).forEach(t=>{if(i.current.has(t)){P(`Double subscription to RecoilValue "${t}"`);return}let r=nC(n,new nN(t),e=>l(e,t),e);i.current.set(t,r),n.getState().nextTree?n.getState().queuedComponentCallbacks_DEPRECATED.push(()=>{l(n.getState(),t)}):l(n.getState(),t)}),et(o.current,r.current).forEach(e=>{a(e)}),o.current=r.current}),nU(()=>{let n=i.current;return et(r.current,new Set(n.keys())).forEach(r=>{let o=nC(t.current,new nN(r),e=>l(e,r),e);n.set(r,o)}),()=>n.forEach((e,t)=>a(t))},[e,t,a,l]),nI(()=>{function e(e){return n=>{nk(t.current,e,n)}}function n(e){var n;r.current.has(e.key)||(r.current=nO(r.current,e.key));let o=t.current.getState();return nL(t.current,e,nR().early&&null!==(n=o.nextTree)&&void 0!==n?n:o.currentTree)}function o(e){return nx(n(e),e,t)}return{getRecoilValue:o,getRecoilValueLoadable:n,getRecoilState:function(t){return[o(t),e(t)]},getRecoilStateLoadable:function(t){return[n(t),e(t)]},getSetRecoilState:e,getResetRecoilState:function(e){return()=>nk(t.current,e,nS)}}},[r,t])},useRecoilState:function(e){return[nK(e),nj(e)]},useRecoilStateLoadable:function(e){return[nW(e),nj(e)]},useRecoilValue:nK,useRecoilValueLoadable:nW,useResetRecoilState:function(e){let t=nA();return nM(()=>{nk(t.current,e,nS)},[t,e])},useSetRecoilState:nj,useSetUnvalidatedAtomValues:function(){let e=nA();return(t,n={})=>{nm(()=>{e.current.addTransactionMetadata(n),t.forEach((t,n)=>nV(e.current,new nN(n),t))})}},useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:nH,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:nq,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:function(e){return[nq(e),nj(e)]}},{useGotoRecoilSnapshot:iy,useRecoilSnapshot:im,useRecoilTransactionObserver:iS}=ri,{useRecoilCallback:ig}=rD,{noWait:iR,waitForAll:iT,waitForAllSettled:ib,waitForAny:iw,waitForNone:iA}={waitForNone:o4,waitForAny:o3,waitForAll:o8,waitForAllSettled:o9,noWait:o6};var iE={DefaultValue:ie,isRecoilValue:io,RecoilLoadable:o7,RecoilEnv:E,RecoilRoot:it,useRecoilStoreID:ir,useRecoilBridgeAcrossReactRoots_UNSTABLE:function(){"MUTABLE_SOURCE"===rs().mode&&console.warn("Warning: There are known issues using useRecoilBridgeAcrossReactRoots() in recoil_mutable_source rendering mode.  Please consider upgrading to recoil_sync_external_store mode.");let e=rc().current;return rd(()=>function({children:t}){return s.createElement(ru,{store_INTERNAL:e},t)},[e])},atom:oO,selector:om,atomFamily:function(e){var t,n;let r=o$({equality:null!==(t=null===(n=e.cachePolicyForParams_UNSTABLE)||void 0===n?void 0:n.equality)&&void 0!==t?t:"value",eviction:"keep-all"});return t=>{var n,o;let i=r.get(t);if(null!=i)return i;let{cachePolicyForParams_UNSTABLE:a,...l}=e,s="default"in e?e.default:new Promise(()=>{}),u=oO({...l,key:`${e.key}__${null!==(n=r0(t))&&void 0!==n?n:"void"}`,default:"function"==typeof s?s(t):s,retainedBy_UNSTABLE:"function"==typeof e.retainedBy_UNSTABLE?e.retainedBy_UNSTABLE(t):e.retainedBy_UNSTABLE,effects:"function"==typeof e.effects?e.effects(t):"function"==typeof e.effects_UNSTABLE?e.effects_UNSTABLE(t):null!==(o=e.effects)&&void 0!==o?o:e.effects_UNSTABLE});return r.set(t,u),oW(u.key,()=>{r.delete(t)}),u}},selectorFamily:oH,constSelector:function(e){return oq(e)},errorSelector:function(e){return oZ(e)},readOnlySelector:function(e){return e},noWait:iR,waitForNone:iA,waitForAny:iw,waitForAll:iT,waitForAllSettled:ib,useRecoilValue:ic,useRecoilValueLoadable:ih,useRecoilState:il,useRecoilStateLoadable:iu,useSetRecoilState:i_,useResetRecoilState:iv,useGetRecoilValueInfo_UNSTABLE:function(){let e=rl();return({key:t})=>ra(e.current,e.current.getState().currentTree,t)},useRecoilRefresher_UNSTABLE:function(e){let t=rO();return rx(()=>{rP(t.current,e)},[e,t])},useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE:ip,useRecoilValue_TRANSITION_SUPPORT_UNSTABLE:id,useRecoilState_TRANSITION_SUPPORT_UNSTABLE:is,useRecoilCallback:ig,useRecoilTransaction_UNSTABLE:function(e,t){let n=rz();return rG(()=>(...t)=>{rF(n.current)(n=>{e(n)(...t)})},null!=t?[...t,n]:void 0)},useGotoRecoilSnapshot:iy,useRecoilSnapshot:im,useRecoilTransactionObserver_UNSTABLE:iS,snapshot_UNSTABLE:ia,useRetain:n_,retentionZone:ii},iN=iE.DefaultValue,iL=iE.isRecoilValue,ik=iE.RecoilLoadable,iV=iE.RecoilEnv,iC=iE.RecoilRoot,iM=iE.useRecoilStoreID,iU=iE.useRecoilBridgeAcrossReactRoots_UNSTABLE,iI=iE.atom,iB=iE.selector,iD=iE.atomFamily,iO=iE.selectorFamily,iP=iE.constSelector,ix=iE.errorSelector,iF=iE.readOnlySelector,iz=iE.noWait,iG=iE.waitForNone,i$=iE.waitForAny,iW=iE.waitForAll,iK=iE.waitForAllSettled,ij=iE.useRecoilValue,iH=iE.useRecoilValueLoadable,iq=iE.useRecoilState,iZ=iE.useRecoilStateLoadable,iY=iE.useSetRecoilState,iJ=iE.useResetRecoilState,iX=iE.useGetRecoilValueInfo_UNSTABLE,iQ=iE.useRecoilRefresher_UNSTABLE,i0=iE.useRecoilValueLoadable_TRANSITION_SUPPORT_UNSTABLE,i1=iE.useRecoilValue_TRANSITION_SUPPORT_UNSTABLE,i2=iE.useRecoilState_TRANSITION_SUPPORT_UNSTABLE,i5=iE.useRecoilCallback,i4=iE.useRecoilTransaction_UNSTABLE,i3=iE.useGotoRecoilSnapshot,i8=iE.useRecoilSnapshot,i9=iE.useRecoilTransactionObserver_UNSTABLE,i6=iE.snapshot_UNSTABLE,i7=iE.useRetain,ae=iE.retentionZone;t.default=iE}}]);