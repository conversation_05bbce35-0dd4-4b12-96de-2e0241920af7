"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5752,6511],{39952:function(e,t,a){a.d(t,{I:function(){return l}});var r=a(15236);a(97175);var n=a(75735);let s=(0,r.ZF)({apiKey:"AIzaSyDa9-0wJAofNhhgK9KVHdbofs_tkVzNjh0",authDomain:"secret-desires-67796.firebaseapp.com",projectId:"secret-desires-67796",storageBucket:"secret-desires-67796.appspot.com",messagingSenderId:"************",appId:"1:************:web:4c3052c228929ac0bdbe44",measurementId:"G-WS929YTP5C"}),l=(0,n.v0)(s)},5207:function(e,t,a){var r=a(57437),n=a(16463);t.default=e=>{let{icon:t,text:a}=e,s=(0,n.useRouter)();return(0,r.jsxs)("button",{"aria-label":a,onClick:()=>{switch(a){case"Start Chatting":s.push("/");break;case"Create Character":case"Create your partner":case"Start Creating":localStorage.removeItem("character"),s.push("/create");break;case"Go to Guide":s.push("/guide")}},className:"flex h-[40px] gap-2 justify-center max-w-fit      items-center px-3 rounded-xl bg-linear-to-b from-[#d52a5d] to-[#bc1f4e] hover:from-[#b02851] hover:to-[#b02851]",children:[t&&t,(0,r.jsx)("span",{className:"font-bold text-base text-nowrap",children:a})]})}},48053:function(e,t,a){a.d(t,{Z:function(){return o}});var r=a(57437),n=a(66648),s=a(5207),l=a(3114),i=a(87138);function o(e){let{title1:t,title2:a,selectCharBtn:o}=e,c=(0,l.P)();return(0,r.jsxs)("div",{className:" flex sm:flex-row flex-col md:p-2 sm:p-1   sm:gap-6 gap-4 items-center justify-center",children:[(0,r.jsxs)(i.default,{href:"/create",className:"relative 2xl:w-80 sLaptop:w-72 sm:w-60 w-60 rounded-lg overflow-hidden cursor-pointer",children:[(0,r.jsx)(n.default,{alt:"Character portrait",className:"w-full object-cover",height:"500",width:"500",src:"/images/CreateCharacterIMG.webp"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4",children:(0,r.jsx)("p",{className:"text-white ",children:"Enter her name:"})})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 text-white items-start  justify-center  cursor-pointer sm:w-fit md:max-w-96 sm:max-w-72 w-64",children:[(0,r.jsxs)(i.default,{href:"/create",className:"flex flex-col sm:text-start text-center gap-y-2",children:[(0,r.jsx)("p",{style:{color:"#d62a5e",textShadow:".5px .5px .5px #c12655"},className:"font-medium text-primary text-nowrap sm:text-base text-sm",children:t}),(0,r.jsx)("h3",{className:" font-semibold md:text-xl text-lg text-wrap",children:a})]}),(0,r.jsxs)("div",{className:"flex md:flex-row flex-col sm:justify-start sm:items-start items-center gap-4 w-full",children:[(0,r.jsx)(i.default,{href:"/create",children:(0,r.jsx)(s.default,{icon:c.createCharIcon,text:"Create your partner"})}),o&&(0,r.jsx)(i.default,{href:"/chat",children:(0,r.jsx)(s.default,{icon:c.chatIcon,text:"Start Chatting"})})]})]})]})}},81733:function(e,t,a){var r=a(57437),n=a(2265),s=a(66648),l=a(44839),i=a(9962),o=a(94693),c=a(53541),d=a(36013),u=a(87497);let m=e=>{let{totalDashes:t,currentDash:a,progress:n}=e;return(0,r.jsx)("div",{className:"flex gap-1 w-full",children:Array.from({length:t},(e,t)=>(0,r.jsx)("div",{className:"flex-1 h-0.5 bg-white/30 rounded-full overflow-hidden",style:{minWidth:"8px"},children:(0,r.jsx)("div",{className:(0,l.Z)("h-full transition-all duration-100 ease-linear rounded-full",t<a?"bg-[#d52a5d] w-full":t===a?"bg-[#d52a5d]":"bg-transparent w-0"),style:{width:t===a?"".concat(n,"%"):t<a?"100%":"0%"}})},t))})},f=()=>(0,r.jsxs)("div",{role:"status",className:"relative min-w-[172px] w-full h-[470px] overflow-hidden rounded-2xl border border-[#262626]  animate-pulse",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-linear-to-r from-transparent via-white/20 to-transparent animate-shimmer"}),(0,r.jsx)("div",{className:"h-56 flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"w-12 h-12 text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"#262626",viewBox:"0 0 16 20",children:[(0,r.jsx)("path",{d:"M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM10.5 6a1.5 1.5 0 1 1 0 2.999A1.5 1.5 0 0 1 10.5 6Zm2.221 10.515a1 1 0 0 1-.858.485h-8a1 1 0 0 1-.9-1.43L5.6 10.039a.978.978 0 0 1 .936-.57 1 1 0 0 1 .9.632l1.181 2.981.541-1a.945.945 0 0 1 .883-.522 1 1 0 0 1 .879.529l1.832 3.438a1 1 0 0 1-.031.988Z"}),(0,r.jsx)("path",{d:"M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"})]})}),(0,r.jsxs)("div",{className:"absolute top-8 right-3 flex flex-col space-y-2 items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-[#262626] rounded-full"}),(0,r.jsx)("div",{className:"w-8 h-8 bg-[#262626] rounded-full"})]}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-3",children:[(0,r.jsx)("div",{className:"h-5 bg-[#262626] rounded w-40 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-[#262626] rounded w-full mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-[#262626] rounded w-3/4"}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-4",children:[(0,r.jsx)("div",{className:"h-8 w-24 bg-[#262626] rounded-full"}),(0,r.jsx)("div",{className:"h-8 w-24 bg-[#262626] rounded-full"})]})]}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]}),p=e=>{var t,a,n,l,u,m,f,p,x;let{character:h,size:g,isMyChar:v,nsfw_preference:y}=e;return(0,r.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent p-4",children:(0,r.jsxs)("div",{className:"text-white",children:[(0,r.jsx)("div",{className:"flex flex-row justify-between text-xl font-bold",children:(0,r.jsxs)(d.ll,{className:"flex w-full justify-between ".concat("large"===g?"text-md":"text-sm"," sm:text-2xl font-bold sm:mt-1 [text-shadow:2px_2px_2px_rgb(0_0_0/50%)]"),children:[(0,r.jsx)("h3",{className:"line-clamp-1",children:h.first_name+" "+h.last_name}),(0,r.jsx)(i.Z,{trigger:(0,r.jsx)("h3",{className:"[text-shadow:2px_2px_2px_rgb(0_0_0/50%)]",children:h.age}),content:"Age",side:"left",className:"py-0 font-normal"})]})}),(0,r.jsx)("p",{className:"".concat("large"===g?"text-[11px]":"text-[9px]"," sm:text-sm sm:mt-1 mt-0.5 line-clamp-2  [text-shadow:2px_2px_2px_rgb(0_0_0/50%)]"),children:null===(t=h.about_me)||void 0===t?void 0:t.replace(/\{\{user\}\}/g,"user").replace(/\{\{char\}\}/g,h.first_name||"character")}),(0,r.jsx)("div",{className:"show-desktop",children:(0,r.jsxs)("div",{className:"mt-2 hidden sm:flex flex-col flex-wrap sm:flex-row items-center gap-1",children:[h.nsfw&&!0===h.nsfw&&(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"w-max flex items-center bg-[#F50004] text-[10px] sm:text-xs truncate max-w-28 sm:max-w-fit",children:[(0,r.jsx)(s.default,{src:"/icons/fire.svg",alt:"fire",width:8,height:8,className:"mr-1"}),(0,r.jsx)("span",{children:"NSFW"})]}),content:"NSFW"}),!v&&(0,r.jsxs)(r.Fragment,{children:[!h.community&&(0,r.jsx)(i.Z,{trigger:(0,r.jsx)(o.C,{className:"text-[10px] sm:text-xs truncate max-w-28 sm:max-w-fit",children:null===(a=h.personality)||void 0===a?void 0:a.split("-")[0]}),content:"Personality: ".concat(null===(n=h.personality)||void 0===n?void 0:n.split("-")[0])}),y&&h.kink&&"Vanilla"!==h.kink&&!h.community&&(0,r.jsx)(i.Z,{trigger:(0,r.jsx)(o.C,{className:"text-[10px] sm:text-xs truncate max-w-28 sm:max-w-fit",children:null===(l=h.kink)||void 0===l?void 0:l.split("-")[0]}),content:"Kink: ".concat(null===(u=h.kink)||void 0===u?void 0:u.split("-")[0])})]}),!v&&(h.community&&h.public?(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"flex items-center gap-1 bg-[#33006F]/40 sm:text-xs mx-1 ".concat("large"==g?"text-[10px]":"text-[8px]"),children:[(0,r.jsx)(s.default,{src:"/icons/community.svg",alt:"community content",width:12,height:12}),(0,r.jsx)("span",{children:"Community"})]}),content:"Community Content"}):(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"flex items-center gap-1 bg-[#FFD700]/40 sm:text-xs mx-1 ".concat("large"==g?"text-[10px]":"text-[8px]"),children:[(0,r.jsx)(c.Z,{width:12,height:12}),(0,r.jsx)("span",{children:"Official"})]}),content:"Official Content"}))]})}),(0,r.jsx)("div",{className:"show-mobile",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[h.nsfw&&!0===h.nsfw&&(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"flex items-center bg-[#F50004]/40 sm:text-xs ".concat("large"==g?"text-[10px]":"text-[8px]"),children:[(0,r.jsx)("span",{children:"NSFW"}),(0,r.jsx)(s.default,{src:"/icons/fire.svg",alt:"fire",width:"large"==g?8:6,height:"large"==g?8:6,className:"ml-1"})]}),content:"NSFW"}),!v&&(h.community&&h.public?(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"flex items-center bg-[#33006F]/40 gap-1 sm:text-xs ".concat("large"==g?"text-[10px]":"text-[8px]"),children:[(0,r.jsx)(s.default,{src:"/icons/community.svg",alt:"community content",width:"large"==g?12:8,height:"large"==g?12:8}),(0,r.jsx)("span",{children:"Community"})]}),content:"Community Content"}):(0,r.jsx)(i.Z,{trigger:(0,r.jsxs)(o.C,{className:"flex gap-1 items-center bg-[#ffd700]/40 sm:text-xs ".concat("large"==g?"text-[10px]":"text-[8px]"),children:[(0,r.jsx)(c.Z,{width:"large"==g?12:8,height:"large"==g?12:8}),(0,r.jsx)("span",{children:"Official"})]}),content:"Official Content"})),"large"==g&&(0,r.jsxs)(r.Fragment,{children:[!h.community&&(0,r.jsx)(i.Z,{trigger:(0,r.jsx)(o.C,{className:"text-[10px] py-[6px] sm:text-xs",children:null===(m=h.personality)||void 0===m?void 0:m.split("-")[0]}),content:"Personality: ".concat(null===(f=h.personality)||void 0===f?void 0:f.split("-")[0])}),y&&h.kink&&"Vanilla"!==h.kink&&!h.community&&(0,r.jsx)(i.Z,{trigger:(0,r.jsx)(o.C,{className:"text-[10px] py-[6px] sm:text-xs",children:null===(p=h.kink)||void 0===p?void 0:p.split("-")[0]}),content:"Kink: ".concat(null===(x=h.kink)||void 0===x?void 0:x.split("-")[0])})]})]})})]})})};t.Z=e=>{let{character:t,isMyChar:a=!1,nsfw_preference:i,size:o="large",imageInterval:c=3e3,className:d}=e,x=(0,u.L)(),[h,g]=(0,n.useState)(0),[v,y]=(0,n.useState)(0),[w,b]=(0,n.useState)(!0),[j,N]=(0,n.useState)(!1),[_,k]=(0,n.useState)(!1),[S,C]=(0,n.useState)(!1),[I,A]=(0,n.useState)(!1),P=(0,n.useRef)(null),Z=S||I,F=(0,n.useMemo)(()=>{var e;let a=[{type:"image",url:t.initial_image}];return null===(e=t.images)||void 0===e||e.forEach(e=>{e.video?a.push({type:"video",url:e.video}):e.image&&a.push({type:"image",url:e.image})}),a},[t]),M=F[h],R=F.length;(0,n.useEffect)(()=>{if(!Z||!_||R<=1)return;let e=(h+1)%R,t=F[e];if((null==t?void 0:t.type)==="image")new window.Image().src=t.url;else if((null==t?void 0:t.type)==="video"){let e=document.createElement("video");e.src=t.url,e.preload="auto"}let a=setInterval(()=>{y(t=>{let r=t+1.6666666666666667;return r>=100?(clearInterval(a),y(0),g(e),k(!1),0):r})},50);return()=>clearInterval(a)},[Z,_,h,F,R]),(0,n.useEffect)(()=>{Z||(g(0),y(0),k(!0))},[Z]);let L=()=>{b(!1),N(!0),k(!0)};return(0,r.jsxs)("div",{className:(0,l.Z)("large"===o?"w-[288px]":"flex min-w-[172px] lg:w-[288px]","aspect-[13/19] min-w-[172px] w-full md:w-full max-w-80","relative overflow-hidden rounded-lg cursor-pointer group","focus:outline-none","transform transition-all duration-300 hover:scale-105","hover:shadow-[0_2px_8px_5px_rgba(214,42,94,0.3)] focus:shadow-[0_2px_8px_5px_rgba(214,42,94,0.3)]",d),onClick:()=>x({charId:t._id,redirectTo:"chat",isPublicChar:!a}),onMouseEnter:()=>C(!0),onMouseLeave:()=>C(!1),onFocus:()=>A(!0),onBlur:()=>A(!1),tabIndex:0,role:"button","aria-label":"View ".concat(t.first_name," ").concat(t.last_name,"'s profile"),children:[Z&&R>1&&(0,r.jsx)("div",{className:"absolute top-3 left-3 right-3 z-20",children:(0,r.jsx)(m,{totalDashes:F.length,currentDash:h,progress:v})}),w&&(0,r.jsx)(f,{}),"image"===M.type?(0,r.jsx)(s.default,{src:M.url,alt:"".concat(t.first_name," ").concat(t.last_name),fill:!0,className:(0,l.Z)("object-cover transition-opacity duration-500",w?"opacity-0":"opacity-100"),onLoad:()=>{b(!1),N(!1),k(!0)},onError:L,priority:0===h,sizes:"(max-width: 768px) 172px, 288px"}):(0,r.jsx)("video",{ref:P,className:"absolute inset-0 w-full h-full object-cover",src:M.url,autoPlay:!0,muted:!0,loop:!0,playsInline:!0,onLoadedData:()=>{var e;b(!1),N(!1),k(!0),null===(e=P.current)||void 0===e||e.play().catch(e=>{console.warn("Autoplay failed:",e)})},onError:L}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"}),(0,r.jsx)(p,{character:t,size:o,isMyChar:a,nsfw_preference:i})]})}},47795:function(e,t,a){var r=a(57437),n=a(2265),s=a(19024),l=a(48053),i=a(99441),o=a(97991),c=a(84125),d=a(82072),u=a(86460),m=a(3021),f=a(81733),p=a(14738);t.Z=e=>{var t;let{favChars:a}=e,{displayChars:x,setDisplayChars:h}=(0,s.c)(),{data:g}=(0,o.e)(),v=(0,i.useRecoilValue)(u.q),{data:y}=(0,p.j)(),[w,b]=(0,n.useState)(!1),{handleRandomizeChars:j}=(0,m.X)(),{data:N,isLoading:_,fetchNextPage:k,hasNextPage:S,isFetchingNextPage:C}=(0,d.Wr)({...v}),I=(0,n.useRef)(!1),A=(0,n.useRef)(0);(0,n.useEffect)(()=>{b(_||C)},[_,C]);let P=(0,n.useCallback)(()=>{if(I.current||C||!S)return;let{scrollTop:e,scrollHeight:t,clientHeight:a}=document.documentElement,r=.6*t;e+a>=r&&t!==A.current&&(I.current=!0,A.current=r,k().finally(()=>{setTimeout(()=>{I.current=!1},1e3)}))},[k,C,S,.6]);(0,n.useEffect)(()=>(window.addEventListener("scroll",P),()=>window.removeEventListener("scroll",P)),[P]);let Z=(0,n.useRef)(new Set);(0,n.useEffect)(()=>{h([]),Z.current.clear()},[v]),(0,n.useEffect)(()=>{if("sort"in v)h((null==N?void 0:N.pages.flatMap(e=>e.data))||[]);else{var e,t,a,r;let n=(null==N?void 0:null===(e=N.pages)||void 0===e?void 0:e.length)?(null==N?void 0:null===(t=N.pages)||void 0===t?void 0:t.length)-1:0,s=(null==N?void 0:null===(r=N.pages)||void 0===r?void 0:null===(a=r.at(-1))||void 0===a?void 0:a.data)||[];s.length>0&&!Z.current.has(n)&&(h(e=>[...e,...j([...s])]),Z.current.add(n))}},[N,v]);let F=Array(50).fill(null);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-row flex-wrap gap-4 md:gap-8 pt-4 pb-6 px-0 md:px-8 items-center justify-center",children:[x.map((e,t)=>{let a=null==g?void 0:g.chars_profile_pics.pfps.find(t=>t.char_id===e._id);return e.initial_image,(null==g?void 0:g.chars_profile_pics.visible)&&a&&a.pfp_url,(0,r.jsx)(f.Z,{character:e,size:"large",isMyChar:!1,nsfw_preference:null==y?void 0:y.nsfw_preference},"".concat(e._id,"-").concat(t))}),w&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"flex flex-row flex-wrap gap-4 md:gap-8 pt-4 pb-6 px-0 md:px-8 items-center justify-center",children:F.map((e,t)=>(0,r.jsx)("div",{className:"max-w-[288px] aspect-13/19 min-w-[172px] w-full",children:(0,r.jsx)(c.Z,{})},t))})}),(null==N?void 0:null===(t=N.pages[0])||void 0===t?void 0:t.data.length)===0&&(0,r.jsx)("div",{className:"flex flex-row flex-wrap gap-4 md:gap-8 pt-4 pb-6 px-0 md:px-8 items-center justify-center",children:(0,r.jsx)("div",{className:"text-center text-lg",children:"No characters found"})})]}),!a&&(0,r.jsx)("div",{className:"w-full flex justify-center items-center",children:(0,r.jsx)("div",{className:"w-fit mt-8 mb-14 bg-opacity-35 bg-[#252525] backdrop-filter backdrop-blur-lg rounded-lg p-5",children:(0,r.jsx)(l.Z,{title1:"Can't find what you're looking for?",title2:"Create your dream partner"})})})]})}},84125:function(e,t,a){var r=a(57437);t.Z=()=>(0,r.jsxs)("div",{role:"status",className:"relative min-w-[172px] w-full h-[470px] overflow-hidden rounded-2xl border border-[#262626]  animate-pulse",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-linear-to-r from-transparent via-white/20 to-transparent animate-shimmer"}),(0,r.jsx)("div",{className:"h-56 flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"w-12 h-12 text-gray-400","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",fill:"#262626",viewBox:"0 0 16 20",children:[(0,r.jsx)("path",{d:"M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM10.5 6a1.5 1.5 0 1 1 0 2.999A1.5 1.5 0 0 1 10.5 6Zm2.221 10.515a1 1 0 0 1-.858.485h-8a1 1 0 0 1-.9-1.43L5.6 10.039a.978.978 0 0 1 .936-.57 1 1 0 0 1 .9.632l1.181 2.981.541-1a.945.945 0 0 1 .883-.522 1 1 0 0 1 .879.529l1.832 3.438a1 1 0 0 1-.031.988Z"}),(0,r.jsx)("path",{d:"M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z"})]})}),(0,r.jsxs)("div",{className:"absolute top-8 right-3 flex flex-col space-y-2 items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-[#262626] rounded-full"}),(0,r.jsx)("div",{className:"w-8 h-8 bg-[#262626] rounded-full"})]}),(0,r.jsxs)("div",{className:"absolute bottom-0 w-full p-3",children:[(0,r.jsx)("div",{className:"h-5 bg-[#262626] rounded w-40 mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-[#262626] rounded w-full mb-2"}),(0,r.jsx)("div",{className:"h-4 bg-[#262626] rounded w-3/4"}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-4",children:[(0,r.jsx)("div",{className:"h-8 w-24 bg-[#262626] rounded-full"}),(0,r.jsx)("div",{className:"h-8 w-24 bg-[#262626] rounded-full"})]})]}),(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})]})},67236:function(e,t,a){a.d(t,{default:function(){return p}});var r=a(57437),n=a(2265),s=a(64756),l=a(42421),i=a(13498);let o=s.fC,c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(s.ck,{ref:t,className:(0,i.cn)("border-b",a),...n})});c.displayName="AccordionItem";let d=n.forwardRef((e,t)=>{let{className:a,children:n,...o}=e;return(0,r.jsx)(s.h4,{className:"flex",children:(0,r.jsxs)(s.xz,{ref:t,className:(0,i.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",a),...o,children:[n,(0,r.jsx)(l.Z,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});d.displayName=s.xz.displayName;let u=n.forwardRef((e,t)=>{let{className:a,children:n,...l}=e;return(0,r.jsx)(s.VY,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...l,children:(0,r.jsx)("div",{className:(0,i.cn)("pb-4 pt-0",a),children:n})})});u.displayName=s.VY.displayName;var m=a(38472);async function f(){try{return(await m.default.get("".concat(i.fw,"/static_data/faq"))).data}catch(e){return[]}}var p=e=>{let{center:t,justifyCenter:a}=e,[s,l]=(0,n.useState)();return(0,n.useEffect)(()=>{(async()=>{l((await f()).faqs)})()},[]),(0,r.jsxs)("div",{className:"w-full py-5 px-2",children:[!t&&!a&&(0,r.jsx)("h2",{className:"scroll-mt-20 pb-5 xl:pe-24 lg:pe-16 md:pe-12 sm:pe-16 pe-8 p-5 pt-0 text-primary text-xl font-bold",children:"Frequenty Asked Questions"}),(0,r.jsx)("div",{className:"w-full ".concat(!t&&!a&&"xl:pe-24 lg:pe-16 md:pe-12 sm:pe-16 pe-8 md:pl-5"),children:s&&Object.keys(s).map(e=>{let n=s[e];return(0,r.jsx)(o,{type:"single",collapsible:!0,children:(0,r.jsxs)(c,{value:"item-1",className:"".concat(t?"max-w-none border-0":a?"max-w-none":"max-w-xl"),children:[(0,r.jsx)(d,{className:"text-start font-bold ".concat(t?"text-xl bg-background py-8 px-10  mt-4 border border-primary rounded-lg data-[state=open]:border-0 [&[data-state=open]]:text-primary":""),children:n.question}),(0,r.jsx)(u,{className:t?"text-xl bg-background py-8 px-10":"",children:n.answer})]})},e)})})]})}},9962:function(e,t,a){a.d(t,{Z:function(){return d}});var r=a(57437),n=a(2265),s=a(27071),l=a(13498);s.zt;let i=s.fC,o=s.xz,c=n.forwardRef((e,t)=>{let{className:a,sideOffset:n=4,...i}=e;return(0,r.jsx)(s.VY,{ref:t,side:"bottom",sideOffset:n,className:(0,l.cn)("z-50 rounded-md overflow-hidden border border-gray-600 bg-popover px-2 py-1 text-[0.7rem] text-popover-foreground shadow-xl animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[side=bottom]:slide-in-from-top-2",a),...i})});c.displayName=s.VY.displayName;var d=e=>{let{trigger:t,content:a,side:n,className:s}=e;return(0,r.jsxs)(i,{delayDuration:500,children:[(0,r.jsx)(o,{children:t}),(0,r.jsx)(c,{side:n,className:s||"",children:a})]})}},94693:function(e,t,a){a.d(t,{C:function(){return i}});var r=a(57437);a(2265);var n=a(12218),s=a(13498);let l=(0,n.j)("inline-flex items-center rounded-full px-3 py-1 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{promptSuggestion:"bg-card hover:bg-background rounded-lg px-3 text-xs xs:text-sm",transparent:"bg-black/40",default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"transparent"}});function i(e){let{className:t,variant:a,...n}=e;return(0,r.jsx)("div",{className:(0,s.cn)(l({variant:a}),t),...n})}},36013:function(e,t,a){a.d(t,{Ol:function(){return i},Zb:function(){return l},aY:function(){return c},eW:function(){return d},ll:function(){return o}});var r=a(57437),n=a(2265),s=a(13498);let l=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("transition-shadow rounded-lg overflow-hidden bg-card text-card-foreground shadow-lg",a),...n})});l.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-2 p-4",a),...n})});i.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,s.cn)("text-lg font-bold leading-none",a),...n})});o.displayName="CardTitle",n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,s.cn)("text-xs text-muted-foreground",a),...n})}).displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("",a),...n})});c.displayName="CardContent";let d=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,s.cn)("w-full bg-card font-bold py-1.5 flex items-center justify-center",a),...n})});d.displayName="CardFooter"},19024:function(e,t,a){a.d(t,{S:function(){return l},c:function(){return i}});var r=a(57437),n=a(2265);let s=(0,n.createContext)({displayChars:[],setDisplayChars:()=>{}});function l(e){let{children:t}=e,[a,l]=(0,n.useState)([]);return(0,r.jsx)(s.Provider,{value:{displayChars:a,setDisplayChars:l},children:t})}function i(){return(0,n.useContext)(s)}},59181:function(e,t,a){a.d(t,{U:function(){return s}});var r=a(22351),n=a(76351);let s=()=>{let e=async()=>{try{return(await r.j.get("/character/public")).data}catch(e){}};return(0,n.useQuery)({queryKey:["public-chars"],queryFn:e,refetchOnWindowFocus:!1,staleTime:6e4,gcTime:18e5})}},87497:function(e,t,a){a.d(t,{L:function(){return h}});var r=a(16463),n=a(59181),s=a(88726),l=a(38472),i=a(13498),o=a(14738),c=a(85499),d=a(99441),u=a(22351),m=a(66511),f=a(34084),p=a(93191),x=a(67374);let h=()=>{let e=(0,r.useRouter)(),t=(0,r.useSearchParams)(),{data:a}=(0,o.j)(),{data:h}=(0,n.U)(),g=(0,d.useSetRecoilState)(c.$d),v=(0,p.useQueryClient)(),y=(0,d.useSetRecoilState)(f.AL),w=async()=>{try{return(await u.j.post("/character/user_characters",{last_char_id:""})).data}catch(e){}},b=r=>{let{charId:n,redirectTo:s}=r;n&&localStorage.setItem("sd_active_char_id",n),"chat"===s?(v.invalidateQueries({queryKey:["user-chars",null==a?void 0:a._id]}),e.push("/chat")):"generator"===s?(null==t?void 0:t.has("image-option"))?e.push("/generator/ai-images?image-option=".concat((null==t?void 0:t.get("image-option"))||"Action")):e.push("/generator/ai-images?image-option=Action"):e.push("/")},j=(0,d.useSetRecoilState)(c.PC),N=(0,d.useSetRecoilState)(c.Qg),_=(0,d.useRecoilState)(m.loggedIn)[0],k=(0,x.a)(),S=async e=>{let{charId:t,isPrivateChar:a}=e;try{let e=await w(),a=null==e?void 0:e.msg.find(e=>e._id===t);if(!_){j(()=>({open:!0,isSignup:!0,text:"Sign up to call ".concat(null==a?void 0:a.first_name,"!"),image:"".concat(null==a?void 0:a.initial_image),gaClass:"ga-reg_popup_calling"}));return}s.default.loading("Calling now ...",{id:"call"});let n=await l.default.post("".concat(i.fw,"/check/check_call"),{},{headers:{Authorization:localStorage.getItem("access_token")||""}});if(s.default.dismiss("call"),0==n.data.code){N({open:!0,text:"Upgrade to call ".concat(null==a?void 0:a.first_name,"!"),image:"".concat(null==a?void 0:a.initial_image),type:"subscription",gaClass:"ga-sub_popup_calling"});return}if(2==n.data.code){N({open:!0,text:"Grab Hearts to call ".concat(null==a?void 0:a.first_name,"!"),image:"".concat(null==a?void 0:a.initial_image),type:"hearts"});return}if(a){var r;localStorage.setItem("sd_active_char_id",t||""),y(!1),g({open:!0,receivingCall:!1,userCalling:!0,text:a.first_name||"",image:null!==(r=a.initial_image)&&void 0!==r?r:""})}}catch(e){s.default.error("Something went wrong. Please try again later.")}};return async e=>{let{charId:t,redirectTo:r,isPublicChar:n}=e;if(!n&&"homepage"!==r){b({charId:t,redirectTo:r});return}try{var l;let e=null==a?void 0:null===(l=a.conv_history)||void 0===l?void 0:l.find(e=>e.parent_char_id===t);if(e&&"homepage"!==r){b({charId:e.char_id,redirectTo:r});return}if(e&&"homepage"===r){S({charId:e.char_id,isPrivateChar:!0});return}let n=null==h?void 0:h.find(e=>e._id===t);if(!n){s.default.error("Public character not found!");return}let i=localStorage.getItem("access_token")||localStorage.getItem("visitor_token");if(!i){(0,s.default)("Please log in again"),k();return}s.default.loading("Please wait while ".concat(n.first_name," is getting ready for you..."),{id:"cloningChar"});let o=(await u.j.post("/character/private",n)).data;await v.invalidateQueries({queryKey:["user",i]}),await v.invalidateQueries({queryKey:["my-chars",null==a?void 0:a._id]}),await v.invalidateQueries({queryKey:["user-chars",null==a?void 0:a._id]}),"homepage"!==r&&b({charId:o.id,redirectTo:r}),await v.invalidateQueries({queryKey:["user"]}),await v.invalidateQueries({queryKey:["my-chars",null==a?void 0:a._id]}),await v.invalidateQueries({queryKey:["user-chars",null==a?void 0:a._id]}),"homepage"===r&&S({charId:o.id,isPrivateChar:!1})}catch(e){console.error(e)}finally{s.default.dismiss("cloningChar")}}}},3021:function(e,t,a){a.d(t,{X:function(){return s}});var r=a(97991);function n(e){for(let t=e.length-1;t>0;t--){let a=Math.floor(Math.random()*(t+1));[e[t],e[a]]=[e[a],e[t]]}return e}let s=()=>{let{data:e}=(0,r.e)();return{handleRandomizeChars:t=>{let a=null==e?void 0:e.chars_profile_pics.pfps,r=t.filter(e=>null==a?void 0:a.some(t=>t.char_id===e._id)),s=t.filter(e=>!(null==a?void 0:a.some(t=>t.char_id===e._id)));return[...n([...r]),...n([...s])]}}}},97991:function(e,t,a){a.d(t,{e:function(){return s}});var r=a(76351),n=a(22351);let s=()=>{let e=async()=>(await n.j.get("/promotional_data/")).data;return(0,r.useQuery)({queryKey:["promotional-data"],queryFn:e,refetchOnWindowFocus:!1,staleTime:1/0})}},82072:function(e,t,a){a.d(t,{AU:function(){return h},Wr:function(){return x},c6:function(){return g}});var r=a(54252),n=a(76351),s=a(93191),l=a(25524),i=a(19868);let o="/api/v1/characters",c=async e=>{var t;e.fields&&!e.fields.includes("_id")&&e.fields.push("_id");let a={params:{...e,fields:null===(t=e.fields)||void 0===t?void 0:t.join(",")}};return await i.h.get(o,a)},d=async e=>(await i.h.get("".concat(o,"/").concat(e))).data,u=async(e,t)=>(await i.h.patch("".concat(o,"/").concat(e),t)).data;var m=a(2265);let f=["community","public","first_name","last_name","age","initial_image","personality","kink","about_me","images","nsfw","tags","gender","style","created_date"],p=["ethnicity","personality","age","nsfw","search","tags","kink","gender","sort","community","style","body_type","relationship"],x=function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{fields:f},n=(0,m.useMemo)(()=>JSON.parse(JSON.stringify(a)),[a]),s=(0,m.useMemo)(()=>new URLSearchParams(Object.entries(n).reduce((e,t)=>{let[a,r]=t;return{...e,[a]:Array.isArray(r)?r.join(","):r}},{})).toString(),[n]);!0===n.nsfw?delete n.nsfw:n.nsfw=!1,(null===(e=n.gender)||void 0===e?void 0:e.toLowerCase())==="both"&&delete n.gender,(null===(t=n.style)||void 0===t?void 0:t.toLowerCase())==="both"&&delete n.style;let l=Object.entries(n).reduce((e,t)=>{let[a,r]=t;return p.includes(a)?{...e,[a]:r}:e},{});return l.fields=f,l.limit=50,(0,r.useInfiniteQuery)({queryKey:["characters",s],queryFn:async e=>{let{pageParam:t=1}=e;return await c({tags:["!Fantasy"],...l,page:t,public:!0})},getNextPageParam:e=>e.meta.nextPage||null,initialPageParam:1,staleTime:864e5})},h=e=>(0,n.useQuery)({queryKey:["character",e],queryFn:()=>d(e),enabled:!!e,staleTime:864e5}),g=()=>{let e=(0,s.useQueryClient)();return(0,l.useMutation)({mutationFn:e=>{let{id:t,payload:a}=e;return u(t,a)},onSuccess:(t,a)=>{let{id:r}=a;e.setQueryData(["character",r],t),e.setQueryData(["characters"],e=>e?{...e,pages:e.pages.map(e=>e.map(e=>e._id===r?{...e,...t}:e))}:e)}})}},67374:function(e,t,a){a.d(t,{a:function(){return d}});var r=a(75735),n=a(39952),s=a(99441),l=a(66511),i=a(88726),o=a(16463),c=a(22351);let d=()=>{let e=(0,s.useSetRecoilState)(l.loggedIn),t=(0,o.useSearchParams)();return async()=>{await (0,r.w7)(n.I),localStorage.removeItem("access_token"),localStorage.removeItem("pref"),localStorage.removeItem("character_id"),localStorage.removeItem("character"),localStorage.removeItem("timezone"),e(!1),c.E.clear(),i.default.success("Logout Successful"),t.has("invite")?window.location.href="/?invite=".concat(t.get("invite")):window.location.href="/"}}},14738:function(e,t,a){a.d(t,{j:function(){return l}});var r=a(22351),n=a(76351),s=a(88726);let l=()=>{let e=localStorage.getItem("access_token")||localStorage.getItem("visitor_token"),t=async()=>{try{if(!e)return;return(await r.j.get("/user/")).data}catch(e){e.response&&"TokenExpiredError"==e.response.data.msg?(localStorage.getItem("access_token")&&s.toast.error("Please login again"),localStorage.removeItem("access_token"),localStorage.removeItem("visitor_token"),window.location.href="/"):console.error(e)}};return(0,n.useQuery)({queryKey:["user",e],queryFn:t,enabled:!!e,refetchOnWindowFocus:!1,staleTime:6048e5})}},53541:function(e,t,a){a.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(78030).Z)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},87138:function(e,t,a){a.d(t,{default:function(){return n.a}});var r=a(231),n=a.n(r)},16463:function(e,t,a){var r=a(71169);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},34084:function(e,t,a){a.d(t,{AL:function(){return s},FG:function(){return l},b7:function(){return o},sE:function(){return i},zK:function(){return n}});var r=a(99441);let n=(0,r.atom)({key:"callStartedAtom",default:!1}),s=(0,r.atom)({key:"callEndedAtom",default:!1}),l=(0,r.atom)({key:"unlockMicrophoneAtom",default:!1}),i=(0,r.atom)({key:"howlerPlayingAtom",default:!1}),o=(0,r.atom)({key:"callInfoAtom",default:{convId:"",charId:"",msgHistory:[]}})},85499:function(e,t,a){a.d(t,{$K:function(){return g},$L:function(){return x},$d:function(){return c},CP:function(){return _},Dz:function(){return m},It:function(){return p},LM:function(){return w},Md:function(){return y},NI:function(){return h},Oc:function(){return l},PC:function(){return d},Qg:function(){return o},Yu:function(){return f},_A:function(){return u},cG:function(){return v},d8:function(){return s},dG:function(){return i},kb:function(){return j},nA:function(){return N},s4:function(){return b},wi:function(){return n},wl:function(){return k}});var r=a(99441);let n=(0,r.atom)({key:"showCharProfileSidebarOnChatPageMobile",default:!1}),s=(0,r.atom)({key:"showCharProfileSidebarOnChatPage",default:!0}),l=(0,r.atom)({key:"showPromptSidebarOnGeneratorPageMobile",default:!0}),i=(0,r.atom)({key:"showMobMenuAtom",default:!1});(0,r.atom)({key:"showPaymentModalAtom",default:!1});let o=(0,r.atom)({key:"showUpgradeToPremiumDialogAtom",default:{open:!1,text:"",image:"",type:"subscription"}}),c=(0,r.atom)({key:"phoneCallModalStateAtom",default:{open:!1,receivingCall:!1,text:"",image:"",userCalling:!1}}),d=(0,r.atom)({key:"showAuthDialogAtom",default:{open:!1,isSignup:!1,text:"",image:"",gaClass:""}}),u=(0,r.atom)({key:"viewImageAtom",default:{open:!1,id:"",src:"",prompt:"",isLiveImg:!1}}),m=(0,r.atom)({key:"showVerifyEmailDialogAtom",default:!1}),f=(0,r.atom)({key:"showGenderPrefDialogAtom",default:!1}),p=(0,r.atom)({key:"genderPrefAtom",default:""}),x=(0,r.atom)({key:"showSystemNotificationDialogAtom",default:{open:!1,_id:"",sender:"",description:"	",date:"",type:"",img:"",redirectUrl:"",datetime:"",charId:"",convId:"",content:""}}),h=(0,r.atom)({key:"disableChatInputAtom",default:!1}),g=(0,r.atom)({key:"chatInputAtom",default:""});(0,r.atom)({key:"myProfileModalStateAtom",default:!1});let v=(0,r.atom)({key:"thankyouModalStateAtom",default:!1}),y=(0,r.atom)({key:"bannedWordPopupAtom",default:[]});(0,r.atom)({key:"nsfwLivePhotoAtom2",default:!1});let w=(0,r.atom)({key:"showSelectShareCharImgsPopupAtom",default:!1}),b=(0,r.atom)({key:"selectedShareCharImgsAtom",default:[]}),j=(0,r.atom)({key:"showPublishCharPopupAtom",default:!1}),N=(0,r.atom)({key:"showSetCharImagesPopupAtom",default:!1}),_=(0,r.atom)({key:"flaggedIdAtom",default:""}),k=(0,r.atom)({key:"showUpdateBillingModalAtom",default:!1})},86460:function(e,t,a){a.d(t,{q:function(){return r}});let r=(0,a(99441).atom)({key:"homePageFiltersAtom",default:{}})},66511:function(e,t,a){a.d(t,{loggedIn:function(){return r}});let r=(0,a(99441).atom)({key:"loggedIn",default:!1})},19868:function(e,t,a){a.d(t,{G:function(){return s},h:function(){return l}});var r=a(38472);let n=function(){let e=r.default.create({baseURL:"https://api.secretdesires.ai",headers:{"Content-Type":"application/json"}});return e.interceptors.response.use(e=>"status"in e.data&&e.status>201?{code:e.data.code,error:e.data.error}:e.data,e=>{if(e.response&&e.response.data)return e.response.data}),e}(),s=(e,t)=>{t?n.defaults.headers.common[e]=t:delete n.defaults.headers.common[e]},l=n},22351:function(e,t,a){a.d(t,{E:function(){return p},j:function(){return m}});var r=a(13498),n=a(58421),s=a(86623),l=a(38472),i=a(75735),o=a(39952),c=a(88726),d=a(22351);let u=async()=>{await (0,i.w7)(o.I);{localStorage.removeItem("access_token"),localStorage.removeItem("pref"),localStorage.removeItem("character_id"),localStorage.removeItem("character"),localStorage.removeItem("timezone"),localStorage.removeItem("visitor_token");let{loggedIn:e}=await Promise.all([a.e(691),a.e(6511)]).then(a.bind(a,66511)),{setRecoil:t}=await Promise.all([a.e(691),a.e(2912)]).then(a.bind(a,82912));t(e,!1),c.toast.success("Logged out please log in again"),window.location.href="/",d.E.clear()}},m=l.default.create({baseURL:r.fw});m.interceptors.request.use(e=>{e.headers||(e.headers=new s.uu);let t=localStorage.getItem("access_token"),a=localStorage.getItem("visitor_token"),r=t||a;return"undefined"==r?u():r&&"undefined"!==r&&(e.headers.set("Authorization",r),e.headers.set("User-Type",t?"login_user":"visitor")),e},e=>Promise.reject(e)),m.interceptors.response.use(e=>e,async e=>{var t,a,r,n,s;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&((null===(r=e.response)||void 0===r?void 0:null===(a=r.data)||void 0===a?void 0:a.msg)==="user_not_found"||(null===(s=e.response)||void 0===s?void 0:null===(n=s.data)||void 0===n?void 0:n.msg)==="no_header_error")&&await u(),Promise.reject(e)});let f=async e=>{let{queryKey:t}=e;try{let[e,a]=t;return(await m(e,a)).data}catch(e){if(e instanceof s.d7){var a;console.error("Axios error:",(null===(a=e.response)||void 0===a?void 0:a.data)||e.message)}else console.error("Unexpected error:",e);throw e}},p=new n.S({defaultOptions:{queries:{queryFn:f}}})},12218:function(e,t,a){a.d(t,{j:function(){return s}});let r=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,n=function(){for(var e,t,a=0,r="";a<arguments.length;)(e=arguments[a++])&&(t=function e(t){var a,r,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t))for(a=0;a<t.length;a++)t[a]&&(r=e(t[a]))&&(n&&(n+=" "),n+=r);else for(a in t)t[a]&&(n&&(n+=" "),n+=a)}return n}(e))&&(r&&(r+=" "),r+=t);return r},s=(e,t)=>a=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:l,defaultVariants:i}=t,o=Object.keys(l).map(e=>{let t=null==a?void 0:a[e],n=null==i?void 0:i[e];if(null===t)return null;let s=r(t)||r(n);return l[e][s]}),c=a&&Object.entries(a).reduce((e,t)=>{let[a,r]=t;return void 0===r||(e[a]=r),e},{});return n(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:a,className:r,...n}=t;return Object.entries(n).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...i,...c}[t]):({...i,...c})[t]===a})?[...e,a,r]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}}}]);