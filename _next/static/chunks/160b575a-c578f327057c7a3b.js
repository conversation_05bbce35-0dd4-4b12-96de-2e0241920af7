"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9571],{27485:function(t,e,i){i.d(e,{g4:function(){return h},gy:function(){return p}});var a=i(57437);i(2265);var r=i(89183);let s="#4fa94d",n={"aria-busy":!0,role:"progressbar"},o=(0,r.ZP).div`
  display: ${t=>t.$visible?"flex":"none"};
`,l="http://www.w3.org/2000/svg",d=(0,r.F4)`
12.5% {
  stroke-dasharray: ${33.98873199462888}px, ${242.776657104492}px;
  stroke-dashoffset: -${26.70543228149412}px;
}
43.75% {
  stroke-dasharray: ${84.97182998657219}px, ${242.776657104492}px;
  stroke-dashoffset: -${84.97182998657219}px;
}
100% {
  stroke-dasharray: ${2.42776657104492}px, ${242.776657104492}px;
  stroke-dashoffset: -${240.34889053344708}px;
}
`;(0,r.ZP).path`
  stroke-dasharray: ${2.42776657104492}px, ${242.776657104492};
  stroke-dashoffset: 0;
  animation: ${d} ${1.6}s linear infinite;
`;let c=(0,r.F4)`
to {
   transform: rotate(360deg);
 }
`;(0,r.ZP).svg`
  animation: ${c} 0.75s steps(12, end) infinite;
  animation-duration: 0.75s;
`,(0,r.ZP).polyline`
  stroke-width: ${t=>t.width}px;
  stroke-linecap: round;

  &:nth-child(12n + 0) {
    stroke-opacity: 0.08;
  }

  &:nth-child(12n + 1) {
    stroke-opacity: 0.17;
  }

  &:nth-child(12n + 2) {
    stroke-opacity: 0.25;
  }

  &:nth-child(12n + 3) {
    stroke-opacity: 0.33;
  }

  &:nth-child(12n + 4) {
    stroke-opacity: 0.42;
  }

  &:nth-child(12n + 5) {
    stroke-opacity: 0.5;
  }

  &:nth-child(12n + 6) {
    stroke-opacity: 0.58;
  }

  &:nth-child(12n + 7) {
    stroke-opacity: 0.66;
  }

  &:nth-child(12n + 8) {
    stroke-opacity: 0.75;
  }

  &:nth-child(12n + 9) {
    stroke-opacity: 0.83;
  }

  &:nth-child(12n + 11) {
    stroke-opacity: 0.92;
  }
`;let p=({height:t=80,width:e=80,strokeWidth:i=2,radius:r=1,color:d=s,ariaLabel:c="tail-spin-loading",wrapperStyle:p,wrapperClass:h,visible:f=!0})=>{let u=parseInt(String(i)),x=u+36,m=u/2,y=m+parseInt(String(r))-1;return(0,a.jsx)(o,{style:p,$visible:f,className:h,"data-testid":"tail-spin-loading","aria-label":c,...n,children:(0,a.jsxs)("svg",{width:e,height:t,viewBox:`0 0 ${x} ${x}`,xmlns:l,"data-testid":"tail-spin-svg",children:[(0,a.jsx)("defs",{children:(0,a.jsxs)("linearGradient",{x1:"8.042%",y1:"0%",x2:"65.682%",y2:"23.865%",id:"a",children:[(0,a.jsx)("stop",{stopColor:d,stopOpacity:"0",offset:"0%"}),(0,a.jsx)("stop",{stopColor:d,stopOpacity:".631",offset:"63.146%"}),(0,a.jsx)("stop",{stopColor:d,offset:"100%"})]})}),(0,a.jsx)("g",{fill:"none",fillRule:"evenodd",children:(0,a.jsxs)("g",{transform:`translate(${m} ${m})`,children:[(0,a.jsx)("path",{d:"M36 18c0-9.94-8.06-18-18-18",id:"Oval-2",stroke:d,strokeWidth:i,children:(0,a.jsx)("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})}),(0,a.jsx)("circle",{fill:"#fff",cx:"36",cy:"18",r:y,children:(0,a.jsx)("animateTransform",{attributeName:"transform",type:"rotate",from:"0 18 18",to:"360 18 18",dur:"0.9s",repeatCount:"indefinite"})})]})})]})})},h=({height:t=80,width:e=80,radius:i=9,color:r=s,ariaLabel:d="three-dots-loading",wrapperStyle:c,wrapperClass:p,visible:h=!0})=>(0,a.jsx)(o,{style:c,$visible:h,className:p,"data-testid":"three-dots-loading","aria-label":d,...n,children:(0,a.jsxs)("svg",{width:e,height:t,viewBox:"0 0 120 30",xmlns:l,fill:r,"data-testid":"three-dots-svg",children:[(0,a.jsxs)("circle",{cx:"15",cy:"15",r:Number(i)+6,children:[(0,a.jsx)("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),(0,a.jsx)("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]}),(0,a.jsxs)("circle",{cx:"60",cy:"15",r:i,attributeName:"fill-opacity",from:"1",to:"0.3",children:[(0,a.jsx)("animate",{attributeName:"r",from:"9",to:"9",begin:"0s",dur:"0.8s",values:"9;15;9",calcMode:"linear",repeatCount:"indefinite"}),(0,a.jsx)("animate",{attributeName:"fill-opacity",from:"0.5",to:"0.5",begin:"0s",dur:"0.8s",values:".5;1;.5",calcMode:"linear",repeatCount:"indefinite"})]}),(0,a.jsxs)("circle",{cx:"105",cy:"15",r:Number(i)+6,children:[(0,a.jsx)("animate",{attributeName:"r",from:"15",to:"15",begin:"0s",dur:"0.8s",values:"15;9;15",calcMode:"linear",repeatCount:"indefinite"}),(0,a.jsx)("animate",{attributeName:"fill-opacity",from:"1",to:"1",begin:"0s",dur:"0.8s",values:"1;.5;1",calcMode:"linear",repeatCount:"indefinite"})]})]})}),f=(0,r.F4)`
to {
   stroke-dashoffset: 136;
 }
`;(0,r.ZP).polygon`
  stroke-dasharray: 17;
  animation: ${f} 2.5s cubic-bezier(0.35, 0.04, 0.63, 0.95) infinite;
`,(0,r.ZP).svg`
  transform-origin: 50% 65%;
`}}]);