(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3741],{81062:function(e,t,a){Promise.resolve().then(a.bind(a,55677))},55677:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ep}});var s=a(57437),l=a(5207),i=a(3114),r=a(66648),n=a(87138),o=a(99441),c=a(66511),d=function(){let e=(0,i.P)(),t=(0,o.useRecoilValue)(c.loggedIn);return(0,s.jsxs)("div",{className:"group grid lg:grid-cols-8 grid-cols-6 gap-2 justify-center content-center p-2 mb-5 text-white",children:[(0,s.jsxs)(n.default,{"aria-label":"Create Character",href:"/create",className:"lg:col-start-2 transition-shadow col-start-1 hover:shadow-[0_2px_8px_2px_rgba(214,42,94,0.3)]    md:col-span-4 col-span-full     bg-linear-to-b from-[#191919] via-[#37262B] to-card     h-fit p-5 pb-0 rounded-3xl overflow-hidden",children:[(0,s.jsx)("h2",{className:"text-lg lg:text-2xl font-semibold lg:font-bold pb-1 ",children:"Spicy AI Chatting"}),(0,s.jsx)("p",{id:t?"old_user_visit":"",className:"text-[#858890] text-xs lg:text-sm font-normal lg:font-bold leading-4 lg:leading-5",children:"Build your perfect AI partner. Customize their looks and personality to bring your fantasies to life."}),(0,s.jsxs)("div",{className:"flex flex-row sm:justify-between items-center sm:pt-3",children:[(0,s.jsx)("span",{className:"scale-75 lg:scale-100 lg:ml-0 -ml-6",children:(0,s.jsx)(l.default,{icon:e.createCharIcon,text:"Create Character"})}),(0,s.jsx)(r.default,{className:"sm:pt-0 pt-3 sm:self-center w-40 sm:w-[280px] aspect-auto",src:"/BannerFaces.webp",alt:"Faces of Characters",height:150,width:280,priority:!0})]})]}),(0,s.jsxs)(n.default,{"aria-label":"Guide to Secret Desires",href:"/guide",className:"transition-shadow hover:shadow-[0_2px_8px_2px_rgba(214,42,94,0.3)] bg-linear-to-b from-[#191919] via-[#37262B] to-card      md:block hidden      col-span-2     lg:p-5 p-5  rounded-3xl",id:"hero-guide-card",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold pb-1",children:"User Guide"}),(0,s.jsx)("h3",{className:"text-[#858890] text-sm font-bold leading-5 pb-10 ",children:"Discover our usage Guide to become a pro."}),(0,s.jsx)(l.default,{icon:e.book,text:"Go to Guide"})]})]})},u=a(47795),m=a(7449),p=a.n(m),f=a(2265),h=a(16427),x=a(16463),g=a(88726),y=a(67374),v=a(85499),b=a(19024),w=a(50495);function C(e){let{title1:t,title2:a,selectCharBtn:l}=e,n=(0,i.P)(),o=(0,x.useRouter)();return(0,s.jsx)("div",{className:" flex sm:flex-row flex-col md:p-2 sm:p-1   sm:gap-6 gap-4 items-center justify-center mt-10",children:(0,s.jsxs)("div",{className:"relative 2xl:w-80 sLaptop:w-72 sm:w-60 w-[79vw] rounded-lg overflow-hidden cursor-pointer",children:[(0,s.jsx)(r.default,{alt:"Character portrait",className:"w-full object-cover",height:"500",width:"500",src:"/images/CreateCharacterIMG.webp"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-linear-to-t from-black to-transparent p-4",children:(0,s.jsxs)("div",{className:"flex flex-col gap-y-2 text-white items-start  justify-center  cursor-pointer sm:w-fit md:max-w-96 sm:max-w-72 w-64",children:[(0,s.jsx)("div",{className:"flex flex-col sm:text-start text-center",children:(0,s.jsx)("h3",{style:{textShadow:"1px 1px black"},className:"md:text-xl text-sm text-wrap text-shadow-[2px_2px_2px_rgba(0,0,0,1)]",children:a})}),(0,s.jsx)("div",{className:"",children:(0,s.jsxs)(w.z,{"aria-label":"Create Partner",className:"text-sm h-8 flex flex-row gap-2 items-center",onClick:()=>o.replace("/create"),children:[n.createCharIcon,(0,s.jsx)("span",{children:"Create Partner"})]})}),(0,s.jsx)("div",{className:"",children:(0,s.jsxs)(w.z,{"aria-label":"Reset Filter",className:"text-sm h-8 flex flex-row gap-2 items-center",onClick:()=>{window.location.href="/"},children:[n.refresh,"Reset Filter"]})})]})})]})})}var j=a(3021),_=a(97991),N=a(84125);/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let k=(0,a(78030).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var S=a(87592),F=a(82072),R=a(86460),A=a(81733),M=a(14738),E=()=>{let e=(0,o.useRecoilValue)(R.q),{displayChars:t,setDisplayChars:a}=(0,b.c)(),{handleRandomizeChars:l}=(0,j.X)(),{data:i}=(0,_.e)(),{data:r}=(0,M.j)(),[n,c]=(0,f.useState)(0),[d,u]=(0,f.useState)(new Map),{data:m,isLoading:p,fetchNextPage:h,hasNextPage:x,isFetchingNextPage:g}=(0,F.Wr)({...e}),y=(0,f.useRef)(!1),v=(0,f.useRef)(0),E=(0,f.useCallback)(()=>{if(y.current||g||!x)return;let{scrollTop:e,scrollHeight:t,clientHeight:a}=document.documentElement;e+a>=.6*t&&t!==v.current&&(y.current=!0,v.current=t,h().finally(()=>{console.log("fetching next page"),setTimeout(()=>{y.current=!1},500)}))},[h,g,x,.6]);(0,f.useEffect)(()=>(window.addEventListener("scroll",E),()=>window.removeEventListener("scroll",E)),[E]);let z=e=>{if(!t||0===t.length)return;let a=new Map(d),s=t.length,l=[];for(let t=1;t<=5;t++){let a=(e-t+s)%s;l.push(a)}for(let t=1;t<=5;t++){let a=(e+t)%s;l.push(a)}l.forEach(e=>{let s=t[e];if(!s)return;let l=null==i?void 0:i.chars_profile_pics.pfps.find(e=>e.char_id===s._id),r=(null==i?void 0:i.chars_profile_pics.visible)&&l?l.pfp_url:s.initial_image;if(r&&!a.has(s._id||"")){let e=new Image;e.src=r,e.onload=()=>{a.set(s._id||"",!0),u(new Map(a))},e.onerror=()=>{a.set(s._id||"",!1),u(new Map(a))},s.images&&s.images.length>0&&s.images.forEach(e=>{e.image&&(new Image().src=e.image)})}})};(0,f.useEffect)(()=>{t&&t.length>0&&z(n)},[n,t,i]);let P=(0,f.useRef)(new Set);if((0,f.useEffect)(()=>{a([]),P.current.clear()},[e]),(0,f.useEffect)(()=>{if("sort"in e)a((null==m?void 0:m.pages.flatMap(e=>e.data))||[]);else{var t,s,i,r;let e=(null==m?void 0:null===(t=m.pages)||void 0===t?void 0:t.length)?(null==m?void 0:null===(s=m.pages)||void 0===s?void 0:s.length)-1:0,n=(null==m?void 0:null===(r=m.pages)||void 0===r?void 0:null===(i=r.at(-1))||void 0===i?void 0:i.data)||[];n.length>0&&!P.current.has(e)&&(a(e=>[...e,...l([...n])]),P.current.add(e))}},[m,e]),p)return(0,s.jsx)("div",{className:"mt-14",children:(0,s.jsx)("div",{className:"w-80 mx-auto mt-10 mb-25 flex justify-center items-center",children:(0,s.jsx)(N.Z,{})})});if(!p&&t&&0===t.length)return(0,s.jsx)("div",{className:"w-full mt-10 mb-20 flex justify-center items-center",children:(0,s.jsx)(C,{title1:"Can't find what you're looking for?",title2:"Your search yielded no results"})});if(!t||0===t.length)return null;let B=t[n],Z=null==i?void 0:i.chars_profile_pics.pfps.find(e=>e.char_id===B._id);return B.initial_image,(null==i?void 0:i.chars_profile_pics.visible)&&Z&&Z.pfp_url,(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"".concat((null==i?void 0:i.promo_bar.visible)?"mt-0":"mt-14"),children:[(0,s.jsx)("div",{className:"relative pt-[70px]",children:(0,s.jsxs)("div",{className:"pt-4 pb-6 px-8 relative",children:[(0,s.jsx)("button",{onClick:()=>{c(0===n?t.length-1:n-1)},className:"absolute left-2 top-1/2 z-10 transform -translate-y-1/2 bg-primary rounded-full p-2","aria-label":"Previous character",children:(0,s.jsx)(k,{className:"h-6 w-6"})}),(0,s.jsx)("div",{className:"flex justify-center items-center",children:(0,s.jsx)(A.Z,{character:B})}),(0,s.jsx)("button",{onClick:()=>{c(n===t.length-1?0:n+1)},className:"absolute right-2 top-1/2 z-10 transform -translate-y-1/2 bg-primary rounded-full p-2","aria-label":"Next character",children:(0,s.jsx)(S.Z,{className:"h-6 w-6"})})]})}),(0,s.jsxs)("div",{className:"py-4",children:[(0,s.jsx)("div",{className:"flex justify-center py-0",children:(0,s.jsx)(w.z,{onClick:()=>{window.scrollTo({top:500,behavior:"smooth"})},className:"h-8",children:"See All Partners"})}),(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4 md:gap-8 pt-4 pb-6 px-6 items-center justify-center",children:t.map(e=>{let t=null==i?void 0:i.chars_profile_pics.pfps.find(t=>t.char_id===e._id);return e.initial_image,(null==i?void 0:i.chars_profile_pics.visible)&&t&&t.pfp_url,(0,s.jsx)("div",{className:"flex min-w-[172px] w-full aspect-13/19",children:(0,s.jsx)(A.Z,{character:e,size:"small",isMyChar:!1,nsfw_preference:null==r?void 0:r.nsfw_preference})},e._id)})})]})]})})},z=a(50189),P=a(53130),B=e=>{let{promotionalData:t}=e,[a,l]=(0,f.useState)(""),[i,r]=(0,f.useState)(36e5),n=(0,x.useRouter)(),c=(0,o.useSetRecoilState)(z.X),{data:d}=(0,M.j)(),{mutateAsync:u}=(0,P.I)();(0,f.useEffect)(()=>{if(null==t?void 0:t.promo_bar.visible){let e=t.promo_bar,a=document.documentElement.style;l(e.title),a.setProperty("--promo_bar_title_color",e.title_color),a.setProperty("--promo_bar_bg_img_url","url(".concat(e.bg_img_url,")")),a.setProperty("--promo_bar_bg_color","rgba(0, 0, 0, ".concat(e.overlay_opacity,")"))}},[t]),(0,f.useEffect)(()=>{if(d){let e=d.promotional_banner;if(!e){c(!1);return}let t=new Date(e).getTime();console.log(t);let a=!1,s=setInterval(()=>{let e=t-Date.now();if(e<=0&&u({updates:{promotional_banner:null}}),e<=0&&!a){try{a=!0,clearInterval(s)}catch(e){console.error("Failed to update promotional data:",e)}return}r(e>0?e:0)},1e3);return r(t-Date.now()),()=>{s&&clearInterval(s)}}},[d]);let m=e=>"Day"===e?Math.floor(i/864e5):"Hour"===e?Math.floor(i%864e5/36e5):"Min"===e?Math.floor(i%36e5/6e4):"Sec"===e?Math.floor(i%6e4/1e3):void 0;return(null==d?void 0:d.promotional_banner)&&(0,s.jsxs)("div",{onClick:()=>n.push("/subscription"),className:"cursor-pointer flex ".concat((null==t?void 0:t.promo_bar.visible)?"promo-bar":"hidden"," mt-16 xs:mt-[4.3rem] sm:mt-0 justify-between sm:justify-around items-center bg-right bg-no-repeat bg-cover bg-blend-overlay gap-1"),children:[(0,s.jsx)("div",{className:"".concat((null==t?void 0:t.promo_bar.visible)?"promo-bar-title":""," px-4 py-2 mx-4 my-1 font-bold text-xs xs:text-sm sm:text-xl md:text-2xl"),children:a}),(0,s.jsx)("div",{className:"text-sm sm:text-base md:text-xl ".concat((null==t?void 0:t.promo_bar.visible)?"promo-countdown-capsule":""," flex justify-between px-6 py-1 mx-4 my-1 font-bold gap-1 sm:gap-2"),children:["Day","Hour","Min","Sec"].map(e=>(0,s.jsxs)("div",{className:"w-8 sm:w-10 text-xs xs:text-sm sm:text-base inline-block text-center bg-background/70 rounded-lg p-0.5",children:[m(e),(0,s.jsx)("div",{className:"text-[0.6rem] xs:text-xs opacity-70",children:e})]},e))})]})},Z=a(67236),I=a(23859),O=a(16576),L=a(74421),T=a(48297),D=a(42421),V=a(14392),W=a(22468),H=a(13498);let G=T.fC;T.ZA;let K=T.B4,q=f.forwardRef((e,t)=>{let{className:a,children:l,...i}=e;return(0,s.jsxs)(T.xz,{ref:t,className:(0,H.cn)("flex h-10 w-full items-center justify-between rounded-md bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...i,children:[l,(0,s.jsx)(T.JO,{asChild:!0,children:(0,s.jsx)(D.Z,{className:"h-4 w-4 opacity-50"})})]})});q.displayName=T.xz.displayName;let U=f.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(T.u_,{ref:t,className:(0,H.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(V.Z,{className:"h-4 w-4"})})});U.displayName=T.u_.displayName;let Y=f.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(T.$G,{ref:t,className:(0,H.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(D.Z,{className:"h-4 w-4"})})});Y.displayName=T.$G.displayName;let Q=f.forwardRef((e,t)=>{let{className:a,children:l,position:i="popper",...r}=e;return(0,s.jsx)(T.h_,{children:(0,s.jsxs)(T.VY,{ref:t,className:(0,H.cn)("relative z-50 max-h-96 min-w-32 overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...r,children:[(0,s.jsx)(U,{}),(0,s.jsx)(T.l_,{className:(0,H.cn)("p-1","popper"===i&&"h-(--radix-select-trigger-height) w-full min-w-(--radix-select-trigger-width)"),children:l}),(0,s.jsx)(Y,{})]})})});Q.displayName=T.VY.displayName,f.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(T.__,{ref:t,className:(0,H.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=T.__.displayName;let $=f.forwardRef((e,t)=>{let{className:a,children:l,...i}=e;return(0,s.jsxs)(T.ck,{ref:t,className:(0,H.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",a),...i,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(T.wU,{children:(0,s.jsx)(W.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(T.eT,{children:l})]})});$.displayName=T.ck.displayName,f.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,s.jsx)(T.Z0,{ref:t,className:(0,H.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=T.Z0.displayName;var X=a(16880),J=a(3274),ee=e=>{let{placeholder:t,searchText:a,onChange:l,onSearch:r,loading:n=!1}=e;var o=(0,X.Z)();try{let e=(0,i.P)().search;return(0,s.jsx)("form",{className:"relative ",onSubmit:e=>{e.preventDefault(),null==r||r(a)},children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"searchedEntry",placeholder:t,className:"bg-card text-white lg:w-[38vw] md:w-[50vw] w-[80vw] xs:w-[72vw] h-7 sm:w-[60vw] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50  flex sm:h-10  xs:h-8 rounded-2xl sm:rounded-md sm:px-3 xs:px-1.5 px-1.5 py-2 sm:text-sm  xs:text-[11px] text-[11px] placeholder:text-[#a1a1a1]  ",onChange:e=>l(e.target.value),value:a}),(0,s.jsx)("button",{"aria-label":"Search",type:"submit",className:"absolute right-2 top-1/2 -translate-y-1/2 text-white sm:scale-100 xs:scale-[.8] scale-[.65]",children:n?(0,s.jsx)(J.Z,{className:"w-4 h-4 animate-spin"}):e})]})})}finally{o.f()}},et=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,[a,s]=(0,f.useState)(e);return(0,f.useEffect)(()=>{let a=setTimeout(()=>s(e),t);return()=>clearTimeout(a)},[e,t]),a},ea=a(15964);let es={age:new Set(["18"]),personality:new Set(["Girlfriend","Wife","Vanilla","Free Spirited","Partying","Hopeless Romantic","Humiliation","Masochist","Sadistic","Dominant","Submissive"]),ethnicity:new Set(["Asian","White","Indian","Hispanic","Arabic","Black","Petite"])},el=e=>Object.entries(es).reduce((t,a)=>{let[s,l]=a;return l.has(e)?{[s]:e}:t},{});var ei=()=>{var e,t,a;let l=[(0,i.P)().femaleGender,(0,i.P)().maleGender,(0,i.P)().biGender],[r,n]=(0,f.useState)(""),[o,c]=(0,f.useState)(void 0),d=et(o,700),{filter:u,setFilter:m}=(0,ea.Z)(),{mutate:p}=(0,P.I)(),h=e=>{let t=el(e),[a,s]=Object.entries(t)[0],l={...u};delete l.age,delete l.personality,delete l.ethnicity,a in u&&u[a]===s?(n(""),delete u[a],m({...l})):(n(e),m({...l,...t}))};return(0,f.useEffect)(()=>{if(d&&m({...u,search:d}),""===d){let e={...u};delete e.search,m(e)}},[d]),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 ",role:"region","aria-label":"Character Filters",children:[(0,s.jsx)("h2",{className:"justify-center text-primary      font-bold lg:text-xl sm:text-lg  text-nowrap  sm:mb-3 xs:mb-1",children:"Find Someone To Chat With"}),(0,s.jsxs)("div",{className:"flex flex-row flex-wrap sm:gap-2 gap-1 justify-center items-center sm:px-8 px-2  w-full",role:"group","aria-label":"Primary Filters",children:[(0,s.jsxs)("div",{className:"sm:order-1 order-3 flex sm:gap-2 gap-1",role:"group","aria-label":"Gender Filters",children:[(0,s.jsx)(w.z,{"aria-label":"Filter female characters",variant:"capsule",size:"icon",value:"Female",onClick:()=>{m({...u,gender:"Female"}),p({updates:{gender_preference:"Female"}})},className:" sm:h-10 sm:w-10 xs::h-8 xs:w-8 w-7 h-7 hover:bg-[#bb0d89] ".concat("Female"===u.gender?"bg-[#EB0FAD]":"bg-card"),"aria-pressed":(null==u?void 0:u.gender)==="Female",children:(0,s.jsx)("span",{className:"sm:scale-100 xs:scale-[.75] scale-[.65]","aria-hidden":"true",children:l[0]})}),(0,s.jsx)(w.z,{"aria-label":"Filter male characters",variant:"capsule",size:"icon",value:"Male",onClick:()=>{m({...u,gender:"Male"}),p({updates:{gender_preference:"Male"}})},className:"sm:h-10 sm:w-10 xs::h-8 xs:w-8 w-7 h-7 hover:bg-[#0a96c5]\n						".concat((null==u?void 0:u.gender)==="Male"?"bg-[#22A3CD]":"bg-card"),"aria-pressed":(null==u?void 0:u.gender)==="Male",children:(0,s.jsx)("span",{className:"sm:scale-100 xs:scale-[.75] scale-[.65]","aria-hidden":"true",children:l[1]})}),(0,s.jsx)(w.z,{"aria-label":"Show both male and female characters",variant:"capsule",size:"icon",value:"Both",onClick:()=>{m({...u,gender:"Both"}),p({updates:{gender_preference:"Both"}})},className:"sm:h-10 sm:w-10 xs::h-8 xs:w-8 w-7 h-7 hover:bg-[#7e3bbd] \n						".concat((null==u?void 0:u.gender)&&(null==u?void 0:u.gender)!=="Both"?"bg-card":"bg-[#9B51E0]"),"aria-pressed":!(null==u?void 0:u.gender)||(null==u?void 0:u.gender)==="Both",children:(0,s.jsx)("span",{className:"sm:scale-100 xs:scale-[.75] scale-[.65]","aria-hidden":"true",children:l[2]})})]}),(0,s.jsxs)("div",{className:"sm:order-2 order-1 flex sm:gap-2 gap-1",role:"group","aria-label":"Style Filters",children:[(0,s.jsx)(w.z,{"aria-label":"Filter realistic characters",variant:"filter",value:"Realistic",onClick:()=>{m({...u,style:"Realistic"}),p({updates:{style_preference:"Realistic"}})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm  h-7 px-4 text-[11px] ".concat((null==u?void 0:u.style)==="Realistic"?"bg-[#b02851]":"bg-card"),"aria-pressed":(null==u?void 0:u.style)==="Realistic",children:"Realistic"}),(0,s.jsx)(w.z,{"aria-label":"Filter anime characters",variant:"filter",size:"lg",value:"Anime",onClick:()=>{m({...u,style:"Anime"}),p({updates:{style_preference:"Anime"}})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm  h-7 px-4 text-[11px] ".concat((null==u?void 0:u.style)==="Anime"?"bg-[#b02851]":"bg-card"),"aria-pressed":(null==u?void 0:u.style)==="Anime",children:"Anime"}),(0,s.jsx)(w.z,{"aria-label":"Show both realistic and anime characters",variant:"filter",size:"lg",value:"Both",onClick:()=>{m({...u,style:"Both"}),p({updates:{style_preference:"Both"}})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm  h-7 px-4 text-[11px] ".concat((null==u?void 0:u.style)&&(null==u?void 0:u.style)!=="Both"?"bg-card":"bg-[#b02851]"),"aria-pressed":!(null==u?void 0:u.style)||(null==u?void 0:u.style)==="Both",children:"Both"})]}),(0,s.jsx)("div",{className:"sm:order-3 order-2 flex sm:gap-2 gap-1",role:"group","aria-label":"Fantasy Filter",children:(0,s.jsxs)(w.z,{"aria-label":"Toggle Fantasy content - Currently ".concat((null==u?void 0:null===(e=u.tags)||void 0===e?void 0:e.includes("Fantasy"))?"enabled":"disabled"),variant:"filter",size:"lg",className:"sm:w-[120px] xs:w-[88px] w-[75px] relative sm:h-11 sm:px-4 xs:h-9 h-7 flex justify-end items-center space-x-2 px-2 group hover:bg-card active:bg-card",children:[(0,s.jsx)("span",{className:"sm:scale-90 xs:scale-[.65] scale-[.5] absolute sm:top-2.5 sm:left-2.5 xs:top-1.5 top-0.5 xs:left-1 left-0.5",children:(0,s.jsx)(L.r,{id:"fantasy-toggle",checked:null==u?void 0:null===(t=u.tags)||void 0===t?void 0:t.includes("Fantasy"),onCheckedChange:()=>{var e;(null==u?void 0:null===(e=u.tags)||void 0===e?void 0:e.includes("Fantasy"))?m({...u,tags:["!Fantasy"]}):m({...u,tags:["Fantasy"]})},thumbStyle:"bg-foreground shadow-[1px_1px_2px_1px_rgba(0,0,0,0.3)] data-[state=checked]:shadow-[-1px_1px_2px_1px_rgba(0,0,0,0.3)] border data-[state=checked]:translate-x-[0.88rem] data-[state=unchecked]:translate-x-[-0.12rem]",className:"h-5 w-9 ring-2 ring-[#e6e6e6] data-[state=checked]:ring-primary","aria-label":"fantasy content ".concat((null==u?void 0:null===(a=u.tags)||void 0===a?void 0:a.includes("Fantasy"))?"enabled":"disabled")})}),(0,s.jsx)("span",{className:"sm:text-md xs:text-sm text-[11px]",children:"Fantasy"})]})}),(0,s.jsx)("div",{className:"sm:order-4 order-3 flex sm:gap-2 gap-1",role:"group","aria-label":"NSFW Filter",children:(0,s.jsxs)(w.z,{"aria-label":"Toggle NSFW content - Currently ".concat((null==u?void 0:u.nsfw)?"enabled":"disabled"),variant:"filter",size:"lg",className:"sm:w-[120px] xs:w-[88px] w-[75px] relative sm:h-11 sm:px-4 xs:h-9 h-7 flex justify-end items-center space-x-2 px-2 group hover:bg-card active:bg-card",children:[(0,s.jsx)("span",{className:"sm:scale-90 xs:scale-[.65] scale-[.5] absolute sm:top-2.5 sm:left-2.5 xs:top-1.5 top-0.5 xs:left-1 left-0.5",children:(0,s.jsx)(L.r,{id:"nsfw-toggle",checked:null==u?void 0:u.nsfw,onCheckedChange:()=>{m({...u,nsfw:!(null==u?void 0:u.nsfw)}),p({updates:{nsfw_preference:!(null==u?void 0:u.nsfw)}})},thumbStyle:"bg-foreground shadow-[1px_1px_2px_1px_rgba(0,0,0,0.3)] data-[state=checked]:shadow-[-1px_1px_2px_1px_rgba(0,0,0,0.3)] border data-[state=checked]:translate-x-[0.88rem] data-[state=unchecked]:translate-x-[-0.12rem]",className:"h-5 w-9 ring-2 ring-[#e6e6e6] data-[state=checked]:ring-primary","aria-label":"NSFW content ".concat((null==u?void 0:u.nsfw)?"enabled":"disabled")})}),(0,s.jsx)("span",{className:"sm:text-md xs:text-sm text-[11px]",children:"NSFW"})]})}),(0,s.jsx)("div",{className:"order-5 flex",role:"search",children:(0,s.jsx)(ee,{placeholder:window.innerWidth>=500?"Search characters, kinks, personalities, and more!":window.innerWidth>=375?"Search for characters, kinks, and more!":"Search for characters, kinks, etc.",searchText:o||"",onChange:c,onSearch:c})})]}),(0,s.jsxs)("div",{className:"flex flex-row items-center justify-center gap-4 w-full sm:px-8 px-2",children:[(0,s.jsx)("div",{className:"flex sm:gap-2 gap-1",role:"group","aria-label":"Date Sort",children:(0,s.jsxs)(G,{onValueChange:e=>m({...u,sort:e}),value:"sort"in u?u.sort:"added",children:[(0,s.jsx)(q,{className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 h-7 px-4 bg-card hover:bg-card/80",children:(0,s.jsx)(K,{placeholder:"Sort By"})}),(0,s.jsxs)(Q,{children:[(0,s.jsx)($,{disabled:!0,value:"added",children:"Sort By"}),(0,s.jsx)($,{value:"-created_date",children:"Newest First"}),(0,s.jsx)($,{value:"created_date",children:"Oldest First"})]})]})}),(0,s.jsxs)("div",{className:"flex sm:gap-2 gap-1",role:"group","aria-label":"Publication Type Filters",children:[(0,s.jsx)(w.z,{"aria-label":"Filter official characters",variant:"filter",value:"official",onClick:()=>{m({...u,community:!1})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm h-7 px-4 text-[11px] ".concat(!1==u.community?"bg-[#b02851]":"bg-card"),"aria-pressed":!1==u.community,children:"Official"}),(0,s.jsx)(w.z,{"aria-label":"Filter community characters",variant:"filter",size:"lg",value:"Community",onClick:()=>{m({...u,community:!0})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm h-7 px-4 text-[11px] ".concat(!0==u.community?"bg-[#b02851]":"bg-card"),"aria-pressed":!0==u.community,children:"Community"}),(0,s.jsx)(w.z,{"aria-label":"Show all characters",variant:"filter",size:"lg",value:"All",onClick:()=>{m({...u,community:void 0})},className:"sm:h-11 sm:px-4 xs:h-8 xs:px-5 sm:text-md xs:text-sm h-7 px-4 text-[11px] ".concat(void 0===u.community?"bg-[#b02851]":"bg-card"),"aria-pressed":void 0===u.community,children:"All"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-x-auto max-w-[780px] custom-scrollbar",role:"group","aria-label":"Sub-category Filters",children:(0,s.jsx)("div",{className:"flex flex-row flex-nowrap sm:space-x-2 space-x-1 items-center",children:Object.values(O.W[0].sub_filter_categories).map(e=>(0,s.jsx)(w.z,{"aria-label":"Filter ".concat(e.display.toLowerCase()," characters"),variant:"capsule",size:"sm",onClick:()=>{h(e.display)},className:"sm:h-9 sm:px-5 sm:text-sm h-6 px-3 text-[11px] whitespace-nowrap ".concat(r===e.display?"bg-[#b02851]":"bg-card"),"aria-pressed":r===e.display,children:e.display},e._id))})})]})]})},er=a(79636),en=a(53428),eo=a(98280),ec=a(72224),ed=e=>{var t;let{isSidebarOpen:a,setIsSidebarOpen:l}=e,r=(0,o.useSetRecoilState)(R.q),{filter:n,setFilter:d}=(0,ea.Z)(),[u,m]=(0,f.useState)({...n}),[p,h]=(0,f.useState)([]),[x,g]=(0,f.useState)([]),[y,b]=(0,f.useState)([]),[C,j]=(0,f.useState)([]),[_,N]=(0,f.useState)([]),k=(0,o.useRecoilValue)(eo.gn),S=(0,o.useRecoilValue)(eo.n8),F=(0,o.useRecoilValue)(eo.jo),A=(0,o.useRecoilValue)(c.loggedIn),M=(0,o.useSetRecoilState)(v.PC);(0,ec.N)(),(0,f.useEffect)(()=>{if(S&&Object.keys(S).length>0){var e,t,a,s,l,i,r,n;h(Array.from(new Set([null===(e=S.Female.Realistic)||void 0===e?void 0:e.ethnicities,null===(t=S.Female.Anime)||void 0===t?void 0:t.ethnicities,null===(a=S.Male.Anime)||void 0===a?void 0:a.ethnicities,null===(s=S.Male.Realistic)||void 0===s?void 0:s.ethnicities].flatMap(e=>e||[]).map(e=>e.display)))),g(Array.from(new Set([null===(l=S.Female.Realistic)||void 0===l?void 0:l.body_types,null===(i=S.Female.Anime)||void 0===i?void 0:i.body_types,null===(r=S.Male.Anime)||void 0===r?void 0:r.body_types,null===(n=S.Male.Realistic)||void 0===n?void 0:n.body_types].flatMap(e=>e||[]).map(e=>e.display))))}},[S]),(0,f.useEffect)(()=>{k&&Object.keys(k).length>0&&b((null==k?void 0:k.kinks).map(e=>e.display))},[k]),(0,f.useEffect)(()=>{F&&Object.keys(F).length>0&&(j((null==F?void 0:F.personalities).map(e=>e.display)),N([...null==F?void 0:F.relationships.Male,...null==F?void 0:F.relationships.Female,...null==F?void 0:F.relationships.Neutral].map(e=>e.display)))},[F]);let E=[{label:"Response Style",items:["Texting","Roleplay","Both"],filter:"character_level_settings.mode"in u?"Texting"==u["character_level_settings.mode"]?"Texting":"Roleplay":"Both",categoryKey:"category1",setFilter:e=>{if("Texting"==e)m({...u,"character_level_settings.mode":"Texting"});else if("Roleplay"==e)m({...u,"character_level_settings.mode":"Roleplay"});else{let e={...u};delete e["character_level_settings.mode"],m(e)}}},{label:"Gender",items:["Male","Female","Both"],filter:"gender"in u?"Male"==u.gender?"Male":"Female":"Both",categoryKey:"category1",setFilter:e=>{if("Male"==e)m({...u,gender:"Male"});else if("Female"==e)m({...u,gender:"Female"});else{let e={...u};delete e.gender,m(e)}}},{label:"Style",items:["Realistic","Anime","Both"],filter:"style"in u?"Realistic"==u.style?"Realistic":"Anime":"Both",categoryKey:"category2",setFilter:e=>{if("Realistic"==e)m({...u,style:"Realistic"});else if("Anime"==e)m({...u,style:"Anime"});else{let e={...u};delete e.style,m(e)}}},{label:"NSFW Characters",items:["Show Only NSFW","Show Only SFW","Both"],filter:"nsfw"in u?u.nsfw?"Show Only NSFW":"Show Only SFW":"Both",categoryKey:"category4",setFilter:e=>{if("Show Only NSFW"==e)m({...u,nsfw:!0});else if("Show Only SFW"==e)m({...u,nsfw:!1});else{let e={...u};delete e.nsfw,m(e)}}},{label:"Content Type",items:["Official","Community","All"],filter:"community"in u?u.community?"Community":"Official":"All",categoryKey:"category4",setFilter:e=>{if("Official"==e)m({...u,community:!1});else if("Community"==e)m({...u,community:!0});else{let e={...u};delete e.community,m(e)}}},{label:"Fantasy",items:["Show Fantasy Partners","Show Non-Fantasy Partners"],filter:(null===(t=u.tags)||void 0===t?void 0:t.includes("Fantasy"))?"Show Fantasy Partners":"Show Non-Fantasy Partners",categoryKey:"category4",setFilter:e=>{if("Show Fantasy Partners"==e)m({...u,tags:["Fantasy"]});else if("Show Non-Fantasy Partners"==e){var t;m({...u,tags:null===(t=u.tags)||void 0===t?void 0:t.filter(e=>"Fantasy"!==e)})}}},{label:"Date Added",items:["Newest First","Oldest First"],filter:"sort"in u?"-created_date"===u.sort?"Newest First":"Oldest First":"Newest First",categoryKey:"category4",setFilter:e=>{"Oldest First"==e?m({...u,sort:"created_date"}):m({...u,sort:"-created_date"})}},{label:"Ethnicities",items:p,filter:"ethnicity"in u&&u.ethnicity||"",categoryKey:"category2",setFilter:e=>{if(e===u.ethnicity||""===e){let e={...u};delete e.ethnicity,m(e)}else m({...u,ethnicity:e})}},{label:"Kinks",items:y,filter:"kink"in u&&u.kink||"",categoryKey:"category2",setFilter:e=>{if(e===u.kink||""===e){let e={...u};delete e.kink,m(e)}else m({...u,kink:e})}},{label:"Personality",items:C,filter:"personality"in u&&u.personality||"",categoryKey:"category2",setFilter:e=>{if(e===u.personality||""===e){let e={...u};delete e.personality,m(e)}else m({...u,personality:e})}},{label:"Relationship",items:_,filter:"relationship"in u&&u.relationship||"",categoryKey:"category2",setFilter:e=>{if(e===u.relationship||""===e){let e={...u};delete e.relationship,m(e)}else m({...u,relationship:e})}},{label:"Age",items:["18","19","20-29","30-39","40-49","50-59","60-69","70-79","80+"],filter:"age"in u&&u.age||"",categoryKey:"category2",setFilter:e=>{if(e===u.age||""===e){let e={...u};delete e.age,m(e)}else m({...u,age:e})}},{label:"Body Type",items:x,filter:"body_type"in u&&u.body_type||"",categoryKey:"category2",setFilter:e=>{if(e===u.body_type||""===e){let e={...u};delete e.body_type,m(e)}else m({...u,body_type:e})}}],z=()=>{r(u),d(u),l(!1)};return(0,s.jsx)(s.Fragment,{children:a&&(0,s.jsx)("div",{className:"fixed top-0 right-0 h-full w-full bg-background shadow-lg z-50",children:(0,s.jsxs)("div",{className:"flex flex-col justify-start h-full sidebar-background",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6",children:[(0,s.jsx)("h1",{className:"text-xl font-bold mt-4",children:"Filters"}),(0,s.jsxs)("div",{className:"flex flex-row space-x-3 pb-3",children:[(0,s.jsx)(w.z,{"aria-label":"Save  filter",onClick:()=>{z()},className:"h-8 xl:hidden flex mt-4 z-50 justify-end items-center",children:"Save"}),(0,s.jsx)("button",{"aria-label":"close filter",onClick:()=>{l(!1)},className:"xl:hidden flex mt-4 z-50 justify-end items-center",children:(0,i.P)().cross})]})]}),(0,s.jsx)("div",{className:"p-4 px-6 overflow-y-scroll",children:(0,s.jsx)("div",{children:E.map(e=>{let{label:t,items:a,filter:l,categoryKey:i,setFilter:r}=e;return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 py-4",children:[(0,s.jsx)("span",{className:"text-sm",children:t}),(0,s.jsx)("div",{className:"flex-grow h-[1px] bg-gray-500"})]}),(0,s.jsx)("div",{className:"flex flex-row flex-wrap  items-center",children:a.map(e=>(0,s.jsx)(w.z,{"aria-label":"filter ".concat(e),variant:"capsule",size:"sm",onClick:()=>{if(l==e)r("");else{if("Show Only NSFW"===e&&!A){M(()=>({open:!0,isSignup:!0,text:"Sign Up to Access NSFW Content",image:"",gaClass:"ga-reg_popup_nsfw_filter"}));return}r(e)}},className:"my-1 mx-[2px] sm:h-9 sm:px-5 sm:text-sm h-6 px-3 text-[11px] whitespace-nowrap ".concat(l===e?"bg-[#b02851]":"bg-card"),children:e},e))})]},t)})})})]})})})},eu=()=>{let[e,t]=(0,f.useState)(""),[a,l]=(0,o.useRecoilState)(R.q),i=et(e,700),[r,n]=(0,f.useState)(!1);return(0,f.useEffect)(()=>{if(i&&l({...a,search:i}),""===i){let e={...a};delete e.search,l({...e})}},[i]),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsxs)("div",{style:{boxShadow:"#000 0px 25px 20px -20px"},className:"flex bg-black fixed top-16  py-4 z-10 flex-row flex-wrap sm:gap-2 gap-1 justify-around items-center px-2 sm:px-8 xs:px-7 w-full",children:[(0,s.jsx)("div",{className:"order-1 flex   ",children:(0,s.jsx)(er.Z,{placeholderText:"   Search for your perfect AI partner!",onSearch:e=>t(e),searchText:e,setSearchText:t})}),(0,s.jsx)("div",{onClick:()=>n(!0),className:"order-2 bg-card rounded-2xl p-1 cursor-pointer",children:(0,s.jsx)(en.k1,{className:"w-6 h-6 xs:w-7 xs:h-7"})})]}),(0,s.jsx)(ed,{isSidebarOpen:r,setIsSidebarOpen:n})]})};let em=e=>{let t="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===t.length){var a;return null===(a=t.pop())||void 0===a?void 0:a.split(";").shift()}};var ep=()=>{let e=(0,o.useSetRecoilState)(h.A),t=(0,x.useSearchParams)(),a=(0,o.useRecoilValue)(c.loggedIn),l=(0,y.a)(),i=(0,o.useSetRecoilState)(v.Yu),r=(0,x.useSearchParams)(),{data:n}=(0,_.e)(),m=(0,I.d)();return(0,f.useEffect)(()=>{let s=t.get("invite");s&&(e(s),a&&l(),(0,g.default)("Please sign up to get the free trial",{duration:7e3}))},[]),(0,f.useEffect)(()=>{let e=em("suppress_popup");if(!localStorage.getItem("pref")&&0===r.size){if(e){let e=()=>{i(!0),document.removeEventListener("click",e)};document.addEventListener("click",e)}else i(!0)}},[r]),(0,f.useEffect)(()=>{document.title="Secret Desires | Chat with AI Girlfriends & Boyfriends";let e=document.querySelector('meta[name="description"]');if(e)e.setAttribute("content","Manage and personalize your AI Girlfriends and Boyfriends. Track their development and enhance interactions for a fully tailored experience.");else{let e=document.createElement("meta");e.name="description",e.content="Manage and personalize your AI Girlfriends and Boyfriends. Track their development and enhance interactions for a fully tailored experience.",document.head.appendChild(e)}},[]),(0,f.useEffect)(()=>{if(null==n?void 0:n.page_bg.home.visible){let e=n.page_bg.home,t=document.documentElement.style;t.setProperty("--home_bg_url","url(".concat(e.img_url,")")),t.setProperty("--home_bg_cover",e.cover_whole_screen?"cover":"600px 600px")}},[n]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(p(),{children:[(0,s.jsx)("title",{children:"Secret Desires | Chat with AI Girlfriends & Boyfriends"}),(0,s.jsx)("meta",{name:"description",content:"Create spicy AI Girlfriends and Boyfriends. Enjoy private chats, voice calls, and images with advanced face swap and voice cloning. The most immersive AI companion experience."})]}),n&&(0,s.jsx)(B,{promotionalData:n}),(0,s.jsxs)("div",{className:"min-h-screen ".concat((null==n?void 0:n.page_bg.home.visible)?"promotional-home-bg":"background"),children:[m?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eu,{}),(0,s.jsx)(E,{})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(ei,{}),(0,s.jsx)(u.Z,{})]}),(0,s.jsxs)("section",{className:"bg-card w-full     flex flex-col items-center gap-5    sm:pt-10 py-8 sm:px-[15vw] px-[10vw]    text-center tracking-wide",children:[(0,s.jsx)("div",{className:"",children:(0,s.jsx)("p",{className:"text-center     font-bold sm:text-lg text-sm",children:"Secret Desires creates immersive experiences that blur the line between simulation and reality. Using cutting-edge artificial intelligence technology and unmatched creativity, we create experiences so vivid, they feel utterly natural. With Secret Desires, every moment is an escape into a world where dreams feel real."})}),(0,s.jsx)("h3",{className:"text-primary font-bold sm:text-xl text-sm pt-4 ",children:"Experience Limitless Possibilities"})]}),(0,s.jsxs)("section",{className:"px-5 sm:px-20 py-20",children:[(0,s.jsx)("h2",{className:"text-3xl text-center",children:"Frequently Asked Questions"}),(0,s.jsx)(Z.default,{justifyCenter:!0})]})]})]})}},53428:function(e,t,a){"use strict";a.d(t,{HX:function(){return n},YI:function(){return l},h_:function(){return c},k1:function(){return o},kB:function(){return i},x2:function(){return r}});var s=a(57437);function l(e){let{color:t,className:a=""}=e;return(0,s.jsxs)("svg",{className:a,width:"100",height:"100",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsxs)("g",{clipPath:"url(#clip0_3295_196)",children:[(0,s.jsx)("path",{d:"M14.229 3.64959C16.2642 3.64959 18.2537 4.25309 19.9458 5.38377C21.638 6.51445 22.9569 8.12153 23.7357 10.0018C24.5146 11.882 24.7183 13.951 24.3213 15.9471C23.9243 17.9431 22.9442 19.7766 21.5051 21.2157C20.0661 22.6548 18.2326 23.6348 16.2365 24.0319C14.2404 24.4289 12.1715 24.2251 10.2912 23.4463C8.41095 22.6675 6.80387 21.3486 5.67319 19.6564C4.54251 17.9642 3.93902 15.9748 3.93902 13.9396C3.95403 11.2151 5.04297 8.60655 6.96947 6.68005C8.89598 4.75354 11.5046 3.6646 14.229 3.64959ZM14.229 1.36292C11.7416 1.36292 9.31002 2.10053 7.24179 3.48247C5.17357 4.86442 3.56159 6.82862 2.60969 9.12671C1.65779 11.4248 1.40873 13.9535 1.89401 16.3932C2.37928 18.8328 3.57709 21.0738 5.33597 22.8326C7.09485 24.5915 9.3358 25.7893 11.7754 26.2746C14.2151 26.7599 16.7438 26.5108 19.0419 25.5589C21.34 24.607 23.3042 22.995 24.6861 20.9268C26.0681 18.8586 26.8057 16.427 26.8057 13.9396C26.8132 12.2859 26.4931 10.6471 25.8638 9.11777C25.2344 7.58848 24.3083 6.19903 23.1389 5.02967C21.9696 3.86031 20.5801 2.93421 19.0508 2.30485C17.5215 1.67549 15.8827 1.35536 14.229 1.36292Z",fill:"currentColor"}),(0,s.jsx)("path",{d:"M19.5458 7.82289L11.9426 11.6531L8.11244 19.2562C8.07369 19.3678 8.06715 19.488 8.09357 19.6032C8.11998 19.7183 8.17829 19.8237 8.26182 19.9072C8.34534 19.9907 8.4507 20.049 8.56583 20.0754C8.68095 20.1019 8.80119 20.0953 8.91278 20.0566L16.5159 16.2264L20.3461 8.62322C20.3849 8.51164 20.3914 8.3914 20.365 8.27628C20.3386 8.16115 20.2803 8.05579 20.1967 7.97227C20.1132 7.88874 20.0078 7.83043 19.8927 7.80402C19.7776 7.7776 19.6574 7.78414 19.5458 7.82289ZM14.2293 15.0831C14.0031 15.0831 13.7821 15.016 13.5941 14.8904C13.4061 14.7647 13.2595 14.5862 13.173 14.3773C13.0864 14.1683 13.0638 13.9385 13.1079 13.7167C13.152 13.4949 13.2609 13.2912 13.4208 13.1313C13.5807 12.9714 13.7844 12.8625 14.0062 12.8184C14.228 12.7742 14.4579 12.7969 14.6668 12.8834C14.8757 12.97 15.0543 13.1165 15.1799 13.3045C15.3056 13.4925 15.3726 13.7136 15.3726 13.9397C15.3726 14.243 15.2522 14.5338 15.0377 14.7482C14.8233 14.9626 14.5325 15.0831 14.2293 15.0831Z",fill:"currentColor"})]}),(0,s.jsx)("defs",{children:(0,s.jsx)("clipPath",{id:"clip0_3295_196",children:(0,s.jsx)("rect",{width:"27.44",height:"27.44",fill:"white",transform:"translate(0.508789 0.219727)"})})})]})}function i(e){let{color:t,background:a,className:l=""}=e;return(0,s.jsxs)("svg",{width:"100",height:"100",className:l,viewBox:"0 0 27 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M16.4977 23.6258L15.8779 24.673C15.3255 25.6063 13.9455 25.6063 13.393 24.673L12.7732 23.6258C12.2925 22.8135 12.0521 22.4076 11.666 22.1829C11.2799 21.9583 10.7938 21.95 9.82157 21.9333C8.38627 21.9085 7.48609 21.8206 6.73114 21.5079C5.33039 20.9276 4.21751 19.8148 3.6373 18.414C3.20215 17.3635 3.20215 16.0316 3.20215 13.368V12.2247C3.20215 8.48206 3.20215 6.61074 4.04456 5.23607C4.51593 4.46686 5.16266 3.82013 5.93187 3.34876C7.30654 2.50635 9.17786 2.50635 12.9205 2.50635H16.3505C20.0931 2.50635 21.9644 2.50635 23.3391 3.34876C24.1083 3.82013 24.755 4.46686 25.2264 5.23607C26.0688 6.61074 26.0688 8.48206 26.0688 12.2247V13.368C26.0688 16.0316 26.0688 17.3635 25.6337 18.414C25.0534 19.8148 23.9406 20.9276 22.5398 21.5079C21.7849 21.8206 20.8847 21.9085 19.4494 21.9333C18.4771 21.95 17.991 21.9583 17.6049 22.1829C17.2188 22.4074 16.9784 22.8135 16.4977 23.6258Z",fill:t}),(0,s.jsx)("path",{d:"M13.4806 16.5786C11.9636 15.4607 9.49023 13.3306 9.49023 11.3268C9.49023 8.26588 12.3201 7.12309 14.6352 9.4876C16.9504 7.12309 19.7802 8.26588 19.7802 11.3268C19.7802 13.3306 17.3069 15.4607 15.7899 16.5786C15.2707 16.9614 15.011 17.1527 14.6352 17.1527C14.2594 17.1527 13.9998 16.9614 13.4806 16.5786Z",fill:a})]})}function r(e){let{color:t,className:a=""}=e;return(0,s.jsxs)("svg",{className:a,width:"100%",height:"100%",viewBox:"0 0 28 28",transform:"translate(3,1)",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("g",{clipPath:"url(#clip0_3153_623)",children:(0,s.jsx)("path",{d:"M0.523438 18.8104C0.523438 19.9519 0.925891 20.9177 1.7308 21.708C2.5357 22.4983 3.50891 22.9007 4.65041 22.9154H18.3704C19.4973 22.9154 20.4632 22.5129 21.2681 21.708C22.073 20.9031 22.4754 19.9372 22.4754 18.8104V5.09035C22.4754 3.94885 22.073 2.97565 21.2681 2.17074C20.4632 1.36583 19.4973 0.963379 18.3704 0.963379H4.65041C3.50891 0.963379 2.5357 1.36583 1.7308 2.17074C0.925891 2.97565 0.523437 3.94885 0.523438 5.09035L0.523438 18.8104ZM3.26744 18.8104V5.09035C3.26744 4.70985 3.39915 4.38789 3.66257 4.12447C3.926 3.86104 4.25528 3.72201 4.65041 3.70738H18.3704C18.7363 3.70738 19.0582 3.84641 19.3363 4.12447C19.6144 4.40253 19.7461 4.72449 19.7314 5.09035V18.8104C19.7314 19.1909 19.5997 19.5128 19.3363 19.7762C19.0729 20.0397 18.7509 20.1714 18.3704 20.1714H4.65041C4.26991 20.1714 3.94063 20.0397 3.66257 19.7762C3.38452 19.5128 3.2528 19.1909 3.26744 18.8104ZM6.01144 11.9394C6.01144 12.3199 6.14315 12.6492 6.40657 12.9272C6.67 13.2053 6.99928 13.337 7.39441 13.3224H10.1384V16.0664C10.1384 16.4469 10.2701 16.7688 10.5335 17.0322C10.797 17.2957 11.1189 17.4274 11.4994 17.4274C11.8799 17.4274 12.2019 17.2957 12.4653 17.0322C12.7288 16.7688 12.8678 16.4469 12.8824 16.0664V13.3224H15.6264C15.9923 13.3224 16.3142 13.1906 16.5923 12.9272C16.8704 12.6638 17.0021 12.3345 16.9874 11.9394C16.9728 11.5442 16.8411 11.2223 16.5923 10.9735C16.3435 10.7247 16.0216 10.593 15.6264 10.5784H12.8824V7.83435C12.8824 7.45385 12.7434 7.13189 12.4653 6.86847C12.1873 6.60504 11.8653 6.46601 11.4994 6.45138C11.1336 6.43674 10.8116 6.57577 10.5335 6.86847C10.2555 7.16116 10.1238 7.48312 10.1384 7.83435V10.5784H7.39441C7.01391 10.5784 6.68463 10.7101 6.40657 10.9735C6.12851 11.2369 5.9968 11.5589 6.01144 11.9394Z",fill:t})}),(0,s.jsx)("defs",{children:(0,s.jsx)("clipPath",{id:"clip0_3153_623",children:(0,s.jsx)("rect",{width:"100",height:"100",fill:"white",transform:"translate(0.523438 0.963379)"})})})]})}function n(e){let{color:t,className:a=""}=e;return(0,s.jsxs)("svg",{className:a,width:"29",height:"28",viewBox:"0 0 29 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M25.6881 19.0174C25.7836 17.8456 25.7964 16.4175 25.7981 14.7372C25.7985 14.2967 25.4411 13.9394 25.0006 13.9394C24.5601 13.9394 24.2031 14.2968 24.2027 14.7373C24.201 16.4267 24.1885 17.7765 24.0979 18.8879C24.0019 20.0673 23.8219 20.9034 23.519 21.5398L20.5086 18.8305C19.403 17.8354 17.7564 17.7363 16.5395 18.5915L16.2223 18.8145C15.3766 19.4088 14.2261 19.3092 13.4951 18.5783L8.93268 14.0158C8.02205 13.1052 6.56132 13.0565 5.59213 13.9046L4.52736 14.8362C4.52699 14.547 4.52699 14.2483 4.52699 13.9394C4.52699 11.41 4.52868 9.59321 4.71458 8.21059C4.89737 6.85092 5.24505 6.03121 5.85091 5.42535C6.45678 4.81948 7.27649 4.47181 8.63615 4.289C9.86883 4.12327 11.4466 4.10396 13.5668 4.10172C14.0073 4.10125 14.365 3.74429 14.365 3.30374C14.365 2.86321 14.0075 2.50588 13.5669 2.50635C11.4763 2.50853 9.75861 2.52839 8.42358 2.70788C6.88701 2.91447 5.67452 3.34558 4.72283 4.29726C3.77114 5.24895 3.34004 6.46145 3.13345 7.99802C2.93162 9.49921 2.93163 11.4232 2.93164 13.8784V13.9404C2.93164 14.5016 2.93164 15.0335 2.93385 15.5363C2.94249 17.5134 2.98413 19.1054 3.21173 20.3851C3.44343 21.6878 3.87913 22.7378 4.72283 23.5816C5.67452 24.5332 6.88701 24.9643 8.42358 25.1709C9.92478 25.3727 11.8488 25.3727 14.3039 25.3727H14.426C16.8811 25.3727 18.8052 25.3727 20.3064 25.1709C21.8429 24.9643 23.0554 24.5332 24.0072 23.5816C24.3425 23.2463 24.6179 22.8734 24.842 22.4572C25.3502 21.5135 25.5773 20.3773 25.6881 19.0174Z",fill:t}),(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.6528 12.7961C18.2275 12.7961 17.0147 12.7961 16.2613 12.0426C15.5078 11.2892 15.5078 10.0765 15.5078 7.6511C15.5078 5.22573 15.5078 4.01304 16.2613 3.25957C17.0147 2.5061 18.2275 2.5061 20.6528 2.5061C23.0782 2.5061 24.2909 2.5061 25.0444 3.25957C25.7978 4.01304 25.7978 5.22573 25.7978 7.6511C25.7978 10.0765 25.7978 11.2892 25.0444 12.0426C24.2909 12.7961 23.0782 12.7961 20.6528 12.7961ZM18.9896 9.61305C18.1152 8.84638 17.2228 7.82481 17.2228 6.84851C17.2228 4.82124 19.1093 4.06435 20.6528 5.6304C22.1963 4.06435 24.0828 4.82124 24.0828 6.84849C24.0828 7.82482 23.1904 8.84639 22.3161 9.61306C21.6345 10.2106 21.2938 10.5094 20.6528 10.5094C20.0119 10.5094 19.6711 10.2106 18.9896 9.61305Z",fill:t})]})}let o=e=>{let{color:t="white",className:a=""}=e;return(0,s.jsx)("svg",{width:"100",height:"100",viewBox:"0 0 27 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:a,children:(0,s.jsx)("path",{d:"M23.1496 6.7248H20.9496M23.1496 12.4998H17.6496M23.1496 18.2748H17.6496M7.74961 20.1998V14.0025C7.74961 13.8023 7.74961 13.7022 7.72715 13.6064C7.70723 13.5215 7.67427 13.4393 7.62918 13.362C7.57835 13.275 7.50688 13.1968 7.36394 13.0404L3.73528 9.07163C3.59234 8.91529 3.52087 8.83712 3.47004 8.75007C3.42495 8.67284 3.39199 8.59063 3.37207 8.50568C3.34961 8.40993 3.34961 8.30982 3.34961 8.1096V6.3398C3.34961 5.80076 3.34961 5.53123 3.4695 5.32534C3.57497 5.14424 3.74324 4.99699 3.95022 4.90471C4.18552 4.7998 4.49355 4.7998 5.10961 4.7998H14.7896C15.4057 4.7998 15.7137 4.7998 15.949 4.90471C16.156 4.99699 16.3242 5.14424 16.4297 5.32534C16.5496 5.53123 16.5496 5.80076 16.5496 6.3398V8.1096C16.5496 8.30982 16.5496 8.40993 16.5272 8.50568C16.5073 8.59063 16.4743 8.67284 16.4292 8.75007C16.3783 8.83712 16.3068 8.91529 16.1639 9.07163L12.5353 13.0404C12.3924 13.1968 12.3209 13.275 12.2701 13.362C12.225 13.4393 12.192 13.5215 12.172 13.6064C12.1496 13.7022 12.1496 13.8023 12.1496 14.0025V17.3123L7.74961 20.1998Z",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})},c=()=>(0,s.jsx)("svg",{viewBox:"0 0 24 24",className:"absolute animate-pulse text-white!",width:"32px",height:"32px",style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:20},children:(0,s.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.79544 4.16635C6.23434 4.27607 5.61457 4.56036 5.09669 4.94552C4.56822 5.33858 3.96639 6.12946 3.73283 6.7378C3.47265 7.41551 3.44195 7.59107  3.43962 8.41576C3.43776 9.06466 3.45021 9.22696 3.52894 9.58022C3.8713 11.1158 4.62349 12.345 6.1818 13.9154C7.20152 14.9431 8.17905 15.7504 9.42289 16.592C9.86243 16.8895 10.7544 17.4582 11.0343 17.6195C11.0969 17.6555 11.4079 17.4719 12.4053  16.8101C14.1693 15.6395 15.8072 14.2409 16.7518 13.0986C16.8599 12.9678 16.9738 12.8339 17.0047 12.8011C17.1059 12.694 17.4689 12.1884 17.6905 11.8461C18.1671 11.1098 18.4649 10.4019 18.6328 9.60625C18.746 9.06938 18.7753 8.07167 18.6905 7.64166C18.5037  6.69403 18.1478 6.00982 17.5023 5.35692C16.9478 4.7962 16.3107 4.41881 15.5855 4.22167C15.0823 4.08492 14.3296 4.0927 13.7965  4.24016C12.9336 4.47889 12.1482 4.96739 11.4125 5.72303L11.0913 6.05292L10.7211 5.68295C9.97811 4.94032 9.17069 4.44873 8.32333  4.22305C7.93293 4.11905 7.18015 4.09111 6.79544 4.16635Z",fill:"white"})})},79636:function(e,t,a){"use strict";var s=a(57437),l=a(16880),i=a(3114),r=a(38472),n=a(13498);t.Z=e=>{let{placeholderText:t,onSearch:a,searchText:o,setSearchText:c,onButtonClick:d}=e;var u=(0,l.Z)();try{let e=(0,i.P)().search,l=async e=>{let t=e.get("searchedEntry");c(t)},u=async()=>{try{await r.default.post("".concat(n.fw,"/query"),{headers:{"Content-Type":"application/json"},body:JSON.stringify(o)})}catch(e){console.error(e)}};return(0,s.jsx)("form",{className:"relative ",action:l,onSubmit:e=>{e.preventDefault(),a(o),u()},children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",name:"searchedEntry",placeholder:t,className:"bg-card text-white lg:w-[38vw] md:w-[50vw] w-[80vw] xs:w-[72vw] h-7 xs:h-9 sm:w-[60vw] focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50       flex sm:h-10  xs:h-8 rounded-2xl sm:rounded-md sm:px-3 xs:px-1.5 px-1.5 py-2 sm:text-sm  xs:text-[11px] text-[11px] placeholder:text-[#a1a1a1]  ",onChange:e=>{c(e.target.value),a(e.target.value)},value:o}),(0,s.jsx)("button",{"aria-label":"Search",type:"submit",className:"absolute right-2 top-1/2 -translate-y-1/2 text-white sm:scale-100 xs:scale-[.8] scale-[.65]",onClick:()=>{d&&d()},children:e})]})})}finally{u.f()}}},74421:function(e,t,a){"use strict";a.d(t,{r:function(){return n}});var s=a(57437),l=a(2265),i=a(9646),r=a(13498);let n=l.forwardRef((e,t)=>{let{className:a,thumbStyle:l,...n}=e;return(0,s.jsx)(i.fC,{className:(0,r.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-foreground group-hover:border-border",a),...n,ref:t,children:(0,s.jsx)(i.bU,{className:(0,r.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0",l)})})});n.displayName=i.fC.displayName},16576:function(e,t,a){"use strict";a.d(t,{W:function(){return s}});let s=[{_id:"sub_filter_categories",sub_filter_categories:{18:{_id:"12",display:"18",nsfw:!0},asian:{_id:"1",display:"Asian",nsfw:!1},girlfriend:{_id:"2",display:"Girlfriend",nsfw:!1},wife:{_id:"3",display:"Wife",nsfw:!1},white:{_id:"4",display:"White",nsfw:!1},indian:{_id:"5",display:"Indian",nsfw:!1},hispanic:{_id:"6",display:"Hispanic",nsfw:!1},arabic:{_id:"7",display:"Arabic",nsfw:!1},black:{_id:"8",display:"Black",nsfw:!1},vanilla:{_id:"9",display:"Vanilla",nsfw:!1},free_spirited:{_id:"10",display:"Free Spirited",nsfw:!1},petite:{_id:"11",display:"Petite",nsfw:!1},partying:{_id:"13",display:"Partying",nsfw:!1},humiliation:{_id:"14",display:"Humiliation",nsfw:!0},masochist:{_id:"15",display:"Masochist",nsfw:!0},sadistic:{_id:"16",display:"Sadistic",nsfw:!0},hopeless_romantic:{_id:"17",display:"Hopeless Romantic",nsfw:!1},dominant:{_id:"18",display:"Dominant",nsfw:!0},submissive:{_id:"19",display:"Submissive",nsfw:!0}}}]},72224:function(e,t,a){"use strict";a.d(t,{N:function(){return n}});var s=a(76351),l=a(22351),i=a(99441),r=a(98280);let n=()=>{let e=(0,i.useSetRecoilState)(r.n8),t=(0,i.useSetRecoilState)(r.jo),a=(0,i.useSetRecoilState)(r.gn),n=(0,i.useSetRecoilState)(r.hE),o=async()=>{try{var s,i,r,o,c;let d=(await l.j.get("/character_options/")).data,u={Female:{Style:{Realistic:d.female_real.path,Anime:d.female_anime.path},Realistic:{age:"",...d.female_real.options},Anime:{age:"",...d.female_anime.options}},Male:{Style:{Realistic:d.male_real.path,Anime:d.male_anime.path},Realistic:{age:"",...d.male_real.options},Anime:{age:"",...d.male_anime.options}},hair_colors:d.hair_colors},m={relationships:{Male:null==d?void 0:null===(s=d.relationships)||void 0===s?void 0:s.filter(e=>"male"===e.gender),Female:null==d?void 0:null===(i=d.relationships)||void 0===i?void 0:i.filter(e=>"female"===e.gender),Neutral:null==d?void 0:null===(r=d.relationships)||void 0===r?void 0:r.filter(e=>"neutral"===e.gender)},personalities:null==d?void 0:d.personalities,voices:{Male:null==d?void 0:null===(o=d.voices[0])||void 0===o?void 0:o.male_voices,Female:null==d?void 0:null===(c=d.voices[0])||void 0===c?void 0:c.female_voices}},p={hobbies:null==d?void 0:d.hobbies,kinks:null==d?void 0:d.kinks,work:null==d?void 0:d.occupations},f={...null==d?void 0:d.review_page_icons};return e(u),t(m),a(p),n(f),{looks_data:u,personality_data:m,lifestyle_data:p,review_page_icons:f}}catch(e){console.log(e,"error")}};return(0,s.useQuery)({queryKey:["createOptions"],queryFn:o,staleTime:1/0,gcTime:1/0})}},23859:function(e,t,a){"use strict";a.d(t,{d:function(){return l}});var s=a(2265);function l(){let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let e=()=>{t(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e}},15964:function(e,t,a){"use strict";var s=a(2265),l=a(16463),i=a(14738),r=a(86460),n=a(99441),o=a(31231);t.Z=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,l.useRouter)(),a=(0,l.usePathname)(),c=(0,l.useSearchParams)(),{data:d}=(0,i.j)(),u=(0,s.useRef)(!0),m=(0,n.useSetRecoilState)(r.q),[p,f]=(0,s.useState)(()=>{let t={...e};return c.forEach((a,s)=>{let l=e[s];Array.isArray(l)||"tags"===s?t[s]=a?a.split(","):[]:"boolean"==typeof l?t[s]="true"===a:isNaN(Number(a))||""===a?"true"===a?t[s]=!0:"false"===a?t[s]=!1:t[s]=a:t[s]=Number(a)}),t});(0,s.useEffect)(()=>{if(u.current&&d){let e={...p};"Visitor"!==d.role&&(e.nsfw=e.nsfw||d.nsfw_preference,e.gender=e.gender||(0,o.LF)(d.gender_preference||"Both"),e.style=e.style||(0,o.LF)(d.style_preference||"Both"),u.current=!1,h(e,!0),m(e))}},[d,p]),(0,s.useEffect)(()=>{u.current||m(p)},[p]);let h=(0,s.useCallback)(function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l="function"==typeof e?e(p):e;if(s){f(l);return}let i=new URLSearchParams;Object.entries(l).forEach(e=>{let[t,a]=e;null==a||""===a||(Array.isArray(a)&&a.length>0?i.set(t,a.join(",")):Array.isArray(a)||i.set(t,String(a)))}),t.replace("".concat(a,"?").concat(i.toString())),f(l)},[p,a,t]);return{filter:p,setFilter:h}}},53130:function(e,t,a){"use strict";a.d(t,{I:function(){return n}});var s=a(22351),l=a(25524),i=a(88726);localStorage.getItem("access_token")||localStorage.getItem("visitor_token");let r=["banned","free_user_last_date","frequency","ip_address","img_blurred","heart_balance","daily_msg","daily_img","daily_talktime","monthly_talktime","role","stripe_customer_id","subscription_end","subscription_start","subscription_status","subscription_tier","subscription_cancelled","unlimited_access","admin"],n=()=>{let e=async e=>{let{updates:t,dontToast:a}=e,l=Object.fromEntries(Object.entries(t).filter(e=>{let[t]=e;return!r.includes(t)})),n=localStorage.getItem("access_token")||localStorage.getItem("visitor_token"),o=s.E.getQueryData(["user",n]);if(o){o&&s.E.setQueryData(["user",n],e=>({...e,...l}));try{let e=await s.j.patch("/user",l);return 200===e.status&&s.E.invalidateQueries({queryKey:["user",n]}),e}catch(e){o&&s.E.setQueryData(["user",n],o),a||i.default.error("Failed to update the User!")}}};return(0,l.useMutation)({mutationFn:async t=>{let{updates:a,dontToast:s}=t;return await e({updates:a,dontToast:s})}})}},22468:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(78030).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},87592:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(78030).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7449:function(e,t){"use strict";function a(){return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16427:function(e,t,a){"use strict";a.d(t,{A:function(){return s}});let s=(0,a(99441).atom)({key:"maxTrialIdAtom",default:void 0})},98280:function(e,t,a){"use strict";a.d(t,{C0:function(){return u},Hf:function(){return m},Y3:function(){return p},gn:function(){return r},hE:function(){return n},ho:function(){return f},j_:function(){return c},jo:function(){return i},l0:function(){return d},mg:function(){return o},n8:function(){return l},nY:function(){return h}});var s=a(99441);let l=(0,s.atom)({key:"looksDataAtom",default:{}}),i=(0,s.atom)({key:"personalityDataAtom",default:{}}),r=(0,s.atom)({key:"lifestyleDataAtom",default:{}}),n=(0,s.atom)({key:"reviewIconsDataAtom",default:{}}),o={style:"",age:null,personality:"",looks:{face_swap_consent:!1,face_swap:""},gender:"",work:[],hobbies:[],relationships:"",voice:"",voice_embedding:"",kinks:"",voice_consent:!1,voice_url:"",first_name:"",last_name:"",about_me:""},c=(0,s.atom)({key:"CharacterDataAtom",default:o}),d={ethnicities:!1,eye_colors:!1,hair_styles:!1,hair_colors:!1,body_types:!1,breast_sizes:!1,butt_sizes:!1,personality:!1,relationships:!1,work:!1,hobbies:!1,kinks:!1},u=(0,s.atom)({key:"customizedFieldsAtom",default:d}),m={1:!1,2:!1,3:!1,4:!1,5:!1,6:!1,7:!1,8:!1},p=(0,s.atom)({key:"completedAtom",default:m}),f=(0,s.atom)({key:"editAtom",default:m}),h=(0,s.atom)({key:"pageAtom",default:1})},50189:function(e,t,a){"use strict";a.d(t,{P:function(){return l},X:function(){return i}});var s=a(99441);let l=(0,s.atom)({key:"liveCharId",default:""}),i=(0,s.atom)({key:"enableBanner",default:!1})},31231:function(e,t,a){"use strict";a.d(t,{LF:function(){return s},Wy:function(){return l},hE:function(){return i}});let s=e=>(null==e?void 0:e.charAt(0))===(null==e?void 0:e.charAt(0).toUpperCase())?e:null==e?void 0:e.replace(/\w\S*/g,e=>e[0].toUpperCase()+e.slice(1).toLowerCase()),l=e=>null==e?void 0:e.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/^./,e=>e.toUpperCase()),i=e=>e.replace(/([a-z])([A-Z])/g,"$1 $2")},9646:function(e,t,a){"use strict";a.d(t,{bU:function(){return j},fC:function(){return C}});var s=a(2265),l=a(78149),i=a(1584),r=a(98324),n=a(91715),o=a(47250),c=a(75238),d=a(25171),u=a(57437),m="Switch",[p,f]=(0,r.b)(m),[h,x]=p(m),g=s.forwardRef((e,t)=>{let{__scopeSwitch:a,name:r,checked:o,defaultChecked:c,required:m,disabled:p,value:f="on",onCheckedChange:x,...g}=e,[y,v]=s.useState(null),C=(0,i.e)(t,e=>v(e)),j=s.useRef(!1),_=!y||!!y.closest("form"),[N=!1,k]=(0,n.T)({prop:o,defaultProp:c,onChange:x});return(0,u.jsxs)(h,{scope:a,checked:N,disabled:p,children:[(0,u.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":N,"aria-required":m,"data-state":w(N),"data-disabled":p?"":void 0,disabled:p,value:f,...g,ref:C,onClick:(0,l.M)(e.onClick,e=>{k(e=>!e),_&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})}),_&&(0,u.jsx)(b,{control:y,bubbles:!j.current,name:r,value:f,checked:N,required:m,disabled:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var y="SwitchThumb",v=s.forwardRef((e,t)=>{let{__scopeSwitch:a,...s}=e,l=x(y,a);return(0,u.jsx)(d.WV.span,{"data-state":w(l.checked),"data-disabled":l.disabled?"":void 0,...s,ref:t})});v.displayName=y;var b=e=>{let{control:t,checked:a,bubbles:l=!0,...i}=e,r=s.useRef(null),n=(0,o.D)(a),d=(0,c.t)(t);return s.useEffect(()=>{let e=r.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(n!==a&&t){let s=new Event("click",{bubbles:l});t.call(e,a),e.dispatchEvent(s)}},[n,a,l]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:r,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return e?"checked":"unchecked"}var C=g,j=v}},function(e){e.O(0,[691,4898,4868,8472,8726,6015,9058,6648,3850,5901,3892,5287,7071,231,8972,8608,694,5752,2971,7023,1744],function(){return e(e.s=81062)}),_N_E=e.O()}]);