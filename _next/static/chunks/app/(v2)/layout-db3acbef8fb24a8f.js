(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1049,8649],{48938:function(e,t,s){Promise.resolve().then(s.bind(s,83550))},83550:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return si}});var a=s(57437),l=s(70930),i=s.n(l);s(56252);var n=s(88726),r=s(66648),o=s(16463),c=s(2265),d=s(99441),u=s(85499),m=s(14738),x=s(66511),h=s(67374),p=s(25524),f=s(59181),g=s(87497);let v=e=>{if(0===e.length)return null;let t=Math.floor(Math.random()*e.length);return e[t]},b=()=>{let{data:e}=(0,f.U)(),t=(0,g.L)(),s=async s=>{if(localStorage.getItem("pref")&&e){var a,l;let i=(localStorage.getItem("pref")||"").split("_"),n=[...e];("Male"===i[0]||"Female"===i[0])&&(n=n.filter(e=>e.gender===i[0])),((null===(a=i[1])||void 0===a?void 0:a.toLowerCase())==="realistic"||(null===(l=i[1])||void 0===l?void 0:l.toLowerCase())==="anime")&&(n=n.filter(e=>{var t,s;return(null===(t=e.style)||void 0===t?void 0:t.toLowerCase())===(null===(s=i[1])||void 0===s?void 0:s.toLowerCase())}));let r=v(n),o=null==r?void 0:r._id;o&&t({charId:o,redirectTo:s,isPublicChar:!0})}};return(0,p.useMutation)({mutationFn:async e=>{let{redirectTo:t}=e;await s(t)}})};var j=e=>{let{icon:t,text:s,onMobile:l,redirectText:i,href:n}=e,r=(0,o.useRouter)(),p=(0,o.usePathname)(),f=null==p?void 0:p.split("/")[1],[g,v]=(0,c.useState)(""),{data:j}=(0,m.j)(),y=(0,d.useSetRecoilState)(u.dG),w=(0,d.useSetRecoilState)(u.PC),_=(0,d.useRecoilValue)(x.loggedIn),N=(0,h.a)(),{mutateAsync:C}=b(),S=()=>{var e,t,a,l,n,o,c,d;let u="";switch(localStorage.getItem("sd_active_char_id")&&(u=localStorage.getItem("sd_active_char_id")||""),i||s){case"Homepage":r.push("/"),localStorage.removeItem("character");break;case"Chat":if(u&&(null==j?void 0:null===(e=j.conv_history)||void 0===e?void 0:e.some(e=>e.char_id===u)))localStorage.removeItem("character"),r.push("/chat");else if((null==j?void 0:j.conv_history)&&(null==j?void 0:null===(t=j.conv_history)||void 0===t?void 0:t.length)>0){let e=null==j?void 0:null===(a=j.conv_history)||void 0===a?void 0:a.length;localStorage.removeItem("character"),localStorage.setItem("sd_active_char_id",j.conv_history&&(null==j?void 0:null===(l=j.conv_history[e-1])||void 0===l?void 0:l.char_id)),r.push("/chat")}localStorage.removeItem("character");break;case"Generate Images":if(u&&(null==j?void 0:null===(n=j.conv_history)||void 0===n?void 0:n.some(e=>e.char_id===u)))localStorage.removeItem("character"),r.push("/generator/ai-images?image-option=Action");else if((null==j?void 0:j.conv_history)&&(null==j?void 0:null===(o=j.conv_history)||void 0===o?void 0:o.length)>0){localStorage.removeItem("character");let e=null==j?void 0:null===(c=j.conv_history)||void 0===c?void 0:c.length;localStorage.setItem("sd_active_char_id",j.conv_history&&(null==j?void 0:null===(d=j.conv_history[e-1])||void 0===d?void 0:d.char_id)),r.push("/generator/ai-images?image-option=Action")}localStorage.removeItem("character");break;case"My Characters":r.push("/characters");break;case"Guide":case"Guide":localStorage.removeItem("character"),r.push("/guide");break;case"Create Character":localStorage.removeItem("character"),r.push("/create");break;case"My Subscription":case"Subscribe":localStorage.removeItem("character"),r.push("/subscription");break;case"Settings":localStorage.removeItem("character"),r.push("/settings");break;case"Logout":N();break;case"Register/Login":w({open:!0,isSignup:!1,text:"Login",image:""});break;default:localStorage.removeItem("character")}};return(0,c.useEffect)(()=>{switch(f){case"":v("Homepage");break;case"chat":v("Chat");break;case"generator":v("Generate Images");break;case"characters":v("My Characters");break;case"guide":v("Guide");break;case"create":v("Create Character");break;case"subscription":v("My Subscription");break;case"settings":v("Settings");break;default:v("null")}},[f]),(0,a.jsxs)("button",{"aria-label":s,onClick:e=>{var t;if("Chat"!==s&&"Generate Images"!==s||(null==j?void 0:j.conv_history)&&(null==j?void 0:null===(t=j.conv_history)||void 0===t?void 0:t.length)!==0){if("Subscribe"===s&&!_){e.preventDefault(),w({open:!0,isSignup:!0,text:"Sign Up to See Plans",image:""});return}}else e.preventDefault(),"Chat"===s&&C({redirectTo:"chat"}),"Generate Images"===s&&C({redirectTo:"generator"});if(n){r.push(n);return}S(),l&&y(!1)},className:"text-white flex h-[60px] items-center border-none lg:border-solid lg:border-y-4 border-transparent gap-1 px-2 xl:px-3 2xl:px-5 -m-2.5 transition-all sm:hover:text-primary hover:border-b-4 hover:border-b-primary\n      ".concat(l?"border-none! gap-3!":"","\n      ").concat(g===s||g===i?"sm:!text-primary border-b-4 border-b-primary":"","\n      ").concat("Subscribe"===s||"My Subscription"===s?"hover:text-[#ffae00]! hover:border-b-[#ffae00]!":"","\n      "),children:[t,s]})},y=s(3114),w=s(5207),_=s(7432),N=s(87138),C=s(36013),S=s(50495),k=s(76351),I=s(22351);let E=async()=>(await I.j.get("/check/heart-costs")).data,R=()=>(0,k.useQuery)({queryKey:["heartCosts"],queryFn:()=>E()});var A=e=>{let{onMobile:t}=e,s=(0,y.P)(),l=(0,o.useRouter)(),{closeModal:i}=(0,_.d)(),n=(0,d.useSetRecoilState)(u.dG),[x,h]=(0,c.useState)(!1),{data:p}=(0,m.j)(),{data:f}=R(),[g,v]=(0,c.useState)(!1),[b,j]=(0,c.useState)(!1),[w,N]=(0,c.useState)(!1),k=e=>{e.stopPropagation(),v(!g)};return(0,a.jsxs)(C.Zb,{className:"xl:pt-0 pt-4 xl:bg-card bg-background shadow-none rounded-none  ",children:[(0,a.jsx)(C.Ol,{onClick:k,className:"space-x-3 space-y-0 p-0 ",onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:(0,a.jsxs)("button",{"aria-label":"Show Heart Rate for all type of responses",className:"w-full flex justify-center items-center  gap-3  ",children:[(0,a.jsx)(r.default,{width:24,height:24,src:"/icons/Heart_Vector_2.svg",alt:"Heart icon",unoptimized:!0}),(0,a.jsxs)("span",{className:"w-full flex  hover:text-primary transition-color justify-between",style:{transition:"color .2s",color:x?"#d62a5e":"#eaeaea"},children:[(0,a.jsxs)("span",{className:"",children:["Hearts:"," ",(null==p?void 0:p.heart_balance)===0||(null==p?void 0:p.heart_balance)===void 0?"0":Math.max(0,Math.round((null==p?void 0:p.heart_balance)*100)/100)]}),(0,a.jsx)("span",{className:"scale-75 text-primary",children:g?s.minus:s.plus})]})]})}),(0,a.jsx)(C.aY,{className:"flex justify-center items-center  mt-2  max-w-full transition-all duration-300 ease-in-out  ".concat(g?"max-h-[900px] opacity-100 cursor-default":"max-h-0 opacity-0 cursor-pointer"),onMouseEnter:()=>!g&&h(!0),onMouseLeave:()=>!g&&h(!1),onClick:k,children:(0,a.jsxs)("div",{className:"max-w-full p-3 space-y-0.5 bg-background xl:rounded-lg text-wrap ",children:[(0,a.jsxs)("div",{className:"flex flex-col w-full",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-2 xl:pe-[5px] pe-[2px] cursor-pointer",onClick:()=>j(!b),children:[(0,a.jsx)("span",{className:"text-[16px] xl:text-sm leading-5 xl:leading-[18px]",children:"Cutting-Edge Chat Engines"}),(0,a.jsx)("span",{className:"scale-75 text-primary",children:b?s.minus:s.plus})]}),(0,a.jsx)("div",{className:"space-y-1 ps-4 transition-all duration-300 ease-in-out overflow-hidden ".concat(b?"max-h-[500px] opacity-100 mt-2":"max-h-0 opacity-0"),children:null==f?void 0:f.chat_engines.map(e=>(0,a.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,a.jsx)("span",{className:"text-[14px] xl:text-sm",children:e.name}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base font-semibold",children:e.cost}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",width:15,height:15,alt:"Heart icon",unoptimized:!0})]})]},e.name))})]}),(0,a.jsxs)("div",{className:"flex flex-col w-full mt-2",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-2 xl:pe-[5px] pe-[2px] cursor-pointer",onClick:()=>N(!w),children:[(0,a.jsx)("span",{className:"text-[16px] xl:text-sm leading-5 xl:leading-[18px]",children:"Image Generation Engines"}),(0,a.jsx)("span",{className:"scale-75 text-primary",children:w?s.minus:s.plus})]}),(0,a.jsx)("div",{className:"space-y-1 ps-4 transition-all duration-300 ease-in-out overflow-hidden ".concat(w?"max-h-[500px] opacity-100 mt-2":"max-h-0 opacity-0"),children:null==f?void 0:f.image_engines.map(e=>(0,a.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,a.jsx)("span",{className:"text-[14px] xl:text-sm",children:e.name}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base font-semibold",children:e.cost}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",width:15,height:15,alt:"Heart icon",unoptimized:!0})]})]},e.name))})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-2 pb-1",children:[(0,a.jsx)("span",{className:"text-[16px] xl:text-sm",children:"1 Minute Voice"}),(0,a.jsxs)("span",{className:"flex justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base font-semibold",children:"2"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-2 pb-1",children:[(0,a.jsx)("span",{className:"text-[16px] xl:text-sm",children:"1 Minute Call"}),(0,a.jsxs)("span",{className:"flex justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base font-semibold",children:"2"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-[16px] xl:text-sm",children:"5 Seconds Live Image"}),(0,a.jsxs)("span",{className:"flex justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base font-semibold",children:"8"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsx)(S.z,{"aria-label":"Go to subscription page to purchase hearts",variant:"default",className:"w-full h-8 text-sm ",size:"default",onClick:()=>(l.push("/subscription"),t&&n(!1)),children:"Purchase Hearts"})]})})]})},P=()=>{let e=(0,y.P)(),t=(0,h.a)(),{data:s}=(0,m.j)(),{closeModal:l}=(0,_.d)(),i=(null==s?void 0:s.subscription_tier)!==""&&(null==s?void 0:s.subscription_tier)!=="Free"&&(null==s?void 0:s.subscription_tier)!=="Visitor";return(0,a.jsxs)("div",{className:"absolute dontCloseModal hidden xl:block w-64 top-14 font-bold -right-2 z-40 bg-card py-4 px-6 rounded-lg",children:[(null==s?void 0:s.subscription_tier)!==""&&(null==s?void 0:s.subscription_tier)!=="Free"&&(null==s?void 0:s.subscription_tier)!=="Visitor"&&(0,a.jsx)(A,{}),(0,a.jsxs)(N.default,{"aria-label":"Guide",href:"/guide",onClick:()=>l("MyProfileModal"),className:"xl3:hidden w-full flex hover:text-primary transition-colors space-x-3 py-2",children:[(0,a.jsx)("span",{className:"h-6 w-6",children:e.book}),(0,a.jsx)("span",{children:"Guide"})]}),(0,a.jsxs)(N.default,{"aria-label":"Guide",href:"/subscription",onClick:()=>l("MyProfileModal"),className:"2xl:hidden w-full flex hover:text-primary transition-colors space-x-3 py-2",children:[(0,a.jsx)("span",{className:"h-6 w-6",children:e.gift}),(0,a.jsx)("span",{children:i?"My Subscription":"Subscribe"})]}),(0,a.jsxs)(N.default,{"aria-label":"Settings",href:"/settings",onClick:()=>l("MyProfileModal"),className:"w-full flex hover:text-primary transition-colors space-x-3 py-2",children:[(0,a.jsx)("span",{className:"h-6 w-6",children:e.setting}),(0,a.jsx)("span",{children:"Settings"})]}),(0,a.jsxs)("button",{"aria-label":"Logout",onClick:()=>{t(),l("MyProfileModal")},className:"w-full flex hover:text-primary transition-colors space-x-3 py-2",children:[(0,a.jsx)("span",{className:"h-6 w-6",children:e.logout}),(0,a.jsx)("span",{children:"Logout"})]})]})},M=s(98130),z=s(6600),L=s(74697),T=s(63858);let D=(0,d.atom)({key:"recentNotificationAtom",default:null}),Z=(0,d.atom)({key:"notificationTempCount",default:0});var F=s(44458),G=s(87592),U=s(41472);let H=()=>{let e=async e=>{let{ids:t,dontToast:s}=e,a=localStorage.getItem("access_token")||localStorage.getItem("visitor_token");if(a)try{let e=await I.j.post("/user/notification_seen",{notificationId:t});return I.E.invalidateQueries({queryKey:["user",a]}),e}catch(e){s||n.default.error("Failed to mark notification as seen!")}};return(0,p.useMutation)({mutationFn:async t=>{let{ids:s,dontToast:a}=t;return await e({ids:s,dontToast:a})}})};var O=s(50189),V=e=>{var t;let{notification:s}=e,l=(0,d.useSetRecoilState)(u.$L),i=(0,d.useSetRecoilState)(O.P),n=(0,o.useRouter)(),{handleUpdateCharCount:c}=(0,U.D)(),{mutateAsync:m}=H();async function x(e){l({open:!0,_id:e,...s}),await m({ids:["system_".concat(e)]})}let h=async()=>{s.redirectUrl?(c({charId:s.charId,convId:s.convId}),s.charId&&localStorage.setItem("sd_active_char_id",s.charId),i(s.charId),n.push(s.redirectUrl)):await x(s._id)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full px-2",onClick:h,children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)(F.qE,{className:"min-w-10 min-h-10",children:[(0,a.jsx)(F.F$,{src:s.img,alt:"avatar",className:" border-primary sm:w-10 sm:h-10 w-14 h-14 object-cover object-top rounded-full"}),s.convId&&(0,a.jsx)("div",{className:"absolute bottom-0 right-0",children:(0,a.jsx)(r.default,{className:"ml-10",src:"missed text"===s.type?"/icons/messageBadge.svg":"/icons/callBadge.svg",alt:"badge",width:T.tq?20:12,height:T.tq?20:12})}),(0,a.jsx)(F.Q5,{className:"flex items-center justify-center bg-background rounded-full text-xl sm:w-10 sm:h-10 w-14 h-14",children:null===(t=s.sender)||void 0===t?void 0:t.charAt(0)})]})}),(0,a.jsxs)("div",{className:"",children:[(0,a.jsxs)("div",{className:"flex px-2 items-center w-[80vw] sm:w-[300px] justify-between custom-notifaction-style",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"sm:text-md text-sm font-semibold",children:s.sender}),(0,a.jsxs)("p",{className:"text-[11px] text-[#E5E4E2]",children:["( ",s.type," )"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)("p",{className:"text-[11px]",children:s.date}),(0,a.jsx)(G.Z,{width:12,height:12})]})]}),(0,a.jsx)("p",{className:"text-[11px] px-2 text-[#E5E4E2] line-clamp-1 w-60 ".concat("system"===s.type?"font-semibold":""),children:s.description})]})]})})},q=s(45018);let B=()=>{let{data:e}=(0,m.j)(),t=null==e?void 0:e._id,s=async()=>{try{var e;let t=await I.j.get("/system_notifications"),s=null==t?void 0:null===(e=t.data)||void 0===e?void 0:e.notifications;if(s)return s;return[]}catch(e){return console.error("Error fetching notifications:",e),null}};return(0,k.useQuery)({queryKey:["system-notifications",{userId:t}],queryFn:s,enabled:!!t,refetchOnWindowFocus:!1,staleTime:36e5})};var Q=function(e){let{clearAllNotifications:t,notifications:s,setMobileDropdown:l,mobileDropdown:i}=e;return(0,a.jsx)("div",{className:"fixed overflow-y-scroll inset-0 w-screen sidebar-background h-screen z-50 ",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex p-4 justify-between items-center bg-black text-white shrink-0",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:"Notification Center"}),(0,a.jsx)("p",{className:"text-sm font-semibold",onClick:t,children:"Clear all"}),(0,a.jsx)("span",{onClick:()=>l(!1),className:"cursor-pointer",children:(0,a.jsx)(L.Z,{width:20,height:20})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-scroll",children:null==s?void 0:s.map(e=>(0,a.jsx)("div",{className:"border-b border-primary last:border-b-0 py-3",children:(0,a.jsx)(V,{notification:e})},e._id))})]})})},K=()=>{let e=(0,d.useSetRecoilState)(D),{data:t}=B(),{data:s}=(0,m.j)(),l=(0,d.useSetRecoilState)(Z),i=(0,d.useRecoilValue)(Z),{mutateAsync:n}=H(),[r,o]=(0,c.useState)(!1),{data:u,isLoading:x}=(0,q.v)({}),[h,p]=(0,c.useState)(!1),[f,g]=(0,c.useState)(!1),[v,b]=(0,c.useState)([]),[j,y]=(0,c.useState)(0),w=(0,c.useRef)(null);function _(e){let t=e?new Date(e):new Date,s=new Date;if(t.toDateString()===s.toDateString())return t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});let a=new Date(s.getTime()-864e5);return t.toDateString()===a.toDateString()?"Yesterday":t.toLocaleDateString([],{day:"2-digit",month:"short"})}async function N(){e(null),o(!0),setTimeout(async()=>{e(null);let t=v.map(e=>e._id);await n({ids:t}),o(!1),p(!1),g(!1)},300)}(0,c.useEffect)(()=>{if(t&&s){var a,n;let r=[],o=[];if(u){let e=null==u?void 0:null===(a=u.msg)||void 0===a?void 0:a.filter(e=>e.newTextCount&&e.newTextCount>0);(null==e?void 0:e.length)>0&&(r=e.map(e=>({_id:new Date(e.convUpdated).toISOString(),sender:e.first_name+" "+e.last_name,description:e.last_message,date:_(e.convUpdated),type:"missed text",img:e.initial_image,redirectUrl:"/chat",datetime:new Date(e.convUpdated).toISOString(),charId:e._id,convId:e.conv_id})))}t&&(null==t?void 0:t.length)>0&&(o=null==t?void 0:t.map(e=>({_id:new Date(e.created_date).toISOString(),sender:"Secret Desires",description:e.title,date:_(e.created_date),type:"system",img:"/icons/mobile-logo.svg",redirectUrl:"",datetime:new Date(e.created_date).toISOString(),charId:"",convId:"",content:e.content})));let c=[...r,...o].filter(e=>{var t;return!(null==s?void 0:null===(t=s.seen_notifications)||void 0===t?void 0:t.includes(e._id))});c.sort((e,t)=>new Date(t.datetime).getTime()-new Date(e.datetime).getTime()),b(c);let d=c.filter(e=>{var t;return null==s?void 0:null===(t=s.seen_notifications)||void 0===t?void 0:t.includes("system_".concat(e._id))});y(c.length-d.length),c.length>i&&("system"===c[0].type&&(null==s?void 0:null===(n=s.seen_notifications)||void 0===n?void 0:n.includes("system_".concat(c[0]._id)))||e(c[0]),l(c.length))}},[u,t]),(0,c.useEffect)(()=>{if(s&&v.length>0){let e=v.filter(e=>{var t;return!(null==s?void 0:null===(t=s.seen_notifications)||void 0===t?void 0:t.includes(e._id))}),t=e.filter(e=>{var t;return null==s?void 0:null===(t=s.seen_notifications)||void 0===t?void 0:t.includes("system_".concat(e._id))});y(e.length-t.length),b(e)}},[s]),(0,c.useEffect)(()=>{let t=setTimeout(()=>{e(null)},5e3);return()=>clearTimeout(t)},[v]);let C=()=>{p(!h)};return(0,c.useEffect)(()=>{function e(e){"Escape"===e.key&&h&&C()}return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[h]),(0,c.useEffect)(()=>{let e=e=>{w.current&&!w.current.contains(e.target)&&C()};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[h]),(0,a.jsx)("div",{className:"z-30",children:T.tq?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("span",{className:"relative scale-[.8] cursor-pointer",onClick:()=>g(!f),children:[(0,a.jsx)(z.Z,{fill:"#fff",stroke:"#fff",width:27,height:27}),j>0&&(0,a.jsx)("span",{className:"absolute bottom-3 right-3 bg-red-500 text-white text-[11px] font-bold rounded-full w-4 h-4 flex items-center justify-center",children:j})]}),f&&(0,a.jsx)(Q,{clearAllNotifications:N,notifications:v,setMobileDropdown:g,mobileDropdown:f})]}):(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"relative inline-block text-left",children:[(0,a.jsx)("div",{className:"group cursor-pointer",children:(0,a.jsxs)("span",{className:"relative scale-[.8] cursor-pointer",onClick:C,children:[(0,a.jsx)(z.Z,{className:"w-[27px] h-[27px] fill-white stroke-white group-hover:fill-primary group-hover:stroke-primary"}),j>0&&(0,a.jsx)("span",{className:"absolute bottom-3 right-3 bg-red-500 text-white text-[11px] font-bold rounded-full w-4 h-4 flex items-center justify-center",children:j})]})}),(0,a.jsx)("div",{style:{borderTopLeftRadius:"10px",borderTopRightRadius:"0px",borderBottomRightRadius:"10px",borderBottomLeftRadius:"10px"},ref:h?w:void 0,className:"".concat(h?"block":"hidden"," absolute right-0 mt-2 min-h-[100px] origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none max-h-[400px] overflow-y-scroll sidebar-background overflow-x-hidden custom-scrollbar sm:w-[400px] w-60 mt-1  bg-background border border-primary z-30"),role:"menu","aria-orientation":"vertical","aria-labelledby":"menu-button",children:(0,a.jsxs)("div",{className:"py-1",role:"none",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between pt-3 py-2 px-3",children:[(0,a.jsx)("p",{className:"text-md font-semibold",children:"Notification Center"}),(0,a.jsx)("p",{className:"text-sm cursor-pointer underline font-semibold hover:text-primary",onClick:()=>{N()},children:"Clear All"}),(0,a.jsx)(L.Z,{className:"cursor-pointer hover:text-primary",width:18,height:18,onClick:C})]}),(0,a.jsx)("div",{className:"flex flex-col",children:v.map(e=>(0,a.jsx)("li",{value:e.sender,className:"transition-colors duration-500 ease-in-out hover:bg-primary/60 border-b border-primary last:border-b-0 focus:outline-none hover:outline-none flex flex-row items-center justify-between z-30 py-6 h-14  ".concat(r?"translate-x-full opacity-0":"translate-x-0 opacity-100"),children:(0,a.jsx)("span",{className:"relative z-10",children:(0,a.jsx)(V,{notification:e})})},e._id))})]})})]})})})},Y=s(90837),$=s(72228);function W(){let[e,t]=(0,d.useRecoilState)(u.PC),s={open:!1,isSignup:!1,text:"",image:"",gaClass:""};return(0,a.jsx)(Y.Vq,{open:e.open,onOpenChange:()=>t(e=>e.open?s:e),children:(0,a.jsx)(Y.cZ,{onOpenAutoFocus:e=>e.preventDefault(),className:"flex justify-center items-center p-0 rounded-lg overflow-x-hidden overflow-y-hidden max-w-80 md:max-w-3xl max-h-fit ".concat(e.gaClass?e.gaClass:""),id:e.isSignup?"ga-auth-popup":"ga-auth-login",children:(0,a.jsx)($.Z,{})})})}function X(){let{data:e}=(0,m.j)(),[t,s]=(0,d.useRecoilState)(u.Qg);return(0,d.useSetRecoilState)(u.Qg),(0,a.jsx)(Y.Vq,{open:t.open,onOpenChange:()=>s(e=>({...e,open:!e.open})),children:(0,a.jsx)(Y.cZ,{id:"ga-subscribe-popup",className:"p-0 max-w-80 md:max-w-lg min-w-56 rounded-lg overflow-hidden shadow-lg ".concat(t.gaClass?t.gaClass:""),children:(0,a.jsxs)("div",{className:"flex flex-wrap md:flex-nowrap",children:[(0,a.jsx)(r.default,{src:t.image||((null==e?void 0:e.gender_preference)==="Male"?"/images/male-signup.png":"/images/female-signup.png"),alt:"Character Image",width:300,height:300,className:"min-w-[40%] w-full h-60 md:h-auto object-cover object-top "}),(0,a.jsxs)("div",{className:"min-w-[60%] p-8 bg-background",children:[(0,a.jsx)("h2",{className:"text-2xl text-center",children:t.text}),"subscription"==t.type?(0,a.jsxs)("ul",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Generate more images"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Get more talk time"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Unlock new features"]})]}):(0,a.jsxs)("ul",{className:"mt-4 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Generate more images"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Access voice chatting"]}),(0,a.jsxs)("li",{className:"flex items-center",children:[(0,a.jsx)(J,{className:"w-5 h-5 mr-2 text-yellow-500"}),"Best chat engines"]})]}),(0,a.jsx)(N.default,{"aria-label":"See subscription plans",href:"/subscription",children:(0,a.jsx)(S.z,{"aria-label":"See subscription plans",onClick:()=>s({open:!1,text:"",image:"",type:"subscription"}),className:"mt-6 w-full bg-pink-500 text-white",children:"See Plans"})})]})]})})})}function J(e){return(0,a.jsxs)("svg",{...e,xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,a.jsx)("path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z"}),(0,a.jsx)("path",{d:"m9 12 2 2 4-4"})]})}var ee=s(41647),et=s(39940),es=s(38472),ea=s(13498),el=s(20708),ei=s(16880),en=s(84019),er=s(45340),eo=s(37762),ec=s(51299),ed=s(93146);let eu=e=>{let{id:t,activeCategory:s,onClick:l,extraClasses:i}=e,n=(0,y.P)();return(0,a.jsxs)(S.z,{"aria-label":"Customize ".concat(t," category"),variant:"link",size:"sm",className:"md:text-sm text-xs text-foreground ".concat(i),onClick:()=>l({btnName:t}),children:["".concat(s.includes(t)?"Options":"Customize")," ",(0,a.jsx)("span",{className:"ps-1 md:scale-[.85] scale-[.7]",children:n.edit})]})};var em=s(83102),ex=e=>{let{tempCharData:t,setTempCharData:s}=e;var l=(0,ei.Z)();try{let e=er.O,[l,i]=(0,c.useState)(void 0),[n,r]=(0,c.useState)(void 0),[o,d]=(0,c.useState)([]),[u,m]=(0,c.useState)({hair_style:!1,hair_color:!1,body_type:!1,breast_size:!1,butt_size:!1,eye_color:!1,ethnicity:!1});(0,c.useEffect)(()=>{if(!t||!e)return;let s=e.filter(e=>{var s,a;return(null===(s=e.gender)||void 0===s?void 0:s.toLowerCase())===(null===(a=t.gender)||void 0===a?void 0:a.toLowerCase())});s.length>0&&i(s)},[e,t.gender]),(0,c.useEffect)(()=>{if(n)for(let s of["hair_styles","hair_colors","body_types","breast_sizes","butt_sizes","eye_colors","ethnicities"]){var e;let a=s.slice(0,-1),l=null==n?void 0:null===(e=n[s])||void 0===e?void 0:e.some(e=>(null==e?void 0:e.display)===t[a]);!1===l&&m(e=>({...e,[a]:!0}))}},[n]),(0,c.useEffect)(()=>{var s;if(!t||!e||!l||0===l.length)return;let a=l.filter(e=>{var s,a;return(null===(s=e.style)||void 0===s?void 0:s.toLowerCase())===(null===(a=t.style)||void 0===a?void 0:a.trim().toLowerCase())});r(null==a?void 0:null===(s=a[0])||void 0===s?void 0:s.options)},[l,t.style]);let x=e=>{let{btnName:t}=e;o.includes(t)?d(o.filter(e=>e!==t)):d([...o,t])};return(0,a.jsxs)("div",{className:"flex flex-col gap-5  items-center   xl:mx-40 sm:mx-24 ms-2",children:[(0,a.jsx)(eo.Z,{heading:"Age"})," ",(0,a.jsx)("div",{className:"flex items-center justify-center space-x-4",children:(0,a.jsx)(ec.Z,{setData:s,defaultAge:t.age})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Ethnicity"}),(0,a.jsx)(eu,{extraClasses:"mt-3",id:"ethnicity_custom",activeCategory:o,onClick:x})]}),(0,a.jsxs)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center items-center flex-wrap 2xl:w-[700px] xl:w-[600px] sm:w-[550px] xs:w-72 w-[80vw] px-2",children:[(null==n?void 0:n.ethnicities)&&(null==n?void 0:n.ethnicities.map((e,l)=>{let i=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,ethnicity:e.display})),children:(0,a.jsx)(en.Z,{filepath:i,CategoryName:e.display,size:"".concat((t.style,"2xl:w-36 xl:w-32 lg:w-24 md:w-[88px] sm:w-[96px] w-[80px]")),selected:e.display===t.ethnicity})},l)})),(o.includes("ethnicity_custom")||!0===u.ethnicity)&&(0,a.jsx)(em.I,{value:t.ethnicity,onChange:e=>s(t=>({...t,ethnicity:e.target.value})),placeholder:"Enter ethnicity. e.g. Caucasian, Asian, Hispanic",className:"2xl:w-[700px] xl:w-[600px] sm:w-[550px] xs:w-72 w-[80vw]"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Eye Color"}),(0,a.jsx)(eu,{extraClasses:"mt-3",id:"eye_colors_custom",activeCategory:o,onClick:x})]}),(0,a.jsxs)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center items-center flex-wrap 2xl:w-[700px] xl:w-[600px] sm:w-[550px]  xs:w-72 w-[80vw] px-2\n				",children:[(null==n?void 0:n.eye_colors)&&(null==n?void 0:n.eye_colors.map(e=>{let l=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,eye_color:e.display})),children:(0,a.jsx)(en.Z,{filepath:l,CategoryName:e.display,size:"".concat("Realistic"==t.style?"2xl:w-32 xl:w-[110px] lg:w-[84px] sLaptop:w-[88px] md:w-[74px] sm:w-[96px] xs:w-[70px] w-[70px]":"2xl:w-32 xl:w-[108px] lg:w-[90px] sLaptop:w-[94px] md:w-20 sm:w-[96px] xs:w-[70px] w-[74px]"),selected:e.display===t.eye_color})},e.display)})),(o.includes("eye_colors_custom")||!0===u.eye_color)&&(0,a.jsx)(em.I,{value:t.eye_color,onChange:e=>s(t=>({...t,eye_color:e.target.value})),placeholder:"Enter Eye Color only. e.g. Red"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Hair Style"})," ",(0,a.jsx)(eu,{extraClasses:"mt-3",id:"hair_style_custom",activeCategory:o,onClick:x})]}),(0,a.jsxs)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center flex-wrap  ".concat((null==t?void 0:t.gender)=="Male"?"Realistic"==t.style?"2xl:w-[700px] xl:w-[700px] lg:w-[600px] sLaptop:w-[500px] md:w-[450px] xs:w-96":"2xl:w-[700px] xl:w-[600px] lg:w-[550px] sLaptop:w-[550px] md:w-[500px]  sm:w-96 xs:w-[350px]":"2xl:w-[700px] xl:w-[600px] lg:w-[550px] sLaptop:w-[550px] md:w-[500px] sm:w-full xs:w-96 ","   px-2\n				"),children:[(null==n?void 0:n.hair_styles)&&(null==n?void 0:n.hair_styles.map(e=>{let l=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,hair_style:e.display})),children:(0,a.jsx)(en.Z,{filepath:l,CategoryName:e.display,size:"".concat("Realistic"==t.style?"2xl:w-32 xl:w-[110px] lg:w-[84px] sLaptop:w-[88px] md:w-[74px] sm:w-[96px] xs:w-[60px] w-[70px]":"2xl:w-32 xl:w-[108px] lg:w-[90px] sLaptop:w-[94px] md:w-20 sm:w-[95px] xs:w-[78px] w-[74px]"),selected:e.display===t.hair_style})},e.display)})),(o.includes("hair_style_custom")||!0===u.hair_style)&&(0,a.jsx)(em.I,{value:t.hair_style,onChange:e=>s(t=>({...t,hair_style:e.target.value})),placeholder:"Enter Hair Style only. e.g. Mohawk"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Hair Color"})," ",(0,a.jsx)(eu,{extraClasses:"mt-3",id:"hair_color_custom",activeCategory:o,onClick:x})]}),(0,a.jsx)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center items-center flex-wrap sLaptop:w-full md:w-full sm:w-[550px] xs:w-[350px] w-full px-2\n						",children:(null==n?void 0:n.hair_colors)&&(null==n?void 0:n.hair_colors.map((e,l)=>{let i=e.filepath.replace("public","");return t.hair_color,e.display,(0,a.jsx)("div",{onClick:()=>s(t=>({...t,hair_color:e.display})),children:(0,a.jsx)(en.Z,{filepath:i,CategoryName:e.display,size:"2xl:w-32 xl:w-[110px] lg:w-[84px] sLaptop:w-[88px] md:w-[74px] sm:w-[96px] xs:w-[78px] w-[74px]",selected:e.display===t.hair_color})},l)}))}),(o.includes("hair_color_custom")||!0===u.hair_color)&&(0,a.jsx)(em.I,{value:t.hair_color,onChange:e=>s(t=>({...t,hair_color:e.target.value})),placeholder:"Enter Hair Color only. e.g. Red with Grey Lines",className:"2xl:w-[700px] xl:w-[600px] sm:w-[550px]  xs:w-72 w-[80vw]"}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Body Type"})," ",(0,a.jsx)(eu,{extraClasses:"mt-3",id:"body_type_custom",activeCategory:o,onClick:x})]}),(0,a.jsx)("div",{className:"\n							flex flex-row sm:gap-2.5 gap-2 pb-3  justify-center items-center flex-wrap  xs:w-full w-[60vw] px-2",children:(null==n?void 0:n.body_types)&&(null==n?void 0:n.body_types.map(e=>{let l=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,body_type:e.display})),children:(0,a.jsx)(en.Z,{filepath:l,CategoryName:e.display,size:"2xl:w-36 xl:w-32 lg:w-24 md:w-[88px]  sm:w-[96px] w-[80px] ",selected:e.display===t.body_type})},e.display)}))}),(o.includes("body_type_custom")||!0===u.body_type)&&(0,a.jsx)(em.I,{value:t.body_type,onChange:e=>s(t=>({...t,body_type:e.target.value})),placeholder:"Enter body type. e.g. 6ft tall, large hips",className:"2xl:w-[700px] xl:w-[600px] sm:w-[550px]  xs:w-72 w-[80vw]"}),(null==n?void 0:n.breast_sizes)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Breast Size"})," ",(0,a.jsx)(eu,{extraClasses:"mt-3",id:"breast_size_custom",activeCategory:o,onClick:x})]}),(0,a.jsx)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center items-center flex-wrap sLaptop:w-full md:w-full sm:w-[550px] xs:w-72 w-[80vw] px-2\n				}",children:null==n?void 0:n.breast_sizes.map((e,l)=>{let i=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,breast_size:e.display})),children:(0,a.jsx)(en.Z,{filepath:i,CategoryName:e.display,size:"2xl:w-32 xl:w-[110px] lg:w-[84px] sLaptop:w-[88px] md:w-[74px] sm:w-[96px] xs:w-[70px] w-[74px]\n											}",selected:e.display===t.breast_size})},l)})}),(o.includes("breast_size_custom")||!0===u.breast_size)&&(0,a.jsx)(em.I,{value:t.breast_size,onChange:e=>s(t=>({...t,breast_size:e.target.value})),placeholder:"Enter breast size and details",className:"2xl:w-[700px] xl:w-[600px] sm:w-[550px]  xs:w-72 w-[80vw]"})]}),(null==n?void 0:n.butt_sizes)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(eo.Z,{heading:"Edit Butt Size"})," ",(0,a.jsx)(eu,{extraClasses:"mt-3",id:"butt_size_custom",activeCategory:o,onClick:x})]}),(0,a.jsx)("div",{className:"flex flex-row sm:gap-2.5 gap-2 pb-3 justify-center items-center flex-wrap sLaptop:w-full md:w-full sm:w-[550px] xs:w-72 w-[80vw] px-2\n						}",children:null==n?void 0:n.butt_sizes.map((e,l)=>{let i=e.filepath.replace("public","");return(0,a.jsx)("div",{onClick:()=>s(t=>({...t,butt_size:e.display})),children:(0,a.jsx)(en.Z,{filepath:i,CategoryName:e.display,size:"\n													2xl:w-32 xl:w-[110px] lg:w-[84px] sLaptop:w-[88px] md:w-[74px] sm:w-[96px] xs:w-[70px] w-[74px]\n											}",selected:e.display===t.butt_size})},l)})}),(o.includes("butt_size_custom")||!0===u.butt_size)&&(0,a.jsx)(em.I,{value:t.butt_size,onChange:e=>s(t=>({...t,butt_size:e.target.value})),placeholder:"Enter Butt size and details",className:"2xl:w-[700px] xl:w-[600px] sm:w-[550px]  xs:w-72 w-[80vw]"})]})]})}finally{l.f()}},eh=s(21220),ep=s(67135),ef=s(98280),eg=s(72224),ev=e=>{let{dropdownData:t,defaultValue:s,setTempCharData:l,trait:i,isArray:n}=e,o=e=>{"hobbies"===i?l(t=>{let s=t.hobbies||[],a=s.includes(e)?s.filter(t=>t!==e):[...s,e];return{...t,[i]:a}}):"work"===i?l(t=>{let s=[i];return s[0]=e,{...t,[i]:s}}):l(t=>({...t,[i]:e}))},c=e=>{"hobbies"===i&&l(t=>({...t,hobbies:t.hobbies.filter(t=>t!==e)}))};return(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)(eh.h_,{children:[(0,a.jsx)(eh.$F,{asChild:!0,id:"Choose ".concat(i),"aria-label":'"Choose '.concat(i,'"'),className:"sm:w-full xs:w-[370px] w-72 flex justify-center items-center mx-auto z-30",children:(0,a.jsxs)(S.z,{"aria-label":"Choose ".concat(i),variant:"outline",className:"font-normal justify-between hover:bg-background border-0 hover:text-primary-foreground focus:ring-1 focus:ring-primary focus:ring-offset-1       focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-1",children:[(()=>{if("hobbies"===i){if(!s)return"Select hobbies...";let e=s.split(",").filter(e=>e.trim()).length;return e>0?"".concat(e," hobbies selected"):"Select hobbies..."}return s||"Select an option...."})(),(0,a.jsx)(r.default,{src:"/icons/caret-down.svg",alt:"Down arrow",width:"16",height:"16"})]})}),(0,a.jsx)(eh.AW,{className:"sm:w-[380px] xs:w-[365px] w-[280px] my-1 cursor-pointer bg-background border border-primary h-72 overflow-y-auto custom-scrollbar z-50 rounded-lg",children:"hobbies"===i?(0,a.jsx)("div",{className:"py-1 border-0 rounded-none mx-1",children:t&&t.map(e=>(0,a.jsxs)(eh.bO,{checked:null==s?void 0:s.split(", ").includes(e.display),onCheckedChange:()=>o(e.display),className:"focus:bg-primary relative flex items-center z-50 h-8 px-4 hover:ring-0 hover:ring-none rounded-md hover:ring-offset-0 hover:outline-none",children:[(null==s?void 0:s.split(", ").includes(e.display))&&(0,a.jsx)(r.default,{src:"/icons/check.svg",alt:"Check icon",width:"16",height:"16",className:"absolute left-3"}),(0,a.jsx)("span",{className:"px-4 text-sm ".concat((null==s?void 0:s.split(", ").includes(e.display))?"font-semibold":""),children:e.display})]},e._id||e.id))}):(0,a.jsx)(eh._x,{defaultValue:s||"",onValueChange:e=>o(e),className:"py-1 border-0 rounded-none mx-1",children:t&&t.map(e=>(0,a.jsxs)(eh.qB,{value:e.display,className:"focus:bg-primary relative flex items-center z-50 h-8 px-4 hover:ring-0 hover:ring-none rounded-md hover:ring-offset-0 hover:outline-none",children:[s===e.display?(0,a.jsx)(r.default,{src:"/icons/check.svg",alt:"Check icon",width:"16",height:"16",className:"absolute left-3"}):"",(0,a.jsx)("span",{className:"".concat(s===e.display?"font-semibold":""," px-4 text-sm"),children:e.display})]},e._id||e.id))})})]}),"hobbies"===i&&s&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 px-1",children:s.split(", ").filter(e=>e.trim()).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 text-sm rounded-full bg-primary text-white",children:[e,(0,a.jsx)("button",{onClick:()=>c(e),className:"hover:text-gray-200 focus:outline-none","aria-label":"Remove ".concat(e),children:(0,a.jsx)(L.Z,{size:14})})]},t))})]})},eb=s(39378),ej=s(1109),ey=e=>{let{tempCharData:t,setTempCharData:s}=e;var l,i,n,o,u,m,x,h,p,f,g,v,b,j,y,w,_,N,C,k,I,E=(0,ei.Z)();try{let[e,E]=(0,c.useState)([]),{data:R}=(0,eg.N)(),A=t=>{let{btnName:s}=t;e.includes(s)?E(e.filter(e=>e!==s)):E([...e,s])},P=(0,d.useRecoilValue)(ef.jo),M=er.O,z=e=>{let t=document.getElementById(e);t?t.play().then(()=>{}).catch(e=>{console.error("Error playing audio:",e)}):console.error("Audio element not found")},L=e=>{let t=document.getElementById(e);t?(t.pause(),t.currentTime=0):console.error("Audio element not found")};(0,c.useEffect)(()=>()=>{var e;L(null!==(e=null==t?void 0:t.voice)&&void 0!==e?e:"")},[]);let T=(null==t?void 0:t.gender)==="Female"?null==P?void 0:null===(l=P.voices)||void 0===l?void 0:l.Female:null==P?void 0:null===(i=P.voices)||void 0===i?void 0:i.Male,D=(null==t?void 0:t.gender)==="Female"?[...null!==(N=null==P?void 0:null===(n=P.relationships)||void 0===n?void 0:n.Female)&&void 0!==N?N:[],...null!==(C=null==P?void 0:null===(o=P.relationships)||void 0===o?void 0:o.Neutral)&&void 0!==C?C:[]]:[...null!==(k=null==P?void 0:null===(u=P.relationships)||void 0===u?void 0:u.Male)&&void 0!==k?k:[],...null!==(I=null==P?void 0:null===(m=P.relationships)||void 0===m?void 0:m.Neutral)&&void 0!==I?I:[]];return(0,a.jsx)("div",{className:"flex flex-col gap-5 items-center justify-center text-foreground mx-2",children:(0,a.jsxs)("div",{className:"max-w-96 sm:w-full xs:w-[370px] w-72 space-y-5",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,a.jsxs)(ee.qE,{children:[(0,a.jsx)(ee.F$,{src:t.initial_image,alt:"avatar"}),(0,a.jsx)(ee.Q5,{children:"Avatar"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-3 ",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)(ep._,{htmlFor:"first-name",className:"ps-2  font-bold text-foreground",children:"First Name"}),(0,a.jsx)(em.I,{id:"first-name",type:"text",value:t.first_name,onChange:e=>s(t=>({...t,first_name:e.target.value})),placeholder:"First Name",className:"md:text-sm text-xs ring-offset-primary   placeholder:text-[#989898] focus-visible:ring-0 focus-visible:ring-none focus-visible:ring-offset-1"})]}),(0,a.jsxs)("div",{className:"",children:[(0,a.jsx)(ep._,{htmlFor:"last-name",className:" ps-2 font-bold text-foreground",children:"Last Name"}),(0,a.jsx)(em.I,{id:"last-name",type:"text",value:t.last_name,onChange:e=>s(t=>({...t,last_name:e.target.value})),placeholder:"Last Name",className:"md:text-sm text-xs ring-offset-primary  placeholder:text-[#989898] focus-visible:ring-0 focus-visible:ring-none focus-visible:ring-offset-1"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsxs)("h2",{className:"md:text-xl font-semibold ",children:["About ",t.first_name]}),(0,a.jsx)("p",{className:"text-xs font-light",children:"This section directly impacts how ".concat(t.first_name," replies back. Have fun\n					changing it up!")}),(0,a.jsx)(ed.g,{value:t.about_me,onChange:e=>s(t=>({...t,about_me:e.target.value})),className:"hide-scrollbar min-h-[100px] text-xs"})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-1.5 ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between ps-2 2xl:-me-[23px] lg:-me-[22px] md:-me-[15px] sm:-me-[16px] xs:-me-[12px] -me-[13px]",children:[(0,a.jsxs)("h2",{className:"text-sm font-semibold ",children:["Your Relationship with ",t.first_name]}),(0,a.jsx)(eu,{id:"Relationship",activeCategory:e,onClick:A})]}),e.includes("Relationship")?(0,a.jsx)("div",{children:(0,a.jsx)(em.I,{value:t.relationship,onChange:e=>s(t=>({...t,relationship:e.target.value})),placeholder:"Describe Custom Relationship here..."})}):(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(ev,{dropdownData:D||[],defaultValue:null==t?void 0:null===(x=t.relationship)||void 0===x?void 0:x.split(" - ")[0],setTempCharData:s,trait:"relationship",isArray:Array.isArray(null==t?void 0:t.relationship)})})]}),(0,a.jsxs)("div",{className:"flex flex-col ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between  ps-2 2xl:-me-[23px] lg:-me-[22px] md:-me-[15px] sm:-me-[16px] xs:-me-[12px] -me-[13px]",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold ",children:"Personality"}),(0,a.jsx)(eu,{id:"Personality",activeCategory:e,onClick:A})]}),e.includes("Personality")?(0,a.jsx)("div",{children:(0,a.jsx)(em.I,{value:t.personality,onChange:e=>s(t=>({...t,personality:e.target.value})),placeholder:"Describe Custom Personality here..."})}):(0,a.jsx)("div",{children:(0,a.jsx)(ev,{dropdownData:(null===(h=M[5])||void 0===h?void 0:h.personalities)||[],defaultValue:null==t?void 0:null===(p=t.personality)||void 0===p?void 0:p.split(" - ")[0],setTempCharData:s,trait:"personality",isArray:Array.isArray(null==t?void 0:t.personality)})})]}),(0,a.jsxs)("div",{className:"flex flex-col ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between  ps-2 2xl:-me-[23px] lg:-me-[22px] md:-me-[15px] sm:-me-[16px] xs:-me-[12px] -me-[13px]",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold ",children:"Occupation"}),(0,a.jsx)(eu,{id:"Occupation",activeCategory:e,onClick:A})]}),e.includes("Occupation")?(0,a.jsx)("div",{children:(0,a.jsx)(em.I,{value:null==t?void 0:null===(f=t.work)||void 0===f?void 0:f[0],onChange:e=>s(t=>{let s=["work"];return s[0]=e.target.value,{...t,work:s}}),placeholder:"Write Custom Occupation here..."})}):(0,a.jsx)("div",{children:(0,a.jsx)(ev,{dropdownData:(null===(g=M[7])||void 0===g?void 0:g.occupations)||[],defaultValue:null==t?void 0:null===(v=t.work)||void 0===v?void 0:v[0],setTempCharData:s,trait:"work",isArray:Array.isArray(null==t?void 0:t.work)})})]}),(0,a.jsxs)("div",{className:"flex flex-col ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between  ps-2 2xl:-me-[23px] lg:-me-[22px] md:-me-[15px] sm:-me-[16px] xs:-me-[12px] -me-[13px]",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold ",children:"Hobby"}),(0,a.jsx)(eu,{id:"Hobby",activeCategory:e,onClick:A})]}),e.includes("Hobby")?(0,a.jsx)("div",{children:(0,a.jsx)(em.I,{value:(null==t?void 0:null===(b=t.hobbies)||void 0===b?void 0:b.join(", "))||"",onChange:e=>{let t=e.target.value;s(e=>({...e,hobbies:[t]}))},onBlur:t=>{let a=t.target.value.split(",").map(e=>e.trim()).filter(e=>""!==e);s(e=>({...e,hobbies:a})),E(e.filter(e=>"Hobby"!==e))},placeholder:"Write Custom Hobbies here... (separate with commas)"})}):(0,a.jsx)("div",{children:(0,a.jsx)(ev,{dropdownData:(null===(j=M[8])||void 0===j?void 0:j.hobbies)||[],defaultValue:null==t?void 0:null===(y=t.hobbies)||void 0===y?void 0:y.join(", "),setTempCharData:s,trait:"hobbies",isArray:Array.isArray(null==t?void 0:t.hobbies)})})]}),(0,a.jsxs)("div",{className:"flex flex-col ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between ps-2 2xl:-me-[23px] lg:-me-[22px] md:-me-[15px] sm:-me-[16px] xs:-me-[12px] -me-[13px]",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold ",children:"Kinks"}),(0,a.jsx)(eu,{id:"Kinks",activeCategory:e,onClick:A})]}),e.includes("Kinks")?(0,a.jsx)("div",{children:(0,a.jsx)(em.I,{value:t.kink,onChange:e=>s(t=>({...t,kink:e.target.value})),placeholder:"Write Custom Kinks here..."})}):(0,a.jsx)("div",{children:(0,a.jsx)(ev,{dropdownData:(null===(w=M[9])||void 0===w?void 0:w.kinks)||[],defaultValue:null==t?void 0:null===(_=t.kink)||void 0===_?void 0:_.split(" - ")[0],setTempCharData:s,trait:"kink",isArray:Array.isArray(null==t?void 0:t.kink)})})]}),(0,a.jsxs)("div",{className:"flex flex-col ",children:[(0,a.jsxs)("div",{className:"flex flex-row items-center justify-between 2xl:me-[7.5px] lg:me-[8px] md:me-[12px] sm:me-[16px] xs:me-[14px] me-[19px] ps-2 pb-1 ",children:[(0,a.jsx)("h2",{className:"text-sm font-semibold ",children:"Voice"}),(0,a.jsxs)("span",{onClick:()=>{var e;return z(null!==(e=null==t?void 0:t.voice)&&void 0!==e?e:"")},title:"Click to play Voice","aria-label":"Click to play Voice",className:"md:scale-[1.2] transition ease-in-out  cursor-pointer duration-300 ",children:[(0,a.jsxs)("audio",{id:null==t?void 0:t.voice,children:[(0,a.jsx)("source",{src:t.voice_url,type:"audio/wav"}),"Your browser does not support the audio element"]},null==t?void 0:t.voice),(null==t?void 0:t.voice)!=="Custom"&&(0,a.jsx)(ej.MVT,{className:"text-white hover:text-primary text-base"})]})]}),(0,a.jsx)("div",{children:(0,a.jsxs)(eh.h_,{children:[(0,a.jsx)(eh.$F,{asChild:!0,id:"voices","aria-label":"Choose Voice",className:" sm:w-full xs:w-[370px] w-72 flex justify-center items-center mx-auto z-30",children:(0,a.jsxs)(S.z,{"aria-label":"Choose Voice",variant:"outline",value:null==t?void 0:t.voice,className:"font-normal justify-between hover:bg-background border-0 hover:text-primary-foreground  focus:ring-1 focus:ring-primary focus:ring-offset-1 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary focus-visible:ring-offset-1",children:[(null==t?void 0:t.voice)||"Select an option....",(0,a.jsx)(r.default,{src:"/icons/caret-down.svg",alt:"Down arrow",width:"16",height:"16"})]})}),(0,a.jsx)(eh.AW,{className:"sm:w-[380px] xs:w-[365px] w-[280px] my-1 cursor-pointer bg-background border border-primary h-72 overflow-y-auto custom-scrollbar z-30 rounded-lg ",children:(0,a.jsx)(eh._x,{defaultValue:(null==t?void 0:t.voice)||"",onValueChange:e=>{let t=e.split("_")[0],a=e.split("_")[1],l=e.split("_")[2];s(e=>({...e,voice:t,voice_embedding:a,voice_url:l}))},className:"py-1 border-0 rounded-none mx-1 ",children:T&&T.map((e,s)=>(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(eh.qB,{value:"".concat(e.name,"_").concat(e.embedding,"_").concat(e.audio_url),className:"focus:bg-primary  relative  flex items-center  z-30 h-8 px-4 hover:ring-0 hover:ring-none rounded-md hover:ring-offset-0  hover:outline-none ",children:[(null==t?void 0:t.voice)===e.name?(0,a.jsx)(r.default,{src:"/icons/check.svg",alt:"Check icon",width:"16",height:"16",className:"absolute left-3"}):"",(0,a.jsx)("span",{className:"".concat((null==t?void 0:t.voice)===e.name?"font-semibold":""," px-4 text-sm text-white"),children:e.name})]},s)}))})})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex sm:flex-row flex-col  gap-y-0.5 items-center justify-between ps-2 pb-2 ",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-sm font-semibold text-nowrap",children:"Voice Clone"}),(0,a.jsx)("p",{className:"text-gray-500 xs:text-xs text-[11px]",children:"Please upload an audio file up to 30 seconds long"}),(0,a.jsx)("p",{className:"text-gray-500 xs:text-xs text-[11px]",children:"Preferred audio formats: .mp3, .wav, .m4a"})]})}),(0,a.jsx)(eb.Z,{data:t,setData:s})]})]})})}finally{E.f()}},ew=s(79861),e_=s(99785),eN=s(67910),eC=()=>{let e=(0,y.P)(),[t,s]=(0,c.useState)(""),l=(0,o.useSearchParams)(),i=(0,o.useRouter)(),r=(0,o.usePathname)(),[u,m]=(0,c.useState)(""),[x,h]=(0,c.useState)(),[p,f]=(0,d.useRecoilState)(ew.G),{mutateAsync:g}=(0,e_.b)(),v=(0,el.l)()({takeCharIdFrom:"editCharIdAtom"}),[b,j]=(0,d.useRecoilState)(eN.cf),w=(0,d.useSetRecoilState)(eN.ZQ),_=(0,d.useSetRecoilState)(eN.Kf),N=(0,d.useSetRecoilState)(eN._0),[C,k]=(0,d.useRecoilState)(eN.qY);(0,c.useEffect)(()=>{v&&h(v)},[v]);let I=async()=>{if(b&&!C){n.default.error("Please agree to the consent to proceed");return}j(null),w(0),_(!1),N(""),k(!1),u?i.push("".concat(r,"?image-option=").concat(u)):i.push(r||"/"),m(""),(null==l?void 0:l.has("tab"))&&(null==l?void 0:l.get("tab"))==="looks"&&localStorage.setItem("character",JSON.stringify(p)),await g({updates:x,charId:p}),f("")};return(0,c.useEffect)(()=>{s((null==l?void 0:l.get("tab"))||""),(null==l?void 0:l.has("image-option"))&&m((null==l?void 0:l.get("image-option"))||"")},[l,r]),(0,a.jsxs)("div",{className:"".concat("looks"===t||"personality"===t?"":"hidden"," fixed inset-0 z-50 overflow-y-auto custom-scrollbar sm:py-4 py-2 px-2 bg-popover\n "),children:[(0,a.jsx)("div",{className:"fixed top-0 ",children:(0,a.jsx)(S.z,{"aria-label":"Update character and Close edit character page",variant:"default",size:"icon",className:"absolute sLaptop:top-4 sm:top-4 top-2 .5 xs:left-3 left-0 sLaptop:h-10 sLaptop:px-4 sLaptop:py-2 sLaptop:scale-[1] md:scale-[.85] sm:scale-[.8] xs:scale-[.7] scale-[.6]",onClick:I,children:(0,a.jsx)("span",{className:"",children:e.angleLeft})})}),(0,a.jsx)("div",{className:"flex flex-row items-center justify-center",children:(0,a.jsxs)("div",{className:"flex flex-row sLaptop:gap-56  md:gap-44 sm:gap-36 xs:gap-20 gap-[18px]",children:[(0,a.jsxs)("button",{"aria-label":"Edit character looks",className:"flex flex-row items-center gap-1 text-[#b62a5e]  lg:h-[55px] sm:h-[45px] h-[40px] md:text-2xl  sm:text-xl font-bold \n                        ".concat("looks"===t?" border-b-2  border-[#b62a5e] text-primary":" "),onClick:()=>{u?i.push("?image-option=".concat(u,"&tab=looks")):i.push("?tab=looks")},children:[(0,a.jsx)("span",{className:"lg:scale-[1.1] md:scale-[.95] scale-[.75]",children:e.adjustment})," Looks"]}),(0,a.jsxs)("button",{"aria-label":"Edit character personality",className:"flex flex-row items-center gap-1 text-[#b62a5e]  lg:h-[55px] sm:h-[45px] h-[40px] md:text-2xl sm:text-xl font-bold \n                        ".concat("personality"===t?" border-b-2  border-[#b62a5e] text-primary":" "),onClick:()=>{u?i.push("?image-option=".concat(u,"&tab=personality")):i.push("?tab=personality&charId=".concat(null==v?void 0:v._id))},children:[(0,a.jsx)("span",{className:"lg:scale-[1.1] md:scale-[.95] scale-[.75]",children:e.puzzle})," Personality"]})]})}),(0,a.jsx)("section",{className:"pb-10 sm:pt-10 pt-5 ".concat("looks"===t?"block":"hidden"),children:x&&h&&(0,a.jsx)(ex,{tempCharData:x,setTempCharData:h})}),(0,a.jsx)("section",{className:"pb-10 sm:pt-10 pt-5 ".concat("personality"===t?"block":"hidden"),children:x&&h&&(0,a.jsx)(ey,{tempCharData:x,setTempCharData:h})})]},null==x?void 0:x._id)},eS=s(9962),ek=s(80217),eI=()=>(0,c.useCallback)(e=>{window.open(e,"_blank")},[]),eE=()=>{let{mutateAsync:e}=(0,e_.b)();return async t=>{let s=localStorage.getItem("sd_active_char_id");if(!s)return;let a={};if(t.initialImage&&(a.initial_image=t.initialImage),t.hoverImage&&(a.hover_image=t.hoverImage),t.hoverVideo&&(a.hover_video=t.hoverVideo),0!==Object.keys(a).length)try{await e({updates:a,charId:s,showToast:!1}),n.default.success("Media updated successfully!")}catch(e){n.default.error("Failed to update media!")}}},eR=s(76821),eA=s(69509),eP=s(10532);let eM=()=>{let e,t="",s=async s=>{let{imgId:a}=s;t=localStorage.getItem("sd_active_char_id"),e=I.E.getQueryData(["gen-images",{charId:t}]),I.E.setQueryData(["gen-images",{charId:t}],e=>({msg:e.msg.filter(e=>e._id!==a)})),await I.j.delete("/char_image_gen/".concat(a)),I.E.invalidateQueries({queryKey:["gen-images",{charId:t}]}),n.default.success("Image deleted successfully")};return(0,p.useMutation)({mutationFn:async e=>{let{imgId:t}=e;await s({imgId:t})},onError:s=>{var a,l;I.E.setQueryData(["gen-images",{charId:t}],e),n.default.error((null==s?void 0:null===(l=s.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.msg)||"Error deleting image")}})};var ez=s(40282),eL=s(77687),eT=s(56688),eD=s(90058);let eZ=e=>{let{isOpen:t,canGoPrev:s,canGoNext:a,onPrev:l,onNext:i,onClose:n}=e,r=(0,c.useCallback)(e=>{t&&!T.tq&&("ArrowLeft"===e.key&&s?l():"ArrowRight"===e.key&&a?i():"Escape"===e.key&&n())},[t,s,a,l,i,n]);(0,c.useEffect)(()=>{if(!T.tq)return window.addEventListener("keydown",r),()=>{window.removeEventListener("keydown",r)}},[r])};var eF=s(22514),eG=s(695),eU=e=>{let{images:t}=e,s=(0,d.useSetRecoilState)(u.LM),l=(0,y.P)(),[i,o]=(0,d.useRecoilState)(u.s4),[m,x]=(0,c.useState)([]),[h,p]=(0,c.useState)(null),[f,g]=(0,c.useState)(!1);(0,c.useEffect)(()=>{x(i)},[]);let v=e=>{let{image:t,video:s}=e;o(e=>{let a=e.find(e=>e.image===t)?e.filter(e=>e.image!==t):e.length<5?[...e,{image:t,video:s}]:e,l=a.find(e=>e.image===t);return s&&l&&0===a.indexOf(l)&&(0,n.default)("Live photos will show as still images when used as profile picture",{duration:4e3,icon:"\uD83D\uDCF8"}),a})},b=i.length>=1&&i.length<=5;return(0,a.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-60",children:(0,a.jsxs)("div",{className:"bg-card rounded-lg p-4 w-[90%] max-w-md max-h-[70vh] flex flex-col",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4 text-center",children:"Select Character Images"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2 overflow-y-auto custom-scrollbar flex-1",children:null==t?void 0:t.map((e,t)=>(0,a.jsxs)("div",{className:(0,ea.cn)("relative cursor-pointer overflow-hidden rounded-lg border-2 transition-all h-max",i.some(t=>t.image===e.imageUrl)?"border-primary":"border-transparent hover:border-primary"),onClick:()=>v({image:e.imageUrl,video:e.videoUrl||""}),onMouseEnter:()=>p(t),onMouseLeave:()=>p(null),children:[e.videoUrl&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute top-1 right-1 bg-black/50 rounded-full p-1 z-10",children:l.livePhotoIcon}),null!==h&&h===t&&(0,a.jsxs)(a.Fragment,{children:[f&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black/60 animate-pulse z-10"}),(0,a.jsx)("video",{src:e.videoUrl,className:"absolute inset-0 w-full h-full object-cover",loop:!0,muted:!0,playsInline:!0,autoPlay:!0,onLoadStart:()=>g(!0),onPlaying:()=>g(!1)})]})]}),(0,a.jsx)(r.default,{src:e.imageUrl,alt:"Character image ".concat(t+1),className:"object-cover",unoptimized:!0,width:300,height:300}),i.some(t=>t.image===e.imageUrl)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-primary/20"}),(0,a.jsx)("div",{className:"absolute bottom-1 right-1 bg-background border border-primary text-primary w-6 h-6 rounded-full flex items-center justify-center z-10",children:i.findIndex(t=>t.image===e.imageUrl)+1}),0===i.findIndex(t=>t.image===e.imageUrl)&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-primary/70 py-1.5 pl-2 text-white text-sm font-bold",children:"Profile Pic"})]})]},t))}),(0,a.jsxs)("div",{className:"mt-4 flex justify-between gap-2",children:[(0,a.jsx)(S.z,{variant:"noFill",onClick:()=>{o(m),s(!1)},children:"Cancel"}),(0,a.jsx)(S.z,{className:"disabled:bg-primary/50 transition-all",onClick:()=>{b&&s(!1)},disabled:!b,children:"Confirm"})]})]})})},eH=()=>(0,a.jsx)("div",{className:"flex gap-3 animate-pulse mb-2 opacity-90",children:Array.from({length:3}).map((e,t)=>(0,a.jsx)("div",{className:"w-[70px] h-[100px] bg-background rounded-lg border border-primary"},t))});function eO(e){var t;let{newHoverImage:s}=e,[l,i]=(0,d.useRecoilState)(ew.G),[o,x]=(0,d.useRecoilState)(u.s4),[h,p]=(0,d.useRecoilState)(u.LM),f=(0,d.useSetRecoilState)(u.nA),g=(0,y.P)(),{mutateAsync:v}=(0,e_.b)(),{data:b}=(0,m.j)(),j=(0,el.l)()(l?{customCharId:l}:{}),w=(null==j?void 0:j.first_name)+" "+(null==j?void 0:j.last_name)||"Your Character",_=(null==j?void 0:j._id)||"",{data:N,isLoading:C}=(0,ek.UB)({charId:_}),k=null==N?void 0:null===(t=N.msg)||void 0===t?void 0:t.filter(e=>{var t;return e.imageUrl&&!(null==b?void 0:null===(t=b.img_blurred)||void 0===t?void 0:t.includes(e._id))&&("initial_image"===e._id||e.imageUrl!==(null==j?void 0:j.initial_image))});(0,c.useEffect)(()=>{0===o.length&&((null==j?void 0:j.images)&&j.images.length>0?(s&&s.image&&j.images.some(e=>e.image===s.image)?(0,n.default)("Image already exists in the carousel.",{icon:"\uD83D\uDCF8"}):4===j.images.length&&(0,n.default)("Please remove one image to add a new one.",{icon:"\uD83D\uDCF8"}),x([{image:j.initial_image||"",video:""},...j.images.length<4?[s]:[],...j.images.map(e=>({image:(null==e?void 0:e.image)||"",video:(null==e?void 0:e.video)||""}))])):(null==k?void 0:k.length)&&k.length>0&&x([{image:(null==j?void 0:j.initial_image)||"",video:""},s]))},[null==k?void 0:k.length]);let I=()=>{p(!0)},E=e=>{x(t=>t.filter((t,s)=>s!==e))},R=()=>{x([]),i(""),p(!1),f(!1)},A=async()=>{await v({updates:{initial_image:o[0].image,images:o.slice(1)},charId:_,showToast:!1}),R(),n.default.success("Character images updated successfully!")};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(Y.Vq,{open:!0,onOpenChange:R,modal:!0,children:(0,a.jsx)(Y.cZ,{className:"sm:max-w-[425px]",closeBtn:"hidden",children:h?(0,a.jsx)(eU,{images:k}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(Y.fK,{children:[(0,a.jsxs)(Y.$N,{className:"text-xl",children:["Set Images for"," ",(0,a.jsx)("span",{className:"text-primary",children:w})]}),(0,a.jsxs)(Y.Be,{className:"space-y-4 pt-4",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Select up to 5 images for your character's profile."}),(0,a.jsx)("div",{className:"overflow-x-auto sm:max-w-[380px] custom-scrollbar",children:(0,a.jsxs)("div",{className:"flex gap-3 w-full pb-2",children:[C?(0,a.jsx)(eH,{}):o.map((e,t)=>(0,a.jsxs)("div",{className:"relative border border-primary rounded-lg mb-2",children:[(0,a.jsxs)("div",{className:"relative cursor-pointer rounded-lg overflow-hidden w-max",onClick:I,children:[e.video&&(0,a.jsx)("div",{className:"scale-75 text-white absolute top-0.5 right-0.5 bg-black/50 rounded-full p-1 z-10",children:g.livePhotoIcon}),(0,a.jsx)(r.default,{src:e.image,alt:"Character image ".concat(t+1),className:"object-cover",unoptimized:!0,width:70,height:70}),0===t&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-primary/70 font-bold text-white text-xs py-1 px-2 text-center",children:"Profile Pic"})]}),(0,a.jsx)("button",{onClick:()=>E(t),className:"absolute -bottom-1.5 -right-1.5 border border-primary bg-background z-10 w-5 h-5 rounded-full flex items-center justify-center",children:(0,a.jsx)(L.Z,{className:"w-3 h-3 text-primary"})})]},t)),!C&&o.length<5&&(0,a.jsxs)("div",{className:"min-w-[70px] h-[100px] border-2 border-dashed border-primary text-primary text-sm text-center rounded-lg flex items-center justify-center cursor-pointer font-bold",onClick:I,children:["Add",(0,a.jsx)("br",{}),"Image"]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-between gap-2",children:[(0,a.jsx)(S.z,{onClick:R,variant:"noFill",children:"Cancel"}),(0,a.jsx)(S.z,{onClick:A,disabled:o.length<1,className:"disabled:opacity-50",children:"Save Images"})]})]})})})})}var eV=s(99010);function eq(e){var t,s,l,i,n;let{charId:x}=e,h=(0,y.P)(),[p,f]=(0,d.useRecoilState)(u._A),{data:g}=(0,m.j)(),v=eI(),b=(0,eR.Z)(),j=eE(),{data:w}=(0,ek.UB)(),[_,N]=(0,c.useState)(0),[C,k]=(0,c.useState)(),{mutateAsync:I}=(0,eA.K)(),{mutateAsync:E}=(0,eP.N)(),{mutateAsync:R,isError:A}=eM(),P=(0,ez.F)({}),M=(0,eL.t)(),z=(0,o.usePathname)(),L=(0,d.useRecoilValue)(eT.FQ),[T,D]=(0,c.useState)(p.isLiveImg),Z=(0,c.useRef)(0),F=(0,c.useRef)(0),[G,U]=(0,c.useState)(!1),[H,O]=(0,d.useRecoilState)(u.nA),V=(0,d.useSetRecoilState)(ew.G);(0,eV.n)(),(0,c.useEffect)(()=>{if(Z.current<1){Z.current++;return}f({open:!1,id:"",src:"",prompt:"",isLiveImg:!1})},[z]),(0,c.useEffect)(()=>{if(F.current<1){F.current++;return}T?p.isLiveImg?f({...p,src:(null==C?void 0:C.videoUrl)||""}):U(!0):f({...p,src:(null==C?void 0:C.imageUrl)||""})},[T,C]);let q=(null===(t=p.prompt)||void 0===t?void 0:t.length)>19?(null===(s=p.prompt)||void 0===s?void 0:s.slice(0,20))+"...":p.prompt,B=async()=>{C&&("generated"===C.imageType?await I({image:C}):await E({imgId:C._id,imgPrompt:C.image_prompt}))},Q=async()=>{var e,t,s,a,l,i,n,r,o,c,d,u,m,x,h,p;let g=P();C&&("generated"===C.imageType?await R({imgId:C._id}):await M({msgId:C._id,msgType:"Image",convId:g,prevMsgText:"",gallery_delete:!0}),!A&&(null==w?void 0:null===(e=w.msg)||void 0===e?void 0:e.length)&&(null==w?void 0:null===(t=w.msg)||void 0===t?void 0:t.length)>0&&((null==w?void 0:null===(s=w.msg)||void 0===s?void 0:s.length)===2?f({open:!0,id:null==w?void 0:null===(i=w.msg)||void 0===i?void 0:null===(l=i[0])||void 0===l?void 0:l._id,src:(null==w?void 0:null===(r=w.msg)||void 0===r?void 0:null===(n=r[0])||void 0===n?void 0:n.live_photo)?(null==w?void 0:null===(c=w.msg)||void 0===c?void 0:null===(o=c[0])||void 0===o?void 0:o.videoUrl)||"":null==w?void 0:null===(u=w.msg)||void 0===u?void 0:null===(d=u[0])||void 0===d?void 0:d.imageUrl,prompt:null==w?void 0:null===(x=w.msg)||void 0===x?void 0:null===(m=x[0])||void 0===m?void 0:m.image_prompt,isLiveImg:(null==w?void 0:null===(p=w.msg)||void 0===p?void 0:null===(h=p[0])||void 0===h?void 0:h.live_photo)||!1}):_!==(null==w?void 0:null===(a=w.msg)||void 0===a?void 0:a.length)-1?Y():$()))},K=e=>{let{id:t,src:s,prompt:a,isLiveImg:l}=e;f({open:!0,id:t,src:s,prompt:a,isLiveImg:l})};(0,c.useEffect)(()=>{var e,t;N((null==w?void 0:null===(e=w.msg)||void 0===e?void 0:e.findIndex(e=>e._id===p.id))||0),k(null==w?void 0:null===(t=w.msg)||void 0===t?void 0:t.find(e=>e._id===p.id))},[p,w]);let Y=()=>{var e,t;if((null==w?void 0:null===(e=w.msg)||void 0===e?void 0:e.length)&&_+1<(null==w?void 0:null===(t=w.msg)||void 0===t?void 0:t.length)){let e=null==w?void 0:w.msg[_+1];(null==e?void 0:e.live_photo)?D(!0):D(!1),K({id:e._id,src:T&&(null==e?void 0:e.live_photo)?e.videoUrl||"":e.imageUrl,prompt:e.image_prompt,isLiveImg:e.live_photo||!1})}},$=()=>{if(0!==_){let e=null==w?void 0:w.msg[_-1];(null==e?void 0:e.live_photo)?D(!0):D(!1),e&&K({id:null==e?void 0:e._id,src:T&&(null==e?void 0:e.live_photo)?e.videoUrl||"":e.imageUrl,prompt:null==e?void 0:e.image_prompt,isLiveImg:e.live_photo||!1})}},W=()=>{f({open:!1,id:"",src:"",prompt:"",isLiveImg:!1})};return eZ({isOpen:p.open,canGoPrev:0!==_,canGoNext:!!(null==w?void 0:w.msg)&&_!==(null==w?void 0:null===(l=w.msg)||void 0===l?void 0:l.length)-1,onPrev:$,onNext:Y,onClose:W}),(0,a.jsxs)("div",{className:"fixed inset-0 top-16 z-40 overflow-auto custom-scrollbar sm:py-4 py-2 px-6 min-h-screen bg-linear-to-t from-[#A11F46] via-[#350A17] to-[#7F1E3B]",children:[(0,a.jsxs)("div",{className:"mb-20",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2 items-center",children:[(0,a.jsx)("div",{className:"relative right-[18%]",children:(0,a.jsxs)(S.z,{"aria-label":"Close Image Gallery",variant:"dark",size:"sm",onClick:W,className:"border border-primary text-primary pl-2 rounded-xl",children:[(0,a.jsx)("span",{className:"scale-75",children:h.caretLeft}),"Go Back"]})}),(0,a.jsxs)("div",{className:"w-full max-w-sm md:max-w-md",children:[(0,a.jsx)("div",{className:"p-0",children:(0,a.jsxs)("div",{className:"relative flex justify-center",children:[(0,a.jsx)("button",{onClick:()=>D(e=>!e),className:"scale-125 cursor-pointer absolute top-3 right-3 transition-colors p-1 rounded-full z-10 \n                  ".concat(T?"bg-primary":"bg-black/50","\n                  ").concat(p.isLiveImg?"":"hidden","\n                  ").concat((null==C?void 0:C.live_photo)&&!(null==C?void 0:C.videoUrl)?"animate-rotate-360":"","\n                  "),children:h.livePhotoIcon}),"pending"===p.id?(0,a.jsx)("div",{className:"xl:w-96 lg:w-72 md:w-80 w-64 h-auto aspect-square flex justify-center items-center ",children:(0,a.jsx)(eF.a,{})}):L===p.id?(0,a.jsxs)("div",{className:"xl:w-96 lg:w-72 md:w-80 w-64 h-auto aspect-square flex justify-center items-center ",children:["This image failed to send."," ",(0,a.jsxs)("button",{"aria-label":"Retry failed image",onClick:B,className:"underline text-primary",children:[" ","Retry?"]})]}):T?(null==p?void 0:p.isLiveImg)&&!(null==p?void 0:p.src)?(0,a.jsx)("div",{className:"xl:w-96 lg:w-72 md:w-80 w-64 h-auto aspect-square flex justify-center items-center ",children:(0,a.jsx)(eF.a,{})}):(null==p?void 0:p.src)?(0,a.jsx)("video",{src:p.src,autoPlay:!0,muted:!0,playsInline:!0,loop:!0,preload:"metadata",className:"object-cover rounded-xl",children:(0,a.jsx)("source",{src:p.src,type:"video/mp4"})}):null:(null==p?void 0:p.isLiveImg)||(null==p?void 0:p.src)?(0,a.jsx)(r.default,{src:p.src,alt:q,height:832,width:1216,unoptimized:!0,className:"aspect-13/19 object-cover rounded-xl ".concat((0,eD.lv)({user:g,id:null==C?void 0:C._id})?"":"blur-[20px]")}):(0,a.jsx)("div",{className:"xl:w-96 lg:w-72 md:w-80 w-64 h-auto aspect-square flex justify-center items-center ",children:(0,a.jsx)(eF.a,{})})]})}),(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 flex items-center",children:(0,a.jsx)(eS.Z,{content:"Previous Image",side:"right",trigger:(0,a.jsx)(S.z,{"aria-label":"Previous Image",onClick:$,disabled:0===_,size:"icon",className:"p-2 m-2 rounded-full bg-primary",children:h.angleLeft})})}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center",children:(0,a.jsx)(eS.Z,{content:"Next Image",side:"left",trigger:(0,a.jsx)(S.z,{"aria-label":"Next Image",onClick:Y,disabled:(null==w?void 0:w.msg)&&_===(null==w?void 0:null===(i=w.msg)||void 0===i?void 0:i.length)-1,size:"icon",className:"p-2 m-2 rotate-180 rounded-full bg-primary ",children:h.angleLeft})})}),(0,a.jsxs)("div",{className:"flex justify-center gap-4 p-4",children:[(0,a.jsx)(eS.Z,{content:"Set as Profile Picture",trigger:(0,a.jsx)(S.z,{"aria-label":"Set as Profile Picture",onClick:()=>j({initialImage:p.src}),disabled:"initial_image"===p.id||p.isLiveImg,size:"icon",className:"p-2 rounded-full bg-primary ",children:h.userAvatar})}),(0,a.jsx)(eS.Z,{content:"Add to Profile Carousel",trigger:(0,a.jsx)(S.z,{"aria-label":"Add to Profile Carousel",onClick:()=>{V(x),O(!0)},disabled:"initial_image"===p.id||p.isLiveImg,size:"icon",className:"p-2 rounded-full bg-primary ",children:h.userAvatar2})}),(0,a.jsx)(eS.Z,{content:"Regenerate Image",trigger:(0,a.jsx)(S.z,{"aria-label":"Regenerate Image",onClick:B,disabled:"initial_image"===p.id||p.isLiveImg,size:"icon",className:"p-2 rounded-full bg-primary ",children:h.refresh})}),(0,a.jsx)(eS.Z,{content:"Delete Image",trigger:(0,a.jsx)(S.z,{"aria-label":"Delete Image",onClick:Q,disabled:"initial_image"===p.id,size:"icon",className:"p-2 rounded-full bg-primary ",children:h.trash})}),(0,a.jsx)(eS.Z,{content:"Open image in New Tab",trigger:(0,a.jsx)(S.z,{"aria-label":"Open image in New Tab",onClick:()=>v(p.src),size:"icon",className:"p-2 rounded-full bg-primary ",children:(0,a.jsx)(eB,{})})}),(0,a.jsx)(eS.Z,{content:"Generate Live Photo",trigger:(0,a.jsx)(S.z,{"aria-label":"Generate Live Photo",onClick:()=>U(!0),size:"icon",disabled:p.isLiveImg||"initial_image"===p.id,className:"p-2 rounded-full bg-primary",children:h.livePhotoIcon})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("p",{className:"border border-gray-600 bg-background px-4 py-2 w-full rounded-xl",children:p.prompt||"Prompt not found!"}),(0,a.jsx)(eS.Z,{content:"Copy Prompt",trigger:(0,a.jsx)(S.z,{"aria-label":"Copy Prompt",onClick:()=>b(p.prompt||"Prompt not found!"),size:"icon",variant:"ghost",className:"ml-2 relative",children:h.copy})})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"p-4 grid grid-flow-col auto-cols-max gap-2 overflow-x-auto custom-scrollbar",children:(0,a.jsx)("div",{className:"grid grid-rows-2 gap-2",children:null==w?void 0:null===(n=w.msg)||void 0===n?void 0:n.map((e,t)=>(0,a.jsxs)("div",{className:"\n                      relative\n                      ".concat(t%2==0?"row-start-1":"row-start-2","\n                    "),children:[(0,a.jsx)(r.default,{onClick:()=>K({id:e._id,src:e.imageUrl,prompt:e.image_prompt,isLiveImg:e.live_photo||!1}),src:e.imageUrl,onContextMenu:t=>{(0,eD.lv)({user:g,id:e._id})||t.preventDefault()},width:832/9,height:1216/9,alt:q,unoptimized:!0,className:"\n                          aspect-13/19 object-cover rounded-md \n                          ".concat(p.id===e._id?"border-4 border-foreground p-2":"","\n                          ").concat(t%2==0?"row-start-1":"row-start-2","\n                          ").concat((0,eD.lv)({user:g,id:e._id})?"":"blur-[7px]","\n                        ")},e._id),e.live_photo&&(0,a.jsx)("div",{className:"absolute top-2 right-2 bg-black/50 p-1 rounded-full",children:h.livePhotoIcon})]},e._id))})})})]}),G&&(0,a.jsx)(eG.Z,{imageUrl:p.src,closeModal:e=>{let{startLoading:t=!1}=e;t?(f({...p,isLiveImg:!0,src:""}),D(!0)):D(!1),U(!1)},isChatImg:(null==C?void 0:C.imageType)==="message",imgId:(null==C?void 0:C._id)||"",charId:x}),H&&(0,a.jsx)(eO,{newHoverImage:{image:(null==C?void 0:C.imageUrl)||"",video:(null==C?void 0:C.videoUrl)||""}})]})}function eB(e){return(0,a.jsx)("svg",{...e,className:"w-6 h-6 text-gray-800 dark:text-white","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M18 14v4.833A1.166 1.166 0 0 1 16.833 20H5.167A1.167 1.167 0 0 1 4 18.833V7.167A1.166 1.166 0 0 1 5.167 6h4.618m4.447-2H20v5.768m-7.889 2.121 7.778-7.778"})})}var eQ=s(48795),eK=s(78312),eY=s(94693),e$=e=>{let{onMobile:t}=e,s=(0,y.P)(),{data:l}=R(),i=(0,o.useRouter)(),{closeModal:n}=(0,_.d)(),m=(0,d.useSetRecoilState)(u.dG),[x,h]=(0,c.useState)(!1),[p,f]=(0,c.useState)(!1);return(0,a.jsxs)("div",{className:"max-w-full bg-[#0E0E0E] rounded-2xl p-3 space-y-0.5 xl:rounded-lg text-wrap font-thin",children:[(0,a.jsxs)("div",{className:"flex flex-col w-full",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-1 xl:pe-[5px] cursor-pointer",onClick:()=>h(!x),children:[(0,a.jsx)("span",{className:"text-sm w-[200px] xl:text-sm leading-5 xl:leading-[18px]",children:"Cutting-Edge Chat Engines"}),(0,a.jsx)("span",{className:"scale-75 text-primary",children:x?s.minus:s.plus})]}),(0,a.jsx)("div",{className:"space-y-1 ps-4 transition-all duration-300 ease-in-out overflow-hidden ".concat(x?"max-h-[500px] opacity-100 mt-2":"max-h-0 opacity-0"),children:null==l?void 0:l.chat_engines.map(e=>(0,a.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,a.jsx)("span",{className:"text-sm",children:e.name}),(0,a.jsxs)("span",{className:"flex w-[40px] justify-end items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm xl:text-base",children:e.cost}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",width:15,height:15,alt:"Heart icon",unoptimized:!0})]})]},e.name))})]}),(0,a.jsxs)("div",{className:"flex flex-col w-full mt-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-1 xl:pe-[5px] cursor-pointer",onClick:()=>f(!p),children:[(0,a.jsx)("span",{className:"text-sm w-[200px] xl:text-sm leading-5 xl:leading-[18px]",children:"Image Generation Engines"}),(0,a.jsx)("span",{className:"scale-75 text-primary",children:p?s.minus:s.plus})]}),(0,a.jsx)("div",{className:"space-y-1 ps-4 transition-all duration-300 ease-in-out overflow-hidden ".concat(p?"max-h-[500px] opacity-100 mt-2":"max-h-0 opacity-0"),children:null==l?void 0:l.image_engines.map(e=>(0,a.jsxs)("div",{className:"flex justify-between gap-4",children:[(0,a.jsx)("span",{className:"text-sm",children:e.name}),(0,a.jsxs)("span",{className:"flex w-[40px] justify-end items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm xl:text-base",children:e.cost}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",width:15,height:15,alt:"Heart icon",unoptimized:!0})]})]},e.name))})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm w-[200px] xl:text-sm",children:"1 Minute Voice"}),(0,a.jsxs)("span",{className:"flex w-[30px] justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base",children:"2"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-sm w-[200px] xl:text-sm",children:"1 Minute Call"}),(0,a.jsxs)("span",{className:"flex w-[30px] justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base",children:"2"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center space-x-1 pb-2",children:[(0,a.jsx)("span",{className:"text-sm w-[200px] xl:text-sm",children:"5 Seconds Live Image"}),(0,a.jsxs)("span",{className:"flex w-[30px] justify-between items-center space-x-1",children:[(0,a.jsx)("span",{className:"text-lg xl:text-base",children:"8"}),(0,a.jsx)(r.default,{src:"/icons/Heart_Vector_2.svg",height:15,width:15,alt:"Heart icon"})]})]}),(0,a.jsx)("div",{className:"px-2",children:(0,a.jsx)(S.z,{"aria-label":"Go to subscription page to purchase hearts",variant:"default",className:"w-full h-8 text-sm",size:"default",onClick:()=>(i.push("/subscription"),t&&m(!1)),children:"Purchase Hearts"})})]})},eW=s(53428),eX=s(71145),eJ=s(96997),e0=s(38272),e1=s(25417),e2=s(54817),e5=s(97991),e4=s(81857),e3=e=>{var t,s,l;let{setMobMenu:i,isSubscribed:n}=e,{data:u}=(0,m.j)(),[h,p]=(0,c.useState)(!1),{data:f}=(0,q.v)({}),g=(0,o.useRouter)(),v=(0,y.P)(),w=(0,d.useSetRecoilState)(eK.m),_=(0,d.useRecoilValue)(x.loggedIn),{mutateAsync:N}=b(),{data:C}=(0,e5.e)();return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("div",{className:"xl3:hidden fixed top-0 right-0 h-full w-screen bg-[#1C1C1C] shadow-xl z-50",children:(0,a.jsxs)("div",{className:"flex flex-col justify-start h-full",children:[(0,a.jsxs)("div",{className:"flex items-center h-20 border-b border-[#575757] justify-between px-6 bg-[#131313]",children:[(0,a.jsx)(r.default,{src:(null==C?void 0:C.logo.visible)?null==C?void 0:C.logo.desk_url:"/logo.svg",alt:"Secret Desires",width:170,height:50,unoptimized:!0,onClick:()=>{g.push("/"),i(!1)}}),(0,a.jsx)("button",{"aria-label":"Toggle Mobile Navbar Menu",onClick:()=>{i(!1)},className:"flex z-50 justify-end items-center",children:(0,y.P)().cross})]}),(0,a.jsxs)("div",{className:"overflow-y-scroll px-2",children:[(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:"text-sm font-light",children:"MAIN"}),(0,a.jsxs)("div",{className:"px-2 py-2",children:[(0,a.jsx)("div",{className:"text-[#FFD900]",children:(0,a.jsx)(j,{icon:v.gift,text:n?"My Subscription":"Subscribe",onMobile:!0})}),n&&u&&(0,a.jsxs)("div",{className:"flex justify-between items-center text-primary",children:[(0,a.jsx)(j,{icon:(0,a.jsx)(r.default,{width:24,height:24,src:"/icons/Heart_Vector_2.svg",alt:"Heart icon",unoptimized:!0}),text:(null==u?void 0:u.heart_balance)===0||(null==u?void 0:u.heart_balance)===void 0?"0":Math.max(0,Math.round((null==u?void 0:u.heart_balance)*100)/100)+"  Hearts",onMobile:!0}),(0,a.jsx)("span",{onClick:()=>p(!h),className:"text-primary extra-small-text cursor-pointer",children:h?v.minus:v.plus})]}),(0,a.jsx)("div",{className:"overflow-hidden transition-colors duration-300 ease-in-out ".concat(h?"h-auto opacity-100 mb-2":"h-0 opacity-0"),children:(0,a.jsx)(e$,{onMobile:!0})}),(0,a.jsx)(j,{icon:(0,a.jsx)(eW.YI,{color:"#fff",className:"w-6 h-6"}),text:"Homepage",onMobile:!0}),(0,a.jsx)(j,{icon:v.createCharIcon,text:"Create Partner",onMobile:!0,redirectText:"Create Character"}),(0,a.jsx)(j,{icon:(0,a.jsx)(eX.Z,{}),text:"My Characters",onMobile:!0,redirectText:"My Characters"}),(0,a.jsx)(j,{icon:v.genImagesIcon,text:"Generate Images",onMobile:!0}),(0,a.jsx)(j,{icon:(0,a.jsx)(eJ.Z,{}),text:"Guide",onMobile:!0,redirectText:"Guide"})]})]}),(0,a.jsxs)("div",{className:"px-6 py-4 flex flex-col gap-2",children:[(0,a.jsx)("span",{className:"text-sm font-light",children:"RESPONSE STYLE"}),(0,a.jsx)(e4.Z,{user:u})]}),(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsx)("span",{className:"text-sm font-light",children:"RECENT MESSAGES"}),(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsx)("div",{className:"flex flex-col px-2 gap-1 custom-scrollbar",onClick:()=>i(!1),children:null==f?void 0:null===(t=f.msg)||void 0===t?void 0:t.slice(0,3).map(e=>(0,a.jsx)(e1.Z,{charId:e._id,lastMsg:e.last_message||"",name:"".concat(e.first_name," ").concat(e.last_name),portrait:e.initial_image||"",sideNav:!0,newText:e.newTextCount||0,convId:e.conv_id||""},e._id))}),(0,a.jsx)("div",{children:f&&(null==f?void 0:null===(s=f.msg)||void 0===s?void 0:s.length)>0?(0,a.jsxs)("div",{onClick:function(){var e,t,s,a;let l=localStorage.getItem("sd_active_char_id")||"";if(l&&(null==u?void 0:null===(e=u.conv_history)||void 0===e?void 0:e.some(e=>e.char_id===l)))localStorage.removeItem("character"),w(!0),g.push("/chat/".concat(l));else if((null==u?void 0:u.conv_history)&&(null==u?void 0:null===(t=u.conv_history)||void 0===t?void 0:t.length)>0){let e=null==u?void 0:null===(s=u.conv_history)||void 0===s?void 0:s.length;localStorage.removeItem("character"),w(!0),g.push("/chat/".concat(u.conv_history&&(null==u?void 0:null===(a=u.conv_history[e-1])||void 0===a?void 0:a.char_id)))}else N({redirectTo:"chat"});localStorage.removeItem("character"),i(!1)},className:"relative flex items-center mt-4",children:[(0,a.jsx)("div",{className:"flex -space-x-[25px]",children:null==f?void 0:null===(l=f.msg)||void 0===l?void 0:l.slice(-3).map(e=>{var t,s;return(0,a.jsxs)(F.qE,{className:"min-h-8 min-w-8",children:[(0,a.jsx)(F.F$,{src:e.initial_image||"",alt:"avatar",className:"h-8 w-8 object-cover object-top rounded-full border border-primary"}),(0,a.jsx)(F.Q5,{className:"flex items-center justify-center bg-background rounded-full text-xl h-8 w-8",children:null===(s=e.first_name)||void 0===s?void 0:null===(t=s.slice(0,1))||void 0===t?void 0:t.toUpperCase()})]},e._id)})}),(0,a.jsx)("div",{className:"shrink-0 text-primary ml-3",children:(0,a.jsx)("h3",{className:"",children:" See More Conversations"})})]}):(0,a.jsx)(j,{icon:(0,a.jsx)(e2.Z,{}),text:"Find Someone To Talk To",onMobile:!0,redirectText:"Homepage"})})]})]}),(0,a.jsxs)("div",{className:"px-6 py-4 text-[#9c9fa7]",children:[(0,a.jsx)("span",{className:"text-sm font-bold",children:"SETTINGS"}),(0,a.jsxs)("div",{className:"px-2 py-2",children:[(0,a.jsx)(j,{icon:(0,a.jsx)(e0.Z,{}),text:"Become an Affiliate",onMobile:!0,href:"https://affiliate.secretdesires.ai/"}),(0,a.jsx)(j,{icon:v.discord,text:"Discord",onMobile:!0,href:"https://discord.com/invite/CHp4uvKhPQ"}),_?(0,a.jsxs)(a.Fragment,{children:["                                ",(0,a.jsx)(j,{icon:v.setting,text:"Settings",onMobile:!0}),(0,a.jsx)(j,{icon:v.logout,text:"Logout",onMobile:!0})]}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(j,{icon:v.login,text:"Register/Login",onMobile:!0})})]})]})]})]})})})},e8=s(93191),e6=s(34084),e7=s(53130),e9=s(22468),te=s(28165);let tt=eh.fC,ts=eh.xz;eh.ZA,eh.Uv,eh.Tr,eh.Ee,c.forwardRef((e,t)=>{let{className:s,inset:l,children:i,...n}=e;return(0,a.jsxs)(eh.fF,{ref:t,className:(0,ea.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",s),...n,children:[i,(0,a.jsx)(G.Z,{className:"ml-auto h-4 w-4"})]})}).displayName=eh.fF.displayName,c.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(eh.tu,{ref:t,className:(0,ea.cn)("z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...l})}).displayName=eh.tu.displayName;let ta=c.forwardRef((e,t)=>{let{className:s,sideOffset:l=4,...i}=e;return(0,a.jsx)(eh.Uv,{children:(0,a.jsx)(eh.VY,{ref:t,sideOffset:l,className:(0,ea.cn)("z-50 min-w-32 overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})})});ta.displayName=eh.VY.displayName;let tl=c.forwardRef((e,t)=>{let{className:s,inset:l,...i}=e;return(0,a.jsx)(eh.ck,{ref:t,className:(0,ea.cn)("cursor-pointer relative flex select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:text-primary data-disabled:pointer-events-none data-disabled:opacity-50",l&&"pl-8",s),...i})});function ti(e){let{user:t}=e,{mutateAsync:s}=(0,e7.I)(),l=(0,y.P)(),i=async e=>{await s({updates:{mode:e,isCall:"Roleplay"!==e&&(null==t?void 0:t.isCall),isVoice:"Roleplay"!==e&&(null==t?void 0:t.isVoice),spontaneous:"Roleplay"!==e&&(null==t?void 0:t.spontaneous)}})};return(0,a.jsxs)(tt,{modal:!1,children:[(0,a.jsx)(ts,{className:"flex items-center gap-2 text-gray-200 hover:text-primary transition-colors w-[180px]",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[l.chatModeIcon,"Response Style",(0,a.jsx)("span",{className:"scale-75",children:l.caretDown})]})}),(0,a.jsxs)(ta,{className:"border-none w-[180px] mt-3",children:[(0,a.jsxs)(tl,{onClick:()=>i("Texting"),className:"flex items-center gap-2 hover:text-primary font-bold ".concat((null==t?void 0:t.mode)==="Texting"?"border border-primary rounded-md":""),children:[l.textingIcon,"Texting"]}),(0,a.jsxs)(tl,{onClick:()=>i("Roleplay"),className:"flex items-center gap-2 hover:text-primary font-bold ".concat((null==t?void 0:t.mode)==="Roleplay"?"border border-primary rounded-md":""),children:[l.rolePlayIcon,"Roleplay"]})]})]})}tl.displayName=eh.ck.displayName,c.forwardRef((e,t)=>{let{className:s,children:l,checked:i,...n}=e;return(0,a.jsxs)(eh.oC,{ref:t,className:(0,ea.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",s),checked:i,...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(eh.wU,{children:(0,a.jsx)(e9.Z,{className:"h-4 w-4"})})}),l]})}).displayName=eh.oC.displayName,c.forwardRef((e,t)=>{let{className:s,children:l,...i}=e;return(0,a.jsxs)(eh.Rk,{ref:t,className:(0,ea.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",s),...i,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(eh.wU,{children:(0,a.jsx)(te.Z,{className:"h-2 w-2 fill-current"})})}),l]})}).displayName=eh.Rk.displayName,c.forwardRef((e,t)=>{let{className:s,inset:l,...i}=e;return(0,a.jsx)(eh.__,{ref:t,className:(0,ea.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",s),...i})}).displayName=eh.__.displayName,c.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(eh.Z0,{ref:t,className:(0,ea.cn)("-mx-1 my-1 h-px bg-muted",s),...l})}).displayName=eh.Z0.displayName;let tn=()=>{let[e,t]=(0,d.useRecoilState)(u.CP);return{handleMarkAsFalsePositive:async()=>{200===(await I.j.post("/check_banned/mark-as-false-positive",{flaggedId:e})).status?n.default.success("Marked as false positive"):n.default.error("Failed to mark as false positive"),t("")}}};var tr=()=>{let[e,t]=(0,d.useRecoilState)(u.Md),[s,l]=(0,d.useRecoilState)(u.CP),{handleMarkAsFalsePositive:i}=tn();return(0,a.jsx)(Y.Vq,{open:(null==e?void 0:e.length)>0,onOpenChange:()=>t([]),children:(0,a.jsxs)(Y.cZ,{children:[(0,a.jsx)(Y.fK,{children:(0,a.jsx)(Y.$N,{children:"Content Notice"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{children:["Our automated system has flagged your recent message due to potential concerns about",(0,a.jsx)("span",{className:"text-red-500",children:"beastiality"===e[0]?" beastiality language ":(0,a.jsxs)(a.Fragment,{children:['\xa0"',e.join(", "),'"\xa0']})}),"This could violate our community guidelines."]}),(0,a.jsx)(Y.fK,{children:(0,a.jsx)(Y.$N,{children:"Please note"})}),(0,a.jsx)("p",{children:"Your account is NOT in trouble or at risk, this is simply a preventative measure, and our systems sometimes make mistakes in detecting policy violations."}),(0,a.jsx)("p",{children:"If you believe this is an error (which happens!), please let us know so we can improve our detection systems."}),(0,a.jsxs)("div",{className:"flex flex-col justify-end sm:flex-row gap-3",children:[(0,a.jsx)(S.z,{variant:"outline",onClick:i,children:"Mark as false positive"}),(0,a.jsx)(S.z,{onClick:()=>t([]),asChild:!0,children:(0,a.jsx)(N.default,{href:"mailto:<EMAIL>",children:"Send us your thoughts here"})})]})]})]})})},to=s(30560);function tc(){var e;let[t,s]=(0,d.useRecoilState)(ew.G),[l,i]=(0,d.useRecoilState)(u.s4),[o,x]=(0,d.useRecoilState)(u.LM),h=(0,d.useSetRecoilState)(u.kb),p=(0,y.P)(),{mutateAsync:f}=(0,e_.b)(),{data:g}=(0,m.j)(),v=(0,el.l)()(t?{customCharId:t}:{}),b=(null==v?void 0:v.first_name)+" "+(null==v?void 0:v.last_name)||"Your Character",j=(null==v?void 0:v._id)||"",{data:w,isLoading:_}=(0,ek.UB)({charId:j}),N=null==w?void 0:null===(e=w.msg)||void 0===e?void 0:e.filter(e=>{var t;return e.imageUrl&&!(null==g?void 0:null===(t=g.img_blurred)||void 0===t?void 0:t.includes(e._id))&&("initial_image"===e._id||e.imageUrl!==(null==v?void 0:v.initial_image))});(0,c.useEffect)(()=>{if((null==N?void 0:N.length)&&(null==N?void 0:N.length)<2){(0,n.default)("Please generate at least 1 image to share ".concat(null==v?void 0:v.first_name," ").concat(null==v?void 0:v.last_name,"."),{icon:"\uD83D\uDCF8"}),h(!1);return}0===l.length&&((null==v?void 0:v.images)&&v.images.length>0?i([{image:(null==v?void 0:v.initial_image)||"",video:""},...v.images.map(e=>({image:(null==e?void 0:e.image)||"",video:(null==e?void 0:e.video)||""}))]):(null==N?void 0:N.length)&&(null==N?void 0:N.length)>0&&i(N.slice(0,2).map(e=>({image:(null==e?void 0:e.imageUrl)||"",video:(null==e?void 0:e.videoUrl)||""}))))},[null==N?void 0:N.length]);let C=()=>{x(!0)},k=e=>{i(t=>t.filter((t,s)=>s!==e))},I=()=>{i([]),s(""),x(!1),h(!1)},E=async()=>{await f({updates:{community:!0,email:null==g?void 0:g.email,name:null==g?void 0:g.user_nickname,images:l},charId:j}),I()};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(Y.Vq,{open:!0,onOpenChange:I,modal:!0,children:(0,a.jsx)(Y.cZ,{className:"sm:max-w-[425px]",closeBtn:"hidden",children:o?(0,a.jsx)(eU,{images:N}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(Y.fK,{children:[(0,a.jsxs)(Y.$N,{className:"text-xl",children:["Are you sure you want to share"," ",(0,a.jsx)("span",{className:"text-primary",children:b}),"?"]}),(0,a.jsxs)(Y.Be,{className:"space-y-4 pt-4",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Sharing this partner will make them available on the homepage for everyone to use. This action is irreversible."}),(0,a.jsx)("p",{className:"text-gray-400",children:"When you share a character, our team reviews it within 72 hours to ensure it meets quality standards and complies with prohibited content rules."}),(0,a.jsx)("p",{className:"text-gray-400",children:"Please select 2-5 images for your character's profile."}),(0,a.jsx)("div",{className:"overflow-x-auto sm:max-w-[380px] custom-scrollbar",children:(0,a.jsxs)("div",{className:"flex gap-3 w-full pb-2",children:[_?(0,a.jsx)(eH,{}):l.map((e,t)=>(0,a.jsxs)("div",{className:"relative border border-primary rounded-lg mb-2",children:[(0,a.jsxs)("div",{className:"relative cursor-pointer rounded-lg overflow-hidden w-max",onClick:C,children:[e.video&&(0,a.jsx)("div",{className:"scale-75 text-white absolute top-0.5 right-0.5 bg-black/50 rounded-full p-1 z-10",children:p.livePhotoIcon}),(0,a.jsx)(r.default,{src:e.image,alt:"Character image ".concat(t+1),className:"object-cover",unoptimized:!0,width:70,height:70}),0===t&&(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-primary/70 font-bold text-white text-xs py-1 px-2 text-center",children:"Profile Pic"})]}),(0,a.jsx)("button",{onClick:()=>k(t),className:"absolute -bottom-1.5 -right-1.5 border border-primary bg-background z-10 w-5 h-5 rounded-full flex items-center justify-center",children:(0,a.jsx)(L.Z,{className:"w-3 h-3 text-primary"})})]},t)),!_&&l.length<5&&(0,a.jsxs)("div",{className:"min-w-[70px] h-[100px] border-2 border-dashed border-primary text-primary text-sm text-center rounded-lg flex items-center justify-center cursor-pointer font-bold",onClick:C,children:["Add",(0,a.jsx)("br",{}),"Image"]})]})})]})]}),(0,a.jsxs)("div",{className:"flex justify-between gap-2",children:[(0,a.jsx)(S.z,{onClick:I,variant:"noFill",children:"Cancel"}),(0,a.jsxs)(S.z,{onClick:E,disabled:l.length<2,className:"disabled:opacity-50",children:[(0,a.jsx)(to.Z,{className:"w-4 h-4 mr-2"}),"Confirm Share"]})]})]})})})})}var td=()=>{var e,t,s,l,i,n,h,p,f;let g=(0,y.P)(),v=(0,o.useRouter)(),{openModal:b,modalState:N}=(0,_.d)(),C=(0,o.useSearchParams)(),k=(0,o.usePathname)(),E=null==k?void 0:k.split("/")[1],R=(0,d.useRecoilValue)(ew.G),A=(0,d.useRecoilValue)(eK.m),z=(0,d.useRecoilValue)(x.loggedIn),[L,T]=(0,d.useRecoilState)(u.dG),Z=(0,d.useSetRecoilState)(u.PC),{data:F,refetch:G}=(0,m.j)(),U=(0,el.l)()({}),H=(0,e8.useQueryClient)(),{mutateAsync:O}=(0,e7.I)(),V=(0,d.useSetRecoilState)(D),{data:B}=(0,q.v)({}),Q=(0,d.useRecoilValue)(u._A),Y=(0,M.L)(),$=(0,d.useRecoilValue)(u.kb),J=(0,d.useSetRecoilState)(u.wi),ei=(0,d.useRecoilValue)(u.wi),en=async e=>{try{var t;if(!e)return;let s=document.referrer,a=(null==C?void 0:C.get("rl"))||"",l=await es.default.post("".concat(ea.fw,"/user/check_visitor"),{identifier:e,referrer:s,referral:a,source:(null==Y?void 0:Y.source)||"direct",tracking_id:(null==Y?void 0:Y.tracking_id)||""}),i=null==l?void 0:null===(t=l.data)||void 0===t?void 0:t.token;i&&(localStorage.setItem("visitor_token",i),G())}catch(e){}};async function er(){let e=localStorage.getItem("timezone"),t=Intl.DateTimeFormat().resolvedOptions().timeZone;if(e!==t){let e=await O({updates:{timezone:t}});(null==e?void 0:e.status)===200&&localStorage.setItem("timezone",t)}}async function eo(){try{await I.j.post("/chat/spontaneous_messaging",{userid:null==F?void 0:F._id}),H.invalidateQueries({queryKey:["my-chars"]})}catch(e){}}async function ec(e){let t=Math.floor(new Date().getTime()/1e3);try{await I.j.post("/user/ga_session_start",{id:t,campaign_name:e})}catch(e){}}(0,c.useEffect)(()=>{!1===z&&Y&&en((0,et.L)())},[z]),(0,c.useEffect)(()=>{var e,t,s,a,l;(null==F?void 0:null===(e=F.marketing_data)||void 0===e?void 0:e.source)&&(null==F?void 0:null===(s=F.marketing_data)||void 0===s?void 0:null===(t=s.source)||void 0===t?void 0:t.length)>2&&(localStorage.setItem("marketing_source",null==F?void 0:null===(a=F.marketing_data)||void 0===a?void 0:a.source),localStorage.setItem("tracking_id",(null==F?void 0:null===(l=F.marketing_data)||void 0===l?void 0:l.tracking_id)||"")),!0===z&&F&&(er(),(null==F?void 0:F.spontaneous)&&(null==F?void 0:F.heart_balance)&&(null==F?void 0:F.heart_balance)>2&&function(){let e=localStorage.getItem("lastOnline");if(navigator.onLine){let t=Date.now();e?t-parseInt(e,10)>48e4&&(localStorage.setItem("lastOnline",t.toString()),eo()):(localStorage.setItem("lastOnline",t.toString()),eo())}}())},[z,F]),(0,c.useEffect)(()=>{let e=null==C?void 0:C.get("utm_campaign"),t=null==C?void 0:C.get("aclid");e&&(localStorage.setItem("campaign",e),ec(e)),t&&localStorage.setItem("aclid",t)},[]);let ed=(0,d.useSetRecoilState)(eN.cf),eu=(0,d.useSetRecoilState)(eN.ZQ),em=(0,d.useSetRecoilState)(eN.Kf),ex=(0,d.useSetRecoilState)(eN._0),eh=(0,d.useSetRecoilState)(eN.qY),ep=(null==F?void 0:F.subscription_tier)!==""&&(null==F?void 0:F.subscription_tier)!=="Free"&&(null==F?void 0:F.subscription_tier)!=="Visitor";(0,c.useEffect)(()=>{ed(null),eu(0),em(!1),ex(""),eh(!1)},[k]);let ef=(0,d.useSetRecoilState)(u.$d),eg=(0,d.useRecoilValue)(u.$d),ev=(0,d.useSetRecoilState)(u.PC),eb=(0,d.useSetRecoilState)(u.Qg),{data:ej}=(0,e5.e)(),ey=(0,d.useSetRecoilState)(e6.AL),e_=async()=>{try{if(!z){ev(()=>({open:!0,isSignup:!0,text:"Sign up to call ".concat(null==U?void 0:U.first_name,"!"),image:"".concat(null==U?void 0:U.initial_image),gaClass:"ga-reg_popup_calling"}));return}let s=await es.default.post("".concat(ea.fw,"/check/check_call"),{},{headers:{Authorization:localStorage.getItem("access_token")||""}});if(0==s.data.code){eb({open:!0,text:"Upgrade to Call ".concat(null==U?void 0:U.first_name,"!"),image:"".concat(null==U?void 0:U.initial_image),type:"subscription"});return}if(2==s.data.code){eb({open:!0,text:"Grab Hearts to call ".concat(null==U?void 0:U.first_name,"!"),image:"".concat(null==U?void 0:U.initial_image),type:"hearts"});return}if(U){var e,t;ey(!1),ef({open:!0,receivingCall:!1,userCalling:!0,text:null!==(e=null==U?void 0:U.first_name)&&void 0!==e?e:"",image:null!==(t=null==U?void 0:U.initial_image)&&void 0!==t?t:""})}}catch(e){}},eS=(0,d.useSetRecoilState)(eK.m),ek=()=>{!0===A?(v.push("/"),eS(!1)):eS(!0),J(!1)},eI=()=>{eS(!1),J(e=>!e)},eE=()=>{let e=null==k?void 0:k.match(/\/chat\/([^/]+)/);if(!e)return null;let t=e[1].split("-");return{firstName:t[0],lastName:t.slice(1).join("-")}},eR=()=>{let e=eE();return!!e&&null!=U&&!!U.first_name&&"".concat(e.firstName).concat(e.lastName?"-".concat(e.lastName):"").toLowerCase()==="".concat(U.first_name).concat(U.last_name?"-".concat(U.last_name):"").toLowerCase().replace(/[^a-z0-9-]/g,"-").replace(/-+/g,"-")};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(tr,{}),$&&(0,a.jsx)(tc,{}),(0,a.jsxs)("nav",{className:"fixed z-50 w-full flex p-3 md:justify-between items-center justify-start h-16 bg-card",children:[(0,a.jsx)(X,{}),eg.open&&(0,a.jsx)(eQ.Z,{}),(0,a.jsx)(W,{}),R&&(0,a.jsx)(eC,{},R),Q.open&&(0,a.jsx)(eq,{charId:(null==U?void 0:U._id)||""}),(0,a.jsx)("div",{role:"link","aria-label":"Homepage",onClick:()=>{let e=C.toString(),t=e?"/?".concat(e):"/";"/"===k||""===k?window.location.href=t:v.push(t)},className:"hidden cursor-pointer md:flex items-center mt-1",children:(0,a.jsx)(r.default,{src:(null==ej?void 0:ej.logo.visible)?null==ej?void 0:ej.logo.desk_url:"/logo.svg",alt:"Secret Desires",width:170,height:50,unoptimized:!0})}),"chat"===E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(S.z,{"aria-label":"Open All Recent Chats Sidebar",title:"Open All Chats",variant:"ghost",onClick:()=>ek(),className:"flex md:hidden hover:bg-inherit items-center hover:text-primary -ml-0 p-0",children:g.angleLeft}),(0,a.jsxs)(S.z,{"aria-label":"Open Character Profile Sidebar",title:"Open Profile",variant:"ghost",onClick:()=>eI(),className:"absolute flex flex-col left-1/2 transform -translate-x-1/2 md:hidden justify-center items-center mt-1 px-0 hover:bg-inherit group",children:[(0,a.jsxs)(ee.qE,{className:"w-9 h-9",children:[(0,a.jsx)(ee.F$,{src:eR()?null==U?void 0:U.initial_image:void 0,alt:eR()?null==U?void 0:U.first_name:null===(e=eE())||void 0===e?void 0:e.firstName}),(0,a.jsx)(ee.Q5,{children:eR()?null==U?void 0:null===(s=U.first_name)||void 0===s?void 0:null===(t=s.slice(0,1))||void 0===t?void 0:t.toUpperCase():(null===(n=eE())||void 0===n?void 0:null===(i=n.firstName)||void 0===i?void 0:null===(l=i.slice(0,1))||void 0===l?void 0:l.toUpperCase())||"?"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-center pl-2 ",children:[(0,a.jsxs)("span",{className:"tracking-wide",children:[null===(h=eE())||void 0===h?void 0:h.firstName," "]}),(0,a.jsx)(r.default,{src:"/icons/greater-than-symbol.svg",alt:"Go to Profile Icon",width:"15",height:"15",className:"-ml-0.5 -mb-[0.5px] font-bold"})]})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{onClick:()=>v.push("/"),className:"md:hidden cursor-pointer flex items-center order-2 sm:order-1",children:(0,a.jsx)(r.default,{src:(null==ej?void 0:ej.logo.visible)?null==ej?void 0:ej.logo.mobile_url:"/icons/mobile-logo.svg",alt:"Secret Desires",width:30,height:30,unoptimized:!0})}),(0,a.jsxs)("div",{className:"absolute right-4 flex items-center gap-2",children:[(0,a.jsx)("div",{className:"block md:hidden order-3",children:(0,a.jsx)(K,{})}),z?(0,a.jsx)(S.z,{className:"order-5 md:hidden h-8",onClick:()=>v.push("/subscription"),children:ep?"My Subscription":"Subscribe 75% OFF"}):(0,a.jsxs)("div",{className:" order-4 flex gap-2 items-center",children:[(0,a.jsx)(S.z,{"aria-label":"Login",variant:"noFill",size:"default",onClick:()=>Z(e=>({...e,open:!0,isSignup:!1})),className:"rounded-lg font-bold text-nowrap text-base md:hidden",children:"Login"}),(0,a.jsx)(S.z,{"aria-label":"Register(Sign up)",variant:"default",size:"default",onClick:()=>Z(e=>({...e,open:!0,isSignup:!0,gaClass:"ga-reg_popup_navigation_menu"})),className:"rounded-lg font-bold text-nowrap text-base md:hidden",children:"Register"})]})]})]}),(0,a.jsxs)("div",{className:"gap-6 hidden lg:flex text-white",children:[(0,a.jsx)(j,{icon:g.chatIcon,text:"Chat",onMobile:!1}),(0,a.jsx)(j,{icon:g.genImagesIcon,text:"Generate Images",onMobile:!1}),(0,a.jsx)(j,{icon:g.myCharIcon,text:"My Characters",onMobile:!1}),(0,a.jsx)("span",{className:"hidden xl3:block",children:(0,a.jsx)(j,{icon:g.book,text:"Guide",onMobile:!1})}),(0,a.jsx)("span",{className:"hidden 2xl:block",children:(0,a.jsx)(j,{icon:g.gift,text:ep?"My Subscription":"Subscribe",onMobile:!1})}),(0,a.jsx)(ti,{user:F}),(0,a.jsx)(w.default,{icon:g.createCharIcon,text:"Create Character"})]}),"chat"===E&&U&&!ei&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"flex items-center md:hidden absolute right-4 xs:right-4 sm:right-4",children:[(0,a.jsx)("div",{className:"",children:(0,a.jsx)(K,{})}),(0,a.jsx)(S.z,{"aria-label":"Call ".concat(null==U?void 0:U.first_name),title:"Call ".concat(null==U?void 0:U.first_name),variant:"ghost",className:"top-3 xs:top-3 text-primary-foreground hover:bg-inherit hover:text-primary",onClick:()=>{e_()},children:(0,a.jsx)("span",{className:"scale-[.8]",children:g.call})})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-3 relative",children:[(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)(K,{})}),N.MyProfileModal&&(0,a.jsx)(P,{}),z?(0,a.jsxs)("button",{"aria-label":"Open My Profile Modal",onClick:()=>{b("MyProfileModal"),V(null)},className:" text-white hidden xl:flex gap-2 items-center transition-colors hover:text-primary ".concat(N.MyProfileModal?"text-primary":""),children:[(0,a.jsxs)(ee.qE,{className:"h-8 w-8",children:[(0,a.jsx)(ee.F$,{src:null==F?void 0:F.profile_picture,alt:"avatar"}),(0,a.jsx)(ee.Q5,{children:null==F?void 0:null===(f=F.email)||void 0===f?void 0:null===(p=f.slice(0,1))||void 0===p?void 0:p.toUpperCase()})]}),(0,a.jsxs)("span",{className:"flex flex-col items-start",children:[(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[null==F?void 0:F.user_nickname,(0,a.jsx)(eY.C,{className:"duration-0",children:(null==F?void 0:F.subscription_tier)||"Free"})]}),(0,a.jsx)("span",{className:"text-xs text-[#676767]",children:null==F?void 0:F.email})]}),(0,a.jsx)("span",{className:"scale-75",children:g.caretDown})]}):(0,a.jsxs)("div",{className:"xl:flex hidden text-base space-x-2",children:[(0,a.jsx)(S.z,{"aria-label":"Login",variant:"noFill",size:"default",onClick:()=>Z(e=>({...e,open:!0,isSignup:!1})),className:"rounded-lg font-bold text-nowrap text-base",children:"Login"}),(0,a.jsx)(S.z,{"aria-label":"Register(Sign up)",variant:"default",size:"default",onClick:()=>Z(e=>({...e,open:!0,isSignup:!0,gaClass:"ga-reg_popup_navigation_menu"})),className:"rounded-lg font-bold text-nowrap text-base",children:"Register"}),(0,a.jsx)("button",{"aria-label":"Toggle Mobile Navbar Menu",onClick:()=>T(!0),className:"flex xl3:hidden mr-2 justify-center items-center order-1 sm:order-10",children:g.bars})]}),L&&(0,a.jsx)(e3,{mobMenu:!0,setMobMenu:T,isSubscribed:ep}),(0,a.jsx)("button",{"aria-label":"Toggle Mobile Navbar Menu",onClick:()=>T(!0),className:"xl:hidden flex mr-2 justify-center items-center order-1 sm:order-10",children:g.bars})]})]})]})},tu=()=>{let{closeModal:e,activeModal:t,modalState:s}=(0,_.d)(),a=s=>{s.target.closest(".dontCloseModal")||e(t)};return(0,c.useEffect)(()=>(document.addEventListener("click",a),()=>{document.removeEventListener("click",a)}),[s]),null},tm=s(61349),tx=s(34999);let th={REGISTER:"register",SEND_TO_USER:"send_to_user",IMAGE_GENERATE:"image:generate",IMAGE_REGENERATE:"image:regenerate",IMAGE_PROGRESS:"image:progress",IMAGE_RESULT:"image:result",CHAT_MESSAGE:"chat:message",CHAT_IMAGE_GENERATE:"chat:image:generate",CHAT_IMAGE_REGENERATE:"chat:image:regenerate",CHAT_IMAGE_PROGRESS:"chat:image:progress",CHAT_IMAGE_RESULT:"chat:image:result"};class tp{setupEventListeners(){this.socket.on("connect",this.handleConnect.bind(this)),this.socket.on("disconnect",this.handleDisconnect.bind(this)),this.socket.on("connect_error",this.handleConnectError.bind(this)),this.socket.on("reconnect_attempt",this.handleReconnectAttempt.bind(this)),this.socket.on("reconnect_failed",this.handleReconnectFailed.bind(this)),this.socket.on("pong_with_time",e=>{let{clientTime:t,serverTime:s}=e,a=Date.now()-t;console.log("PING_PONG: Latency -> ".concat(a,"ms (serverTime: ").concat(s,")"))})}handleConnect(){console.log("Socket connected successfully. ID:",this.socket.id),this.connectionState.isConnected=!0,this.connectionState.isConnecting=!1,this.connectionState.reconnectAttempts=0,this.connectionState.lastConnectTime=Date.now(),this.register(this.userId),this.flushMessageQueue(),this.restoreEventListeners(),this.startPing(),this.connectionCallbacks.forEach(e=>e())}handleDisconnect(e){console.log("Socket disconnected. Reason:",e),this.connectionState.isConnected=!1,this.connectionState.isConnecting=!1,this.connectionState.lastDisconnectTime=Date.now(),this.stopPing(),this.disconnectCallbacks.forEach(e=>e())}handleConnectError(e){console.error("Socket connection error:",e),this.connectionState.isConnecting=!1}handleReconnectAttempt(e){console.log("Reconnection attempt ".concat(e)),this.connectionState.reconnectAttempts=e}handleReconnectFailed(){console.error("Socket reconnection failed after all attempts"),this.connectionState.isConnecting=!1}restoreEventListeners(){this.eventListeners.forEach((e,t)=>{this.socket.off(t),e.forEach(e=>{this.socket.on(t,e)})})}flushMessageQueue(){for(;this.messageQueue.length>0;){let e=this.messageQueue.shift();e&&this.socket.emit(e.event,e.payload)}}get connected(){return this.connectionState.isConnected}get connecting(){return this.connectionState.isConnecting}get connectionInfo(){return{...this.connectionState}}get ID(){return this.connected?this.socket.id:null}startPing(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e4;this.pingInterval||(console.log("starting ping"),this.pingInterval=setInterval(()=>{this.socket.connected&&(this.socket.emit("ping_with_time",Date.now()),console.log("PING_PONG: ping sent"))},e))}stopPing(){this.pingInterval&&(console.log("stopping ping"),clearInterval(this.pingInterval),this.pingInterval=null)}connect(){console.log("connecting socket"),this.connectionState.isConnected||(this.connectionState.isConnecting=!0,console.log("Initiating socket connection..."),this.socket.connect())}disconnect(){console.log("Manually disconnecting socket..."),this.connectionState.isConnecting=!1,this.connectionState.reconnectAttempts=0,this.socket.disconnect()}reconnect(){console.log("Manually triggering reconnection..."),this.disconnect(),setTimeout(()=>this.connect(),1e3)}onConnect(e){console.log("onConnect"),this.connectionCallbacks.push(e),this.connected&&e(),this.startPing()}onDisconnect(e){this.disconnectCallbacks.push(e),this.stopPing()}register(e){this.connected?this.socket.emit(th.REGISTER,e):console.warn("Cannot register user: socket not connected")}sendToUser(e,t,s){this.connected?this.socket.emit(th.SEND_TO_USER,{to:e,event:t,payload:s}):(console.warn("Cannot send message: socket not connected"),this.queueMessage(th.SEND_TO_USER,{to:e,event:t,payload:s}))}queueMessage(e,t){this.messageQueue.push({event:e,payload:t})}on(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(t),this.socket.on(e,t)}off(e){this.eventListeners.delete(e),this.socket.off(e)}emit(e,t){this.connected?this.socket.emit(e,t):(console.warn("Cannot emit event: socket not connected"),this.queueMessage(e,t))}activeListeners(e){return this.socket.listeners(e)}destroy(){this.disconnect(),this.eventListeners.clear(),this.connectionCallbacks=[],this.disconnectCallbacks=[],this.messageQueue=[]}constructor(e,t){this.url=e,this.userId=t,this.eventListeners=new Map,this.connectionCallbacks=[],this.disconnectCallbacks=[],this.messageQueue=[],this.pingInterval=null,this.connectionState={isConnected:!1,isConnecting:!1,reconnectAttempts:0,lastConnectTime:null,lastDisconnectTime:null},this.socket=(0,tx.io)(this.url,{transports:["websocket"],autoConnect:!1,reconnection:!0,reconnectionAttempts:1/0,reconnectionDelay:1e3,reconnectionDelayMax:5e3,timeout:2e4,auth:{userId:this.userId}}),this.setupEventListeners()}}var tf=s(28590);let tg=new tp("https://socket-microservice.onrender.com/",localStorage.getItem("access_token")||(0,tf.Z)()),tv=e=>({_id:e.run_id,created_date:new Date,imageUrl:e.src||"",videoUrl:"video"===e.type?e.src:"",imageType:"generated",image_prompt:e.prompt||"",live_photo:"video"===e.type,current_step:e.progress||0,step_name:"runid_saved",...e});var tb=s(41829);let tj=e=>({_id:e._id,message_sender_type:e.message_sender_type,message_type:e.message_type,message_content:e.message_content,voice_content:"",image:"",image_prompt:"",rating:"",step_name:"",current_step:0,live_photo:!1,video:"",created_date:new Date,...e}),ty=(0,c.createContext)({socket:null,connected:!1,bindListener:()=>{}}),tw=e=>{let{children:t}=e,{socket:s,connected:l}=function(){let[e,t]=(0,c.useState)(null),[s,a]=(0,c.useState)(!1),{data:l}=(0,m.j)();return(0,c.useEffect)(()=>(null==l?void 0:l._id)?(console.log("\uD83D\uDD0C Connecting to socket"),tg.connected||tg.connect(),console.log("\uD83D\uDD0C Registering user",null==l?void 0:l._id),tg.onConnect(()=>{tg.register(l._id),tg.connected&&t(tg.ID),a(!0),console.log("\uD83D\uDD0C Connected as ".concat(l._id," (socket: ").concat(tg.ID,")"))}),tg.onDisconnect(()=>{a(!1),t(null),console.log("❌ Disconnected socket"),tg.reconnect()}),tg.reconnect(),()=>{tg.off("connect"),tg.off("disconnect")}):void 0,[null==l?void 0:l._id]),{socket:tg,socketId:e,connected:s}}(),{handleImageGenerateSocketEvent:i}=function(){let e=(0,el.l)()({}),[t,s]=(0,c.useState)({}),a=(0,c.useRef)(null),l=e=>{s(t=>{let s={...t};if(Array.isArray(e))for(let t of e)delete s[t];else delete s[e];return s})};return(0,c.useEffect)(()=>{if(a.current){let e=[];Object.entries(t).forEach(t=>{let[s,l]=t,i={_id:s,status:"pending",progress:l.progress||l.current_step||0};Object.entries(l).forEach(e=>{let[t,s]=e;s&&(i[t]=s)}),console.log({run_id:i.run_id,toBeSavedInGenImagesCache:i}),(0,ek.nO)({charId:i.char_id||a.current,data:i}),("generated"===i.status||"success"===i.status)&&e.push(i.run_id)}),e.length>0&&l(e)}},[t,a.current]),(0,c.useEffect)(()=>{(null==e?void 0:e._id)&&a.current!==(null==e?void 0:e._id)&&(a.current=null==e?void 0:e._id)},[null==e?void 0:e._id]),{handleImageGenerateSocketEvent:(e,t)=>{let{run_id:a}=t.data,l={};l=e===th.IMAGE_GENERATE?tv({...t.data}):t.data,s(e=>{let t={...e},s={...e[a]||{},...l};return t[a]={...s},t})}}}(),{handleChatSocketEvent:n}=function(){let e=(0,el.l)()({}),[t,s]=(0,c.useState)({}),a=(0,c.useRef)(null),l=e=>{s(t=>{let s={...t};if(Array.isArray(e))for(let t of e)delete s[t];else delete s[e];return s})};return(0,c.useEffect)(()=>{if(a.current){let e=[];Object.entries(t).forEach(t=>{let[s,l]=t,i={image_gen_status:"pending",run_id:s};Object.entries(l).forEach(e=>{let[t,s]=e;s&&(i[t]=s)}),console.log({toBeSavedInMessageHistory:i}),setTimeout(()=>{(0,tb.p)({charId:i.char_id||a.current,data:i})},2e3),(0,ek.nO)({charId:i.char_id||a.current,data:{...i,imageUrl:i.image||i.imageUrl}}),("completed"===i.image_gen_status||"failed"===i.image_gen_status)&&e.push(i.run_id)}),e.length>0&&l(e)}},[t,a.current]),(0,c.useEffect)(()=>{(null==e?void 0:e._id)&&a.current!==(null==e?void 0:e._id)&&(a.current=null==e?void 0:e._id)},[null==e?void 0:e._id]),{handleChatSocketEvent:(e,t)=>{let{run_id:a}=t.data;console.log({data:t.data});let l={};l=e===th.CHAT_IMAGE_GENERATE?tj({...t.data}):t.data,s(e=>{let t={...e},s={...e[a]||{},...l};return t[a]={...s},t})}}}(),r=(0,c.useRef)({}),o=e=>t=>{"image"===e.split(":")[0]&&i(e,t),"chat"===e.split(":")[0]&&n(e,t),e in r.current&&r.current[e](t)};return(0,c.useEffect)(()=>{if(s&&l)return s.on(th.IMAGE_GENERATE,o(th.IMAGE_GENERATE)),s.on(th.IMAGE_REGENERATE,o(th.IMAGE_REGENERATE)),s.on(th.IMAGE_PROGRESS,o(th.IMAGE_PROGRESS)),s.on(th.IMAGE_RESULT,o(th.IMAGE_RESULT)),s.on(th.CHAT_MESSAGE,o(th.CHAT_MESSAGE)),s.on(th.CHAT_IMAGE_GENERATE,o(th.CHAT_IMAGE_GENERATE)),s.on(th.CHAT_IMAGE_REGENERATE,o(th.CHAT_IMAGE_REGENERATE)),s.on(th.CHAT_IMAGE_PROGRESS,o(th.CHAT_IMAGE_PROGRESS)),s.on(th.CHAT_IMAGE_RESULT,o(th.CHAT_IMAGE_RESULT)),()=>{s.off(th.IMAGE_GENERATE),s.off(th.IMAGE_REGENERATE),s.off(th.IMAGE_PROGRESS),s.off(th.IMAGE_RESULT),s.off(th.CHAT_MESSAGE),s.off(th.CHAT_IMAGE_GENERATE),s.off(th.CHAT_IMAGE_REGENERATE),s.off(th.CHAT_IMAGE_PROGRESS),s.off(th.CHAT_IMAGE_RESULT)}},[s,l]),(0,a.jsx)(ty.Provider,{value:{socket:s,connected:l,bindListener:(e,t)=>{console.log("Listener Bound",e,t),r.current[e]=t}},children:t})},t_=(0,c.createContext)({click:!1,setClick:()=>{}});function tN(e){let{children:t}=e,[s,l]=(0,c.useState)(!1);return(0,a.jsx)(t_.Provider,{value:{click:s,setClick:l},children:t})}var tC=s(27071),tS=s(82912);function tk(){let e=(0,y.P)(),t=(0,o.usePathname)(),s=null==t?void 0:t.split("/")[1],l=(0,d.useRecoilValue)(x.loggedIn),i=(0,d.useSetRecoilState)(u.PC),n=(0,o.useRouter)(),[m,h]=(0,c.useState)(!1);return((0,c.useEffect)(()=>{l&&m&&(n.replace("/subscription"),h(!1))},[l]),"chat"===s||"generator"===s||"create"===s||"guide"===s)?null:(0,a.jsx)("footer",{className:"bg-linear-to-t pt-10 md:pt-12 from-primary to-black text-primary-foreground",children:(0,a.jsxs)("div",{className:"container px-4 py-8 mb-10 md:pb-12 lg:flex lg:justify-between lg:items-start",children:[(0,a.jsxs)("div",{className:"lg:w-1/4",children:[(0,a.jsx)(N.default,{"aria-label":"Go to Homepage",href:"/",children:(0,a.jsx)(r.default,{src:"/logo.svg",alt:"Secret Desires",width:170,height:200,unoptimized:!0,className:"mb-4"})}),(0,a.jsx)("p",{className:"mb-4 text-xs",children:"Secret Desires creates immersive experiences that blur the line between simulation and reality."}),(0,a.jsxs)("p",{className:"text-xs",children:["Secret Desires AI \xa9secretdesires.ai All Rights Reserved.",(0,a.jsx)("br",{}),"<EMAIL>",(0,a.jsx)("br",{}),"Secret Desires AI is a Playhouse Media LLC company.",(0,a.jsx)("br",{}),"Playhouse Media LLC",(0,a.jsx)("br",{}),"8 The Green STE A",(0,a.jsx)("br",{}),"Dover, DE, 19901, United States"]})]}),(0,a.jsxs)("div",{className:"lg:w-1/4 mt-8 lg:mt-0",children:[(0,a.jsx)("h3",{className:"font-semibold mb-4 text-xl",children:"Browse & Create"}),(0,a.jsxs)("ul",{className:"flex flex-col space-y-4 mt-6",children:[(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"My Characters",className:"hover:underline mb-2 flex space-x-2",href:"/characters",children:[(0,a.jsx)("span",{children:e.myCharIcon}),(0,a.jsx)("span",{children:"My Characters"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Create Character",className:"hover:underline mb-2 flex space-x-2",href:"/create",children:[(0,a.jsx)("span",{children:e.createCharIcon}),(0,a.jsx)("span",{children:"Create Character"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Our Partners",className:"hover:underline mb-2 flex space-x-2",href:"/our-partners",children:[(0,a.jsx)("span",{children:e.partners}),(0,a.jsx)("span",{children:"Our Partners"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Become an Affiliate",className:"hover:underline mb-2 flex space-x-2",target:"_blank",href:"https://affiliate.secretdesires.ai/",children:[(0,a.jsx)("span",{children:e.dollar}),(0,a.jsx)("span",{children:"Become an Affiliate"})]})})]})]}),(0,a.jsxs)("div",{className:"mb-20 sm:mb-0 lg:w-1/4 mt-8 lg:mt-0",children:[(0,a.jsx)("h3",{className:"font-semibold mb-4 text-xl",children:"Info"}),(0,a.jsxs)("ul",{className:"flex space-x-6 my-6",children:[(0,a.jsx)("li",{className:"scale-105",children:(0,a.jsx)(N.default,{"aria-label":"Twitter(X)",target:"_blank",href:"https://x.com/SecretDesiresAi",className:"transition-colors hover:text-black",children:e.x})}),(0,a.jsx)("li",{className:"scale-150",children:(0,a.jsx)(N.default,{"aria-label":"Discord",target:"_blank",href:"https://discord.com/invite/CHp4uvKhPQ",className:"transition-colors hover:text-[#5865F2]",children:e.discord})}),(0,a.jsx)("li",{className:"scale-150",children:(0,a.jsx)(N.default,{"aria-label":"Reddit",target:"_blank",href:"https://www.reddit.com/r/secret_desires_ai",className:"transition-colors hover:text-[#5865F2]",children:e.reddit})})]}),(0,a.jsxs)("ul",{className:"flex flex-col space-y-4",children:[(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{className:"hover:underline mb-2 flex items-center space-x-2",href:"/subscription",onClick:e=>{e.preventDefault(),l?n.replace("/subscription"):(h(!0),i(e=>({...e,open:!0,isSignup:!0,text:"Sign up to Continue",gaClass:"ga-reg_popup_access_subscription"})))},children:[(0,a.jsx)("span",{children:e.gift}),(0,a.jsx)("span",{children:"Premium Subscription"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Privacy Policy",className:"hover:underline mb-2 flex items-center space-x-2",href:"/privacy",children:[(0,a.jsx)("span",{children:e.file}),(0,a.jsx)("span",{children:"Privacy Policy"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Complaints Policy",className:"hover:underline mb-2 flex items-center space-x-2",href:"/complaints-policy",children:[(0,a.jsx)("span",{children:e.file}),(0,a.jsx)("span",{children:"Complaints Policy"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Complaints Policy",className:"hover:underline mb-2 flex items-center space-x-2",href:"/complaints-policy#content-removal",children:[(0,a.jsx)("span",{children:e.file}),(0,a.jsx)("span",{children:"Content Removal Policy"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Terms of Service",className:"hover:underline mb-2 flex items-center space-x-2",href:"/tos",children:[(0,a.jsx)("span",{children:e.file}),(0,a.jsx)("span",{children:"Terms of Service"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Report a Bug",className:"hover:underline mb-2 flex items-center space-x-2",target:"_blank",href:"https://forms.gle/ikpieJcvopkmF8xM6",children:[(0,a.jsx)("span",{children:e.bug}),(0,a.jsx)("span",{children:"Report a Bug"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Submit a Complaint",className:"hover:underline mb-2 flex items-center space-x-2",href:"mailto:<EMAIL>?subject=Complaint%20Submission&body=Dear%20Secret%20Desires%20Support%2C%0A%0AI%20am%20writing%20to%20complain%20about%3A%0A%0A-%20Issue%20Description%3A%0A%0A-%20Date%20and%20Time%20of%20Incident%3A%0A%0A-%20Additional%20Details%3A%0A%0A",children:[(0,a.jsx)("span",{children:e.complaint}),(0,a.jsx)("span",{children:"Submit a Complaint"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"Content Removal Request",className:"hover:underline mb-2 flex items-center space-x-2",href:"mailto:<EMAIL>?subject=Content%20Removal%20Request&body=Dear%20Secret%20Desires%20Support%2C%20%0A%0AHere%20are%20the%20details%20of%20the%20content%2C%20I%20would%20like%20to%20be%20removed%3A%0A%0A-%20URL%20or%20location%20of%20content%3A%0A-%20Reason%20for%20removal%20request%3A%0A-%20My%20relationship%20to%20the%20content%3A%0A",children:[(0,a.jsx)("span",{children:e.content_removal}),(0,a.jsx)("span",{children:"Content Removal Request"})]})}),(0,a.jsx)("li",{className:"",children:(0,a.jsxs)(N.default,{"aria-label":"18 U.S.C. 2257 Exemption",className:"hover:underline mb-2 flex items-center space-x-2",href:"/usc-2257-exemption",children:[(0,a.jsx)("span",{children:e.file}),(0,a.jsx)("span",{children:"18 U.S.C. 2257 Exemption"})]})})]})]})]})})}var tI=s(19024);let tE=(0,c.createContext)({allPublicChars:[],setAllPublicChars:()=>{}});function tR(e){let{children:t}=e,[s,l]=(0,c.useState)([]);return(0,a.jsx)(tE.Provider,{value:{allPublicChars:s,setAllPublicChars:l},children:t})}var tA=s(79839);let tP=(0,d.atom)({key:"userUpdatedAtom",default:!1});var tM=s(27485),tz=s(86460),tL=s(15964),tT=()=>{let e=(0,y.P)(),t=(0,d.useRecoilValue)(u.Yu),{mutateAsync:s}=(0,e7.I)(),{setFilter:l}=(0,tL.Z)(),i=(0,d.useSetRecoilState)(tP),n=(0,d.useSetRecoilState)(u.It),[x,h]=(0,d.useRecoilState)(tz.q),{data:p,status:f,refetch:g}=(0,m.j)(),[v,b]=(0,c.useState)(!1),j=(0,o.useSearchParams)(),w=(0,d.useSetRecoilState)(u.Yu),_=(0,d.useSetRecoilState)(u.PC);(0,c.useEffect)(()=>{!j.has("filter")&&p&&(""===p.gender_preference||""===p.style_preference)&&_({open:!1,isSignup:!1,text:"",image:""})},[f]);let C=async e=>{let{gender_preference:t,style_preference:a}=e;b(!0),await s({updates:{gender_preference:t,style_preference:a}});{let e=t+"_"+a;localStorage.setItem("pref",e),i(e=>!e),n(e)}let l={...x};"Both"===t?l.gender=void 0:l.gender=t,"both"===a?l.style=void 0:"anime"===a?l.style="Anime":"realistic"===a&&(l.style="Realistic"),h(l),b(!1),w(!1)};return(0,a.jsxs)(Y.Vq,{open:t,children:[(0,a.jsx)(Y.t9,{className:"backdrop-blur-sm z-51"}),(0,a.jsxs)(Y.cZ,{closeBtn:"hidden",style:{background:"radial-gradient(hsl(var(--primary)) 0%, hsl(var(--background)) 50%, hsl(var(--background))"},className:"rounded-2xl! overflow-hidden w-72 xs:w-80 md:min-w-max z-51",children:[v&&(0,a.jsx)("div",{className:"bg-black/70 absolute h-full w-full flex justify-center items-center z-51",children:(0,a.jsx)(tM.gy,{color:"#d62a5e"})}),(0,a.jsx)("div",{className:"mb-6 flex justify-center",children:(0,a.jsx)(r.default,{src:"/logo.svg",width:200,height:100,alt:"Secret Desires Logo"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("button",{"aria-label":"I am interested in realistic female",onClick:()=>C({gender_preference:"Female",style_preference:"realistic"}),className:"bg-black h-14 md:h-auto hover:shadow-lg hover:shadow-primary transition-shadow flex md:block relative rounded-xl overflow-hidden md:border-2 md:border-card",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/female.png",height:300,width:300,alt:"Women",className:"hidden md:block absolute w-full h-48 object-cover"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/female_2.png",height:300,width:300,alt:"Women",className:"hidden md:block absolute w-full h-48 object-cover"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/bg_female.png",height:300,width:300,alt:"Women",className:"hidden md:block w-full z-20 h-48 object-cover"}),(0,a.jsxs)("div",{className:"md:absolute md:bottom-2 flex justify-between md:justify-center items-center space-x-0.5 w-full",children:[(0,a.jsx)("span",{className:"ml-3",children:(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/female_mob.png",height:115,width:115,unoptimized:!0,alt:"Female Image",className:"block md:hidden"})}),(0,a.jsxs)("span",{className:"flex justify-center w-full space-x-1",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/female_gender_icon.svg",height:16,width:16,alt:"Female gender icon"}),(0,a.jsx)("h3",{className:"text-xl font-bold [text-shadow:0px_0px_10px_rgb(214_42_94)]",children:"Women"})]})]})]}),(0,a.jsxs)("button",{"aria-label":"I am interested in realistic male",onClick:()=>C({gender_preference:"Male",style_preference:"realistic"}),className:"bg-black h-14 md:h-auto hover:shadow-lg hover:shadow-primary transition-shadow flex md:block relative rounded-xl overflow-hidden shadow-md md:border-2 md:border-card",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/male.png",height:300,width:300,alt:"Men",className:"hidden md:block absolute w-full h-48 object-cover"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/male_2.png",height:300,width:300,alt:"Men",className:"hidden md:block absolute w-full h-48 object-cover"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/bg_male.svg",height:300,width:300,alt:"Men",className:"hidden md:block w-full bg-white h-48 object-cover"}),(0,a.jsxs)("div",{className:"md:absolute md:bottom-2 flex justify-between md:justify-center items-center space-x-0.5 w-full",children:[(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/male_mob.png",height:95,width:95,unoptimized:!0,alt:"Male Image",className:"block md:hidden"})}),(0,a.jsxs)("span",{className:"flex justify-center w-full space-x-1",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/male_gender_icon.svg",height:21,width:21,alt:"Male gender icon"}),(0,a.jsx)("h3",{className:"text-xl font-bold [text-shadow:0px_0px_10px_rgb(1_148_196)]",children:"Men"})]})]})]}),(0,a.jsxs)("button",{"aria-label":"I am interested in anime male and female",onClick:()=>C({gender_preference:"Both",style_preference:"anime"}),className:"bg-black h-14 md:h-auto hover:shadow-lg hover:shadow-primary transition-shadow flex md:block relative rounded-xl overflow-hidden shadow-md md:border-2 md:border-card",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/male_anime.png",height:300,width:300,alt:"Anime",className:"hidden md:block md:absolute w-auto md:w-full h-20 md:h-36 object-contain -bottom-1 -right-6"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/female_anime.png",height:300,width:300,alt:"Anime",className:"hidden md:block md:absolute w-auto md:w-full h-20 md:h-48 md:object-cover"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/bg_anime.svg",height:300,width:300,alt:"Anime",className:"hidden md:block w-full bg-white h-48 object-cover"}),(0,a.jsxs)("div",{className:"md:absolute md:bottom-2 flex justify-between md:justify-center items-center space-x-0.5 w-full",children:[(0,a.jsxs)("span",{className:"flex -space-x-3 pl-3",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/anime_male_mob.png",height:50,width:50,unoptimized:!0,alt:"Male Anime Image",className:"block md:hidden"}),(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/anime_female_mob.png",height:50,width:50,unoptimized:!0,alt:"Female Anime Image",className:"block md:hidden"})]}),(0,a.jsxs)("span",{className:"flex justify-center w-full space-x-1",children:[(0,a.jsx)(r.default,{src:"/images/gender_pref_dialog/anime_gender_icon.svg",height:26,width:26,alt:"Anime gender icon"}),(0,a.jsx)("h3",{className:"text-xl font-bold [text-shadow:0px_0px_10px_rgb(204_0_255)]",children:"Anime"})]})]})]})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("button",{"aria-label":"No gender preference",onClick:()=>C({gender_preference:"Both",style_preference:"both"}),className:"mb-4 flex justify-center space-x-0.5 text-muted-foreground",children:[(0,a.jsxs)("p",{className:"underline",children:["I want to see ",(0,a.jsx)("strong",{children:"all"})," genders"]}),(0,a.jsx)("span",{className:"opacity-50 border-2 rounded-md scale-75",children:tD})]})}),(0,a.jsxs)("div",{className:"flex justify-center items-center space-x-0.5 text-muted-foreground text-sm",children:[(0,a.jsx)("span",{className:"scale-75",children:e.questionMark}),(0,a.jsxs)("p",{children:["By entering, you agree to our"," ",(0,a.jsx)(N.default,{"aria-label":"Terms of Service",href:"/tos",onClick:()=>C({gender_preference:"Both",style_preference:"both"}),className:"underline",children:"terms of service"})," ","and confirm that you are 18+"]})]})]})]})};let tD=(0,a.jsx)("svg",{className:"w-6 h-6 text-gray-800 dark:text-white","aria-hidden":"true",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",fill:"none",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 12H5m14 0-4 4m4-4-4-4"})});var tZ=s(62009),tF=()=>{let e=(0,d.useSetRecoilState)(u.$L),t=(0,d.useRecoilValue)(u.$L);return(0,a.jsxs)(Y.Vq,{open:t.open,onOpenChange:()=>e({open:!1,_id:""}),children:[(0,a.jsx)(Y.t9,{className:"backdrop-blur-sm z-51"}),(0,a.jsxs)(Y.cZ,{className:"rounded-2xl max-h-[90vh] overflow-y-scroll custom-scrollbar p-5 sidebar-background max-w-80 w-fit md:max-w-3xl min-w-80 rounded-lg shadow-lg overflow-x-hidden z-51",children:[(0,a.jsx)("div",{className:"mb-5 flex justify-center",children:(0,a.jsx)(r.default,{src:"/logo.svg",width:200,height:100,alt:"Secret Desires Logo"})}),(0,a.jsx)("div",{className:"text-center text-primary",children:(0,a.jsx)("h1",{className:"text-xl font-bold",children:null==t?void 0:t.description})}),(0,a.jsx)("div",{className:"pt-2 text-sm prose dark:prose-invert",children:(0,a.jsx)(tZ.Z,{content:null==t?void 0:t.content})})]})]})},tG=s(9881),tU=s(31877);function tH(){let e=(0,d.useRecoilValue)(u.dG);return(0,c.useEffect)(()=>{e?(document.body.classList.add("fixed"),document.body.style.overflow="hidden"):(document.body.classList.remove("fixed"),document.body.style.overflow="auto")},[e]),null}var tO=()=>{let e=(0,y.P)(),t=(0,o.useRouter)();(0,o.useSearchParams)();let s=(0,o.usePathname)(),l=(0,d.useRecoilValue)(x.loggedIn),{data:i,refetch:n}=(0,m.j)(),{mutateAsync:r}=b();(0,M.L)();let h=(0,d.useSetRecoilState)(eN.cf),p=(0,d.useSetRecoilState)(eN.ZQ),f=(0,d.useSetRecoilState)(eN.Kf),g=(0,d.useSetRecoilState)(eN._0),v=(0,d.useSetRecoilState)(eN.qY);(0,c.useEffect)(()=>{h(null),p(0),f(!1),g(""),v(!1)},[s]);let j=(0,d.useSetRecoilState)(u.PC),w=(0,d.useSetRecoilState)(eK.m),[_,N]=(0,c.useState)(""),C=[{icon:(0,a.jsx)(eW.YI,{color:"".concat("Explore"===_?"#dc295d":"#fff"),className:"w-6 h-6"}),name:"Explore"},{icon:(0,a.jsx)(eW.HX,{color:"".concat("Generate"===_?"#dc295d":"#fff"),className:"w-6 h-6"}),name:"Generate"},{icon:(0,a.jsx)(eW.x2,{color:"".concat("Create"===_?"#dc295d":"#fff"),className:"w-6 h-6"}),name:"Create"},{icon:(0,a.jsx)(eW.kB,{color:"".concat("Chat"===_?"#dc295d":"#fff"),background:"#000",className:"w-6 h-6"}),name:"Chat"},{icon:e.gift,name:"Premium"}],S=e=>{var s,a,n,o,c,d,u,m;let x="";switch(localStorage.getItem("sd_active_char_id")&&(x=localStorage.getItem("sd_active_char_id")||""),e){case"Explore":t.push("/"),localStorage.removeItem("character");break;case"Chat":if(x&&(null==i?void 0:null===(s=i.conv_history)||void 0===s?void 0:s.some(e=>e.char_id===x)))localStorage.removeItem("character"),w(!0),t.push("/chat");else if((null==i?void 0:i.conv_history)&&(null==i?void 0:null===(a=i.conv_history)||void 0===a?void 0:a.length)>0){let e=null==i?void 0:null===(n=i.conv_history)||void 0===n?void 0:n.length;localStorage.removeItem("character"),w(!0),localStorage.setItem("sd_active_char_id",i.conv_history&&(null==i?void 0:null===(o=i.conv_history[e-1])||void 0===o?void 0:o.char_id)),t.push("/chat")}else r({redirectTo:"chat"});localStorage.removeItem("character");break;case"Generate":if(x&&(null==i?void 0:null===(c=i.conv_history)||void 0===c?void 0:c.some(e=>e.char_id===x)))localStorage.removeItem("character"),t.push("/generator/ai-images?image-option=Action");else if((null==i?void 0:i.conv_history)&&(null==i?void 0:null===(d=i.conv_history)||void 0===d?void 0:d.length)>0){localStorage.removeItem("character");let e=null==i?void 0:null===(u=i.conv_history)||void 0===u?void 0:u.length;localStorage.setItem("sd_active_char_id",i.conv_history&&(null==i?void 0:null===(m=i.conv_history[e-1])||void 0===m?void 0:m.char_id)),t.push("/generator/ai-images?image-option=Action")}else r({redirectTo:"generator"});localStorage.removeItem("character");break;case"Create":localStorage.removeItem("character"),t.push("/create");break;case"Premium":l?(localStorage.removeItem("character"),t.push("/subscription")):j({open:!0,isSignup:!0,text:"Sign Up to See Plans",image:""});break;default:localStorage.removeItem("character")}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("header",{className:"fixed bottom-0 left-0 right-0 bg-black shadow-md z-10",children:(0,a.jsx)("nav",{className:"navbar container px-6",children:(0,a.jsx)("div",{className:"menu",children:(0,a.jsx)("ul",{className:"menu-list flex justify-around items-center py-4",children:C.map((e,t)=>(0,a.jsx)("li",{onClick:()=>{N(e.name),S(e.name)},className:"menu-item cursor-pointer ".concat("Premium"===e.name&&_===e.name?"text-[#ffae00]":_===e.name?"text-primary font-bold":""),children:(0,a.jsxs)("div",{className:"menu-link flex flex-col items-center",children:[e.icon,(0,a.jsx)("span",{style:{fontSize:"0.7rem"},className:"menu-name",children:"Premium"===e.name?i?"Free"!==i.subscription_tier&&"Visitor"!==i.subscription_tier?i.subscription_tier:e.name:"Premium":e.name})]})},t))})})})})})},tV=function(){var e;let t=(0,d.useRecoilValue)(D),s=(0,d.useSetRecoilState)(u.$L),{mutateAsync:l}=H(),i=(0,o.useRouter)(),n=(0,d.useSetRecoilState)(D),[r,m]=(0,c.useState)(!1);if(!t)return null;async function x(e){await l({ids:["system_".concat(e)]}),s({open:!0,_id:e,...t})}let h=()=>{m(!0)};return(0,a.jsx)(a.Fragment,{children:!r&&(0,a.jsxs)("div",{onClick:()=>{t.redirectUrl?i.push(t.redirectUrl):x(t._id),n(null)},className:"fixed right-4 z-50 flex gap-2 items-center cursor-pointer",style:{top:"80px"},children:[(0,a.jsxs)("div",{className:" flex items-center line-clamp-1 max-w-[100px] active-notification rounded-full px-2 py-4 text-white text-sm overflow-hidden",children:[(0,a.jsx)("p",{style:{lineClamp:1,maxWidth:"180px"},className:"line-clamp-1",children:null==t?void 0:t.description}),(0,a.jsxs)("p",{className:"text-[8px]",children:[" ",(0,a.jsx)(G.Z,{width:12,height:12})]})]}),(0,a.jsxs)(F.qE,{className:"relative min-w-14 min-h-14",children:[(0,a.jsx)("button",{className:"absolute -top-2 -right-2 bg-white text-red-500 rounded-full w-6 h-6 flex items-center justify-center shadow-md border border-gray-300 transition",onClick:e=>{e.stopPropagation(),h()},children:"✕"}),(0,a.jsx)(F.F$,{src:null==t?void 0:t.img,alt:"avatar",className:"w-14 h-14 object-cover object-top rounded-full border-2 border-primary"}),(0,a.jsx)(F.Q5,{className:"border-2 border-primary flex items-center justify-center bg-background rounded-full text-xl w-14 h-14",children:null==t?void 0:null===(e=t.sender)||void 0===e?void 0:e.charAt(0)})]})]})})},tq=s(99687),tB=s(23859);function tQ(e){let{setIsOpen:t}=e,s=(0,tB.d)(),l=(0,o.useRouter)(),i=e=>{l.push(e),s&&t(!1)};return(0,a.jsx)("div",{className:"flex bottom-0 items-center justify-center pt-1",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4 text-center",children:[(0,a.jsxs)("div",{className:"space-y-2 flex flex-col items-center justify-center",children:[(0,a.jsx)(tK,{}),(0,a.jsx)("h2",{className:"font-semibold text-lg",children:"Capabilities"})]}),(0,a.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,a.jsxs)("div",{className:"bg-card p-3 rounded-lg",children:["Want to read the full guide?"," ",(0,a.jsx)("button",{className:"font-bold underline",onClick:()=>i("/guide"),children:"Click here"})]}),(0,a.jsxs)("div",{className:"bg-card p-3 rounded-lg",children:["Visit your"," ",(0,a.jsx)("button",{className:"font-bold underline",onClick:()=>i("/settings"),children:"settings"})," ","to toggle between roleplay and texting response styles."]}),(0,a.jsxs)("div",{className:"bg-card p-3 rounded-lg",children:["Your conversations are"," ",(0,a.jsx)("button",{className:"font-bold underline",onClick:()=>i("/privacy"),children:"100% secure"})," ","and confidential. Your data is never sold to advertisers (or anyone, for that matter)."]})]})]})})}let tK=()=>(0,a.jsx)("svg",{width:"19",height:"22",viewBox:"0 0 19 22",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M1.25 12.75L11.75 1.5L9.5 9.75H17.75L7.25 21L9.5 12.75H1.25Z",fill:"none",stroke:"#FFFFFF",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),tY=(0,d.atom)({key:"sessionIdAtom",default:""}),t$=(0,d.atom)({key:"assistantMessagesAtom",default:[{role:"assistant",text:"Hi! I'm {{assistant_name}}. Feel free to ask me anything about using the platform."}]});var tW=s(18977),tX=s(27423);function tJ(e){let{assistantName:t,setIsOpen:s}=e,l=(0,tX.useMutationState)({filters:{mutationKey:["get-assistant-reply"],status:"pending"}}).length>0,i=(0,c.useRef)(null),n=(0,d.useRecoilValue)(t$),r=e=>e.replace("{{assistant_name}}",t);return(0,c.useEffect)(()=>{var e,t;null===(t=i.current)||void 0===t||t.scrollTo({top:null===(e=i.current)||void 0===e?void 0:e.scrollHeight,behavior:"smooth"})},[n,l]),(0,a.jsxs)("div",{ref:i,className:"custom-scrollbar background flex-1 overflow-y-auto p-4 space-y-4 flex flex-col justify-between",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[n.map((e,t)=>(0,a.jsx)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"),children:(0,a.jsx)("div",{className:"p-3 max-w-[80%] ".concat("user"===e.role?"bg-primary rounded-t-lg rounded-bl-lg":"bg-card rounded-t-lg rounded-br-lg"),children:(0,a.jsx)(tW.Z,{text:r(e.text)})})},t)),l&&(0,a.jsx)("div",{className:"p-3 w-fit bg-card rounded-t-lg rounded-br-lg",children:(0,a.jsx)(tM.g4,{visible:!0,height:"20",width:"30",color:"#eaeaea",radius:"5",ariaLabel:"three-dots-loading",wrapperStyle:{},wrapperClass:""})})]}),n.length<2&&(0,a.jsx)(tQ,{setIsOpen:s})]})}function t0(e){var t;let{assistant:s,setIsOpen:l}=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-2 border-b border-opacity-10 border-white",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(F.qE,{className:"h-12 w-12 rounded-full overflow-hidden",children:[(0,a.jsx)(F.F$,{src:s.pfp,alt:"profile picture of AI assistant"}),(0,a.jsx)(F.Q5,{children:null===(t=s.name.slice(0,1))||void 0===t?void 0:t.toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-lg leading-tight",children:s.name}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"AI Assistant"})]})]}),(0,a.jsx)(S.z,{variant:"ghost",size:"icon",onClick:()=>l(!1),children:(0,a.jsx)(L.Z,{className:"h-4 w-4"})})]})}let t1=()=>{let{data:e}=(0,m.j)(),[t,s]=(0,d.useRecoilState)(tY),[a,l]=(0,d.useRecoilState)(t$),i=async i=>{let{query:n}=i;l(e=>[...e,{role:"user",text:n}]);let r=await I.j.post("/assistant/get-reply",{session_id:t||"",query:n,messages:a||[],user:{user_nickname:(null==e?void 0:e.user_nickname)||"user",user_age:(null==e?void 0:e.user_age)||18,gender_preference:(null==e?void 0:e.gender_preference)||"female",about_me:(null==e?void 0:e.about_me)||"I'm a user"}});return l(e=>[...e,{role:"assistant",text:r.data.msg}]),s(r.data.session_id),r.data};return(0,p.useMutation)({mutationKey:["get-assistant-reply"],mutationFn:i,onSuccess:()=>{},onError:()=>{}})};var t2=s(60994),t5=s(56533);function t4(){var e=(0,ei.Z)();try{let[e,t]=(0,c.useState)(""),s=(0,c.useRef)(null),{mutateAsync:l,isPending:i}=t1(),n=i||""===e.trim();(0,t5.c)(s);let r=async s=>{s&&s.preventDefault();let a=e.trim();a&&(t(""),await l({query:a}))};return(0,c.useEffect)(()=>{if(s.current){let e=s.current;e.style.height="auto";let t=Math.max(e.scrollHeight,30);e.style.height="".concat(Math.min(t,200),"px")}},[e]),(0,a.jsx)("div",{className:"bg-black",children:(0,a.jsxs)("form",{onSubmit:r,className:"flex items-center gap-2 rounded-lg bg-card px-4 py-2 m-4",children:[(0,a.jsx)("textarea",{ref:s,value:e,onChange:e=>t(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),r())},placeholder:"Ask me anything...",className:"flex-1 bg-transparent text-white placeholder-neutral-400 outline-none resize-none overflow-y-auto max-h-[200px]",rows:1}),(0,a.jsx)("button",{type:"submit",disabled:n,"aria-label":"Send query to helping AI assistant",children:(0,a.jsx)(t2.Z,{"aria-disabled":n,className:"h-5 w-5 text-primary aria-disabled:text-white transition-colors"})})]})})}finally{e.f()}}function t3(e){let{isOpen:t,setIsOpen:s}=e,[l,i]=(0,c.useState)({name:"Scarlett",pfp:"/images/female_assistant.png"}),{data:n}=(0,m.j)();return(0,c.useEffect)(()=>{n&&i({name:"Male"===n.gender_preference?"Roman":"Scarlett",pfp:"Male"===n.gender_preference?"/images/male_assistant.png":"/images/female_assistant.png"})},[n]),(0,a.jsxs)(C.Zb,{className:"".concat(t?"flex":"hidden"," fixed rounded-none sm:rounded-2xl bottom-16 right-0 sm:bottom-20 ms:right-4 sm:w-[380px] w-full h-[calc(100dvh-4rem)] sm:h-[600px] flex-col shadow-2xl sm:border border-opacity-20 border-white"),children:[(0,a.jsx)(t0,{assistant:l,setIsOpen:s}),(0,a.jsx)(tJ,{assistantName:l.name,setIsOpen:s}),(0,a.jsx)(t4,{})]})}function t8(){let e=(0,o.usePathname)(),[t,s]=(0,c.useState)(!1);return(0,a.jsxs)("div",{className:"".concat(e.includes("/chat")||e.includes("/generator")?"hidden":""," fixed bottom-20 sm:bottom-4 right-4 z-50 "),children:[(0,a.jsx)(t3,{isOpen:t,setIsOpen:s}),(0,a.jsx)(S.z,{className:"h-12 w-12 rounded-full p-0 shadow-xs",onClick:()=>s(!t),children:t?(0,a.jsx)(L.Z,{}):(0,a.jsx)(tq.Z,{})})]})}var t6=s(76383),t7=s(88876);function t9(){let e=(0,o.usePathname)(),t="".concat("https://secretdesires.ai").concat(e.split("?")[0]);return(0,a.jsx)("link",{rel:"canonical",href:t})}var se=s(66519),st=s(9835),ss=s(61235),sa=s(4686);function sl(){let{data:e}=(0,m.j)(),{mutate:t}=(0,e7.I)(),[s,l]=(0,c.useState)({}),i=(0,d.useRecoilValue)(u.wl),r=(0,d.useSetRecoilState)(u.wl);(0,c.useEffect)(()=>{(async()=>{l(await (0,se.Z)())})()},[]),(0,c.useEffect)(()=>{console.log({showUpdateBillingModal:i})},[]),(0,c.useEffect)(()=>{console.log("checking payment info and show modal"),(async()=>{if(e&&0!==Object.keys(s).length&&"Free"!==e.subscription_tier&&"Visitor"!==e.subscription_tier&&"Max-Trial"!==e.subscription_tier&&!1===e.dontShowBillingPopup&&"active"===e.subscription_status&&(""!==e.stripe_customer_id||null!==e.stripe_customer_id||void 0!==e.stripe_customer_id))try{let{data:e}=await (0,st.q3)();e.success&&0===e.data.payment_methods.length&&r(!0)}catch(e){console.error("Error checking payment info:",e),r(!1)}})()},[e,s]);let o=async s=>{let{paymentInfo:a,billingInfo:l,dontShowBillingPopup:i}=s,o={user_id:(null==e?void 0:e._id)||"123",customer_info:{customer_email:l.email,customer_first_name:l.firstName,customer_last_name:l.lastName,customer_phone:l.phoneNumber,billing_address:l.address,billing_address_city:l.city,billing_address_state:l.state,billing_address_country:l.country,billing_address_zip:l.pinCode},payment_methods:[{customer_cc_exp_date:a.cardExpirationDate,customer_cc_code:a.cardCvv,customer_cc_number:a.cardNumber.replace(/\s/g,""),customer_cc_brand:a.cardBrand,customer_ssn_last_4:String(a.cardNumber.replace(/\s/g,"")).slice(-4),is_default:!0,is_active:!0}]},{data:c}=await (0,ss.ND)(o);i&&console.log({res:await t({updates:{dontShowBillingPopup:!0}})}),c.success?(n.default.success("Payment information saved successfully",{id:"hearts",duration:1e3}),r(!1)):n.default.error("Failed to save payment information",{id:"hearts",duration:1e3})};return i&&(0,a.jsx)(sa.Y,{open:i,setOpen:r,title:"Update your payment details to keep enjoying hearts and premium features",onSave:o})}function si(e){let{children:t}=e,s=(0,o.usePathname)();(0,c.useEffect)(()=>{(0,t6.R)(),t7.Z.init(5288494,6)},[]);let l=!s.includes("/call")&&!s.includes("/payment");return(0,a.jsxs)("html",{lang:"en",className:"dark",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("title",{children:"Secret Desires AI - Your Fantasies Live Here"}),(0,a.jsx)("meta",{name:"description",content:"Unlock your deepest fantasies and connect with an AI partner who understands you like no one else. Text, call, and receive images from your AI partner for raw and unfiltered fun."}),(0,a.jsx)("meta",{property:"og:url",content:"https://staging.secretdesires.ai/create"}),(0,a.jsx)("meta",{property:"og:type",content:"website"}),(0,a.jsx)("meta",{property:"og:title",content:"Secret Desires AI - Your Fantasies Live Here"}),(0,a.jsx)("meta",{property:"og:description",content:"Unlock your deepest fantasies and connect with an AI partner who understands you like no one else. Text, call, and receive images from your AI partner for raw and unfiltered fun."}),(0,a.jsx)("meta",{property:"og:image",content:"/images/seo-thumbnail.png"}),(0,a.jsx)("meta",{property:"og:image:alt",content:"Secret Desires AI - Your Fantasies Live Here"}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,a.jsx)("meta",{property:"twitter:domain",content:"staging.secretdesires.ai"}),(0,a.jsx)("meta",{property:"twitter:url",content:"https://staging.secretdesires.ai/create"}),(0,a.jsx)("meta",{name:"twitter:title",content:"Secret Desires AI - Your Fantasies Live Here"}),(0,a.jsx)("meta",{name:"twitter:description",content:"Unlock your deepest fantasies and connect with an AI partner who understands you like no one else. Text, call, and receive images from your AI partner for raw and unfiltered fun."}),(0,a.jsx)("meta",{name:"twitter:image",content:"/images/seo-thumbnail.png"}),(0,a.jsx)("meta",{property:"twitter:image:alt",content:"Secret Desires AI - Your Fantasies Live Here"}),(0,a.jsx)("link",{rel:"shortcut icon",href:"/favicon.svg"}),(0,a.jsx)("script",{src:"fprmain.js",defer:!0}),(0,a.jsx)("script",{src:"https://cdn.firstpromoter.com/fpr.js",defer:!0}),(0,a.jsx)(tU.default,{id:"bing-uet",strategy:"lazyOnload",dangerouslySetInnerHTML:{__html:'\n              (function(w,d,t,r,u) {\n                var f,n,i;\n                w[u]=w[u]||[],f=function() {\n                  var o={ti:"331006063", enableAutoSpaTracking: true};\n                  o.q=w[u],w[u]=new UET(o),w[u].push("pageLoad")\n                },\n                n=d.createElement(t),n.src=r,n.async=1,n.onload=n.onreadystatechange=function() {\n                  var s=this.readyState;\n                  s&&s!=="loaded"&&s!=="complete"||(f(),n.onload=n.onreadystatechange=null)\n                },\n                i=d.getElementsByTagName(t)[0],i.parentNode.insertBefore(n,i)\n              })(window,document,"script","//bat.bing.com/bat.js","uetq");\n            '}}),(0,a.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap",rel:"stylesheet"}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,a.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com"}),(0,a.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap",rel:"stylesheet"}),(0,a.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Permanent+Marker&display=swap",rel:"stylesheet"}),(0,a.jsx)("noscript",{children:(0,a.jsx)("img",{height:1,width:1,style:{display:"none"},src:"https://www.facebook.com/tr?id=".concat("428243453561665","&ev=PageView&noscript=1")})}),(0,a.jsx)("noscript",{children:(0,a.jsx)("img",{height:1,width:1,style:{display:"none"},src:"https://www.facebook.com/tr?id=".concat("341501846698358","&ev=PageView&noscript=1")})}),(0,a.jsx)(t9,{})]}),(0,a.jsxs)("body",{className:"custom-scrollbar h-screen lg:w-[calc(100vw-6px)] ".concat(i().className),children:[(0,a.jsx)(e8.QueryClientProvider,{client:I.E,children:(0,a.jsx)(d.RecoilRoot,{children:(0,a.jsx)(tw,{children:(0,a.jsx)(tm.SocketProvider,{children:(0,a.jsx)(_.D,{children:(0,a.jsx)(tR,{children:(0,a.jsx)(tN,{children:(0,a.jsx)(tI.S,{children:(0,a.jsx)(tC.pn,{delayDuration:200,children:(0,a.jsxs)(c.Suspense,{children:[(0,a.jsx)(tT,{}),(0,a.jsx)(tF,{}),(0,a.jsx)(tA.t,{initialIsOpen:!1,buttonPosition:"bottom-left"}),(0,a.jsx)(n.Toaster,{}),(0,a.jsx)(tS.ZP,{}),(0,a.jsx)(tu,{}),(0,a.jsx)(tG.GoogleTagManager,{gtmId:"GTM-PQWLXBK5"}),(0,a.jsx)(tG.GoogleAnalytics,{gaId:"G-7Y539MDMRN"}),l&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(td,{}),(0,a.jsx)(sl,{}),(0,a.jsx)(tH,{}),(0,a.jsx)(tV,{}),(0,a.jsx)(t8,{})]}),(0,a.jsx)("div",{className:"".concat(l?"pt-16":""),children:t}),!l&&(0,a.jsx)("div",{className:"show-mobile",children:!s.includes("chat")&&!s.includes("/call")&&(0,a.jsx)(tO,{})}),!s.includes("/subscription")&&!s.includes("/call")&&!s.includes("/payment")&&(0,a.jsx)(tk,{})]})})})})})})})})})}),(0,a.jsx)(tU.default,{id:"clarity",strategy:"lazyOnload",dangerouslySetInnerHTML:{__html:'\n              (function(c,l,a,r,i,t,y) {\n                  c[a] = c[a] || function() {\n                      (c[a].q = c[a].q || []).push(arguments)\n                  };\n                  t = l.createElement(r);\n                  t.async = 1;\n                  t.src = "https://www.clarity.ms/tag/" + i;\n                  y = l.getElementsByTagName(r)[0];\n                  y.parentNode.insertBefore(t, y);\n              })(window, document, "clarity", "script", "ntp30h52n2");\n            '}}),(0,a.jsx)(tU.default,{id:"facebook-pixel",strategy:"lazyOnload",dangerouslySetInnerHTML:{__html:"\n              !function(f,b,e,v,n,t,s) {\n                if(f.fbq) return;\n\n                n=f.fbq=function(){\n                  n.callMethod ?\n                    n.callMethod.apply(n, arguments) :\n                    n.queue.push(arguments)\n                };\n\n                if(!f._fbq)f._fbq=n;\n                n.push=n;\n                n.loaded=!0;\n                n.version='2.0';\n                n.queue=[];\n                t=b.createElement(e);\n                t.async=!0;\n                t.src=v;\n                s=b.getElementsByTagName(e)[0];\n                s.parentNode.insertBefore(t,s)\n              }\n              (\n                window, document,'script',\n                  'https://connect.facebook.net/en_US/fbevents.js'\n              );\n              fbq('init', ".concat("428243453561665",");\n              fbq('init', ").concat("341501846698358",");\n              fbq('trackSingle', ").concat("428243453561665",", 'PageView');\n              fbq('trackSingle', ").concat("341501846698358",", 'PageView');\n            ")}})]})]})}},84849:function(e,t,s){"use strict";s.d(t,{Z:function(){return I}});var a=s(57437),l=s(16880),i=s(42421),n=s(14392),r=s(2265),o=s(48297),c=s(13498);let d=o.fC,u=o.B4,m=r.forwardRef((e,t)=>{let{className:s,children:l,...n}=e;return(0,a.jsxs)(o.xz,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...n,children:[l,(0,a.jsx)(o.JO,{asChild:!0,children:(0,a.jsx)(i.Z,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=o.xz.displayName;let x=r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(o.u_,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,a.jsx)(n.Z,{className:"h-4 w-4"})})});x.displayName=o.u_.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(o.$G,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...l,children:(0,a.jsx)(i.Z,{className:"h-4 w-4"})})});h.displayName=o.$G.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:l,position:i="popper",...n}=e;return(0,a.jsx)(o.h_,{children:(0,a.jsxs)(o.VY,{ref:t,className:(0,c.cn)("relative z-[9999999] max-h-96 overflow-hidden rounded-md border bg-[#111111] text-white shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,avoidCollisions:!1,style:{width:"var(--radix-select-trigger-width)"},...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(o.l_,{className:(0,c.cn)("p-1","popper"===i&&"w-full"),children:l}),(0,a.jsx)(h,{})]})})});p.displayName=o.VY.displayName,r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)(o.__,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...l})}).displayName=o.__.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:l,...i}=e;return(0,a.jsx)(o.ck,{ref:t,className:(0,c.cn)("relative w-full cursor-default select-none items-center rounded-sm text-sm outline-none focus:bg-accent focus:text-accent-foreground data-disabled:pointer-events-none data-disabled:opacity-50",s),...i,children:(0,a.jsx)(o.eT,{children:l})})});f.displayName=o.ck.displayName;let g=r.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,c.cn)("mx-1 my-1 h-px bg-gray-600",s),...l})});g.displayName="SelectSeparator";let v=e=>{let{value:t,onChange:s,options:i,placeholder:n}=e;var o=(0,l.Z)();try{let[e,l]=(0,r.useState)(!1),[o,c]=(0,r.useState)({label:n||"Select Country",value:""});(0,r.useEffect)(()=>{let e=i.find(e=>{var s;return!e.separator&&(e.value===t||e.label===t||(null===(s=e.meta)||void 0===s?void 0:s.name)===t)});c(e||{label:n||"Select Country",value:""})},[t]);let x=e=>{let t=i.find(t=>t.value===e);t&&!t.separator&&(null==s||s(t),c(t),l(!1))},h=e=>{"Space"===e.code&&e.preventDefault()};return(0,a.jsxs)(d,{onValueChange:e=>x(e),value:o.value,open:e,onOpenChange:l,children:[(0,a.jsx)(m,{className:"w-full text-[#BFBFBF] border-none rounded-xl focus:ring-0 focus:ring-offset-0 focus:outline-none focus:border-none",onKeyDown:h,children:(0,a.jsx)(u,{placeholder:n})}),(0,a.jsx)(p,{className:"z-60",side:"bottom",align:"center",sideOffset:4,onKeyDown:h,children:i.map(e=>{var t;return e.separator?(0,a.jsx)(g,{},e.value):(0,a.jsx)(f,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-x-2 px-2 py-3 text-md font-poppins",children:[(0,a.jsx)("img",{src:null===(t=e.meta)||void 0===t?void 0:t.flag,alt:e.label,className:"w-4 h-4"}),e.label]})},e.value)})})]})}finally{o.f()}};var b=s(74567),j=s(96223);function y(e){let{data:t,isLoading:s,error:l}=(0,b.F)();return s?(0,a.jsxs)("div",{className:"w-full flex flex-col gap-y-1",children:[(0,a.jsx)(j.Z,{label:"Country",className:"text-sm font-bold"}),(0,a.jsx)("div",{className:"w-full h-[40px] p-2 flex items-center bg-transparent border rounded-md",children:"Loading..."})]}):(0,a.jsx)(v,{...e,placeholder:"Select Country",label:"Country",options:null==t?void 0:t.map(e=>({label:e.name,value:e.code3,meta:e,separator:e.separator||!1}))})}var w=function(e){let{label:t,placeholder:s,className:i,value:n,onChange:r,maxLength:o,numberOnly:c,suffix:d,...u}=e;var m=(0,l.Z)();try{return(0,a.jsxs)("div",{className:"flex flex-row items-center pr-4 w-full h-fit gap-y-1 ".concat(i),children:[(0,a.jsx)("input",{placeholder:s,value:n,onChange:e=>{c&&(e.target.value=e.target.value.replace(/[^0-9]/g,"")),null==r||r(e.target.value)},maxLength:o,className:"w-full p-2 h-[40px] flex text-[#BFBFBF] items-center bg-transparent focus:outline-none rounded-md py-2 px-3",...u}),d&&(0,a.jsx)(a.Fragment,{children:d})]})}finally{m.f()}},_=s(50824),N=s(68495),C=s(48689),S=s(88908),k=s(48841);function I(e){let{data:t,setData:s}=e;var i=(0,l.Z)();try{let{user:e}=(0,C.a)(),{data:l}=(0,k.F)(),[i,n]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{(async()=>{if((null==e?void 0:e.ip_address)&&e.ip_address.length>0)try{let a=await (0,S.H)(e);if(a.country&&!a.error){let e=null==l?void 0:l.find(e=>{let t=e.name===a.country,s=e.code3===a.countryCode||e.code2===a.countryCode;return t||s});(""!==t.country||null!==t.country)&&s({...t,country:(null==e?void 0:e.name)||"",countryShort:(null==e?void 0:e.code3)||""})}}catch(e){console.error("Error getting country from IP:",e)}})()},[e,l]),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 h-full",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(j.Z,{label:"Cardholder Name"}),(0,a.jsxs)(_.ZP,{direction:"horizontal",className:"rounded-xl",children:[(0,a.jsx)(w,{placeholder:"First Name",value:t.firstName,onChange:e=>s({...t,firstName:e})}),(0,a.jsx)(w,{className:"border-none",placeholder:"Last Name",value:t.lastName,onChange:e=>s({...t,lastName:e})})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(j.Z,{label:"Billing Address"}),(0,a.jsxs)(_.ZP,{direction:"vertical",className:"rounded-xl relative",children:[(0,a.jsx)(y,{value:t.country,onChange:e=>s({...t,country:e.label,countryShort:e.value})}),(0,a.jsx)(N.Z,{label:"Address",placeholder:"Address",value:t.address,onAddressChange:e=>{console.log({addressComponents:e}),s({...t,address:e.address||"",city:e.city||"",state:e.state||"",country:e.country||"",countryShort:e.countryShort||"",pinCode:e.pinCode||""}),n(!0)},required:!0}),(0,a.jsxs)(_.ZP,{direction:"vertical",className:i?"":"hidden",children:[(0,a.jsxs)(_.ZP,{direction:"horizontal",children:[(0,a.jsx)(w,{label:"City",placeholder:"City",value:t.city,onChange:e=>s({...t,city:e})}),(0,a.jsx)(w,{label:"ZIP",placeholder:"ZIP",value:t.pinCode,onChange:e=>s({...t,pinCode:e})})]}),(0,a.jsx)(w,{label:"State/Province",placeholder:"State",value:t.state,onChange:e=>s({...t,state:e})})]})]})]}),(0,a.jsx)(j.Z,{label:i?"Back":"Add Address Manually",className:"text-xs font-light underline w-fit",onClick:()=>n(!i)})]})}finally{i.f()}}},18207:function(e,t,s){"use strict";s.d(t,{Z:function(){return v}});var a=s(57437),l=s(16880),i=s(57218),n=s(85231),r=s(2265),o=s(96223),c=s(44839);function d(e){let{id:t,name:s,label:l,checked:i,onChange:n,className:r}=e;return(0,a.jsxs)("div",{className:(0,c.Z)("flex gap-x-8 items-center",r),children:[(0,a.jsxs)("div",{className:"grid place-items-center",children:[(0,a.jsx)("input",{type:"radio",id:t,checked:i,onChange:n,name:s,className:(0,c.Z)("peer col-start-1 row-start-1 appearance-none shrink-0 w-6 h-6 border-2 border-[#d62a5e] rounded-full focus:outline-none focus:ring-offset-0 disabled:border-gray-400")}),(0,a.jsx)("div",{className:(0,c.Z)("pointer-events-none","col-start-1 row-start-1","w-[13px] h-[13px] rounded-full peer-checked:bg-[#d62a5e]","peer-checked:peer-disabled:bg-gray-400")})]}),(0,a.jsx)("label",{htmlFor:t,className:(0,c.Z)("text-start hover:cursor-pointer my-0.5 flex gap-x-2 items-center"),children:l})]})}function u(e){let{icon:t,label:s,id:n,children:u,className:m=""}=e;var x=(0,l.Z)();try{let e=i.BK.value===n,l=(0,r.useRef)(null),x=(0,r.useRef)(null),[h,p]=(0,r.useState)("0px");return(0,r.useEffect)(()=>{if(!l.current||!x.current)return;let t=()=>{if(e&&x.current){let e=x.current.scrollHeight;p("".concat(e,"px"))}else p("0px")};t();let s=new ResizeObserver(()=>{e&&t()});return x.current&&s.observe(x.current),()=>{s.disconnect()}},[e,u]),(0,a.jsxs)("div",{className:(0,c.Z)("rounded-lg dark:bg-[#131313]/80 w-full flex flex-col cursor-pointer px-8",m,e&&"gap-y-2 py-6 px-8"),onClick:()=>(0,i.OR)(n),"aria-expanded":e,"aria-controls":n,children:[(0,a.jsx)(d,{id:n,name:"payment-method",className:"my-2",label:(0,a.jsxs)(a.Fragment,{children:[t,(0,a.jsx)(o.Z,{label:s,className:"text-sm font-bold"})]}),checked:e,onChange:()=>{}}),(0,a.jsx)("div",{ref:l,id:n,role:"region","aria-labelledby":"accordion-title-01","aria-hidden":!e,style:{height:h,transition:"height 300ms ease"},className:"text-sm text-slate-600 overflow-hidden",children:(0,a.jsx)("div",{ref:x,className:"py-2",children:u})})]})}finally{x.f()}}var m=s(84849),x=s(31231),h=s(50824),p=s(60977),f=s(20151),g=s(47818);function v(e){let{info:t,setInfo:s}=e;var r=(0,l.Z)();try{return(0,a.jsx)(u,{id:"debit-credit-card",icon:(0,a.jsx)(n._K,{className:"w-6 h-6 sm:w-7 sm:h-7"}),className:"relative",label:"Credit/Debit Card",children:(0,a.jsxs)("div",{className:"w-full rounded-2xl flex flex-col gap-y-3 sm:gap-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2 sm:gap-y-3",children:[(0,a.jsx)(o.Z,{label:"Card Information"}),(0,a.jsxs)(h.ZP,{direction:"vertical",className:"rounded-xl",children:[(0,a.jsx)(p.Z,{cardNumber:t.cardNumber,onChange:e=>s({...t,cardNumber:e.number,cardBrand:(0,x.LF)(e.type)})}),(0,a.jsxs)(h.ZP,{direction:"horizontal",children:[(0,a.jsx)(f.Z,{value:t.cardExpirationDate,onChange:e=>s({...t,cardExpirationDate:e})}),(0,a.jsx)(g.Z,{label:"CVV",placeholder:"123",suffix:(0,a.jsx)(n._K,{className:"w-5 h-5"}),maxLength:4,numberOnly:!0,value:t.cardCvv,onChange:e=>s({...t,cardCvv:e})})]})]})]}),(0,a.jsx)("div",{className:"flex flex-col gap-y-3 sm:gap-y-4",children:(0,a.jsx)(m.Z,{data:i.paymentStore.value.billingInformation,setData:e=>(0,i.sw)(e)})})]})})}finally{r.f()}}},38857:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var a=s(57437),l=s(3274);function i(){return(0,a.jsx)("div",{className:"w-full h-full z-10 absolute pointer-events-none top-0 left-0 bg-black/50 backdrop-blur-sm flex items-center justify-center rounded-2xl",children:(0,a.jsx)(l.Z,{className:"w-8 h-8 animate-spin text-white"})})}},96223:function(e,t,s){"use strict";var a=s(57437);t.Z=function(e){let{label:t,className:s,onClick:l}=e;return(0,a.jsx)("div",{className:"text-white text-md ".concat(s),onClick:l,children:t})}},51299:function(e,t,s){"use strict";var a=s(57437),l=s(2265),i=s(21220),n=s(50495),r=s(66648);t.Z=e=>{let{setData:t,setCharOrUser:s,defaultAge:o,handleEditUser:c}=e,[d,u]=(0,l.useState)(!1),[m,x]=(0,l.useState)(o),h=e=>{x(e),t&&t(t=>({...t,age:e}))};return(0,l.useEffect)(()=>{x(o)},[o]),(0,l.useEffect)(()=>{t&&t(e=>({...e,age:m})),s&&(s(e=>({...e,user_age:m})),u(!0))},[m]),(0,l.useEffect)(()=>{d&&(c&&c(),u(!1))},[d]),(0,a.jsx)("div",{className:"relative inline-block ",children:(0,a.jsxs)(i.h_,{children:[(0,a.jsx)(i.$F,{asChild:!0,id:"age","aria-label":"Select Age",className:"flex items-center bg-background justify-between ps-3 pe-0.5 border border-background rounded-full cursor-pointer hover:bg-background       focus:outline-none focus:ring-0 focus:ring-none focus:ring-offset-0",children:(0,a.jsxs)(n.z,{"aria-label":"Select Age",variant:"outline",className:"bg-background ".concat(void 0===m||null==m?"text-muted-foreground hover:text-muted-foreground":"text-white "," px-2 w-28 outline-none \n						focus:ring-0  focus:ring-offset-0\n             focus-visible:ring-0 focus-visible:ring-offset-0"),children:[void 0===m||null==m?"Age":m,(0,a.jsx)("span",{className:"flex items-center justify-center w-8 h-8 bg-primary rounded-full",children:(0,a.jsx)(r.default,{src:"/icons/caret-down.svg",alt:"Down arrow",width:"16",height:"16"})})]})}),(0,a.jsx)(i.AW,{className:"w-28 my-1 bg-background cursor-pointer rounded-md shadow-lg max-h-40 overflow-y-auto custom-scrollbar text-white z-30",children:Array.from({length:63},(e,t)=>18+t).map(e=>(0,a.jsx)(i.qB,{value:e.toString(),className:" px-4 py-2 cursor-pointer border-0 focus:bg-primary hover:bg-primary        hover:ring-0 hover:ring-none hover:ring-offset-0 hover:outline-none ",onClick:()=>h(e),children:e},e))})]})})}},39378:function(e,t,s){"use strict";s.d(t,{Z:function(){return y}});var a=s(57437),l=s(70571),i=s(66648),n=s(2265),r=s(86466),o=e=>{let{consent:t,setConsent:s}=e;return(0,a.jsxs)("div",{className:"flex items-center sm:pt-2 sm:gap-2 gap-1",children:[(0,a.jsx)(r.X,{id:"terms",className:"sm:scale-[1] scale-[.65]",checked:t,onCheckedChange:()=>{s(e=>!e)}}),(0,a.jsx)("label",{htmlFor:"terms",className:"sm:text-base text-[11px] sm:tracking-wide font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"I have consent to use this media"})]})},c=s(50495),d=s(22351);let u=()=>async e=>{let{clip:t}=e;return(await d.j.post("/voice/voice_cloning",t,{headers:{"Content-Type":"multipart/form-data"}})).data};var m=s(38472),x=s(13498),h=s(66511),p=s(99441),f=s(85499),g=s(14738),v=s(3114),b=s(67910),j=s(88726),y=e=>{let{data:t,setData:s}=e,r=(0,v.P)(),d=u(),{data:y}=(0,g.j)(),[w,_]=(0,p.useRecoilState)(b.cf),[N,C]=(0,p.useRecoilState)(b.ZQ),[S,k]=(0,p.useRecoilState)(b.Kf),[I,E]=(0,p.useRecoilState)(b._0),[R,A]=(0,p.useRecoilState)(b.qY),P=(0,p.useRecoilValue)(h.loggedIn),M=(0,p.useSetRecoilState)(f.PC),z=(0,p.useSetRecoilState)(f.Qg),[L,T]=n.useState((null==t?void 0:t.voice)||""),[D,Z]=n.useState((null==t?void 0:t.voice_embedding)||""),[F,G]=n.useState((null==t?void 0:t.voice_url)||"");(0,n.useEffect)(()=>{s(e=>({...e,voice_consent:R}))},[R]);let U=e=>new Promise(t=>{let s=document.createElement("audio");s.src=URL.createObjectURL(e),s.onloadedmetadata=()=>{let e=s.duration;URL.revokeObjectURL(s.src),t(e<=30)}}),H=()=>{k(!0),E(""),setTimeout(()=>{_(null),A(!1),s(e=>({...e,voice_consent:!1})),C(0),k(!1)},500),""!==L&&""!==D&&s(e=>({...e,voice:L,voice_embedding:D,voice_url:F}))},O=async e=>{var t,a,l;try{if(!P){M({open:!0,isSignup:!0,text:"Sign Up to use Voice Cloning",image:"",gaClass:"ga-reg_popup_voice_cloning"});return}let a=await m.default.post("".concat(x.fw,"/check/feature"),{feature:"voice_cloning"},{headers:{Authorization:localStorage.getItem("access_token")}});if(2==a.data.code){z({open:!0,text:"Voice Cloning requires an Ultra or Max subscription!",image:"",type:"subscription",gaClass:"ga-sub_popup_voice_cloning_character_creation"});return}let l=null===(t=e.target.files)||void 0===t?void 0:t[0];if(l){if(!await U(l)){j.default.error("Audio duration exceeds 30 seconds!");return}_(l);let e=new FormData;e.append("clip",l);let t=await d({clip:e});if(t){E(t.audio),s(e=>({...e,voice:"Custom",voice_embedding:t.msg,voice_url:""}));let e=setInterval(()=>{C(t=>{let s=t+10;return s>=100&&clearInterval(e),s})},200)}}}catch(e){j.default.error((null==e?void 0:null===(l=e.response)||void 0===l?void 0:null===(a=l.data)||void 0===a?void 0:a.msg)||"Error cloning voice"),H()}};return(0,a.jsxs)("div",{className:"flex flex-col justify-center items-center",children:[w?100===N&&(0,a.jsxs)(c.z,{"aria-label":"Play sample audio",onClick:()=>{let e=new Audio("data:audio/wav;base64,".concat(I));e?e.play().then(()=>{}).catch(e=>{console.error("Error playing audio:",e)}):console.error("Audio element not found")},children:[r.play," Play sample audio"]}):(0,a.jsx)("label",{htmlFor:"audio-file-input",className:"cursor-pointer",children:(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center sm:w-40 w-32 bg-background rounded-lg sm:py-8 py-4 sm:px-4 px-2 hover:bg-[#131313b5] transition-all ease-in-out duration-300  group cursor-pointer",children:[(0,a.jsx)(i.default,{src:"/icons/upload-simple.png",alt:"Upload",width:window.innerWidth<=640?16:24,height:"12"}),(0,a.jsxs)("p",{className:"sm:text-sm xs:text-[10px] text-[9px] xs:leading-snug leading-tight sm:tracking-wide text-muted-foreground text-center",children:["Upload your audio file here"," ",(0,a.jsx)("span",{className:"font-medium text-primary underline underline-offset-4 cursor-pointer transition-all ease-in-out duration-700",children:"browse"})]}),(0,a.jsx)("input",{id:"audio-file-input",type:"file",accept:"audio/mp3, audio/wav, audio/m4a",onChange:O,className:"hidden"})]})}),w&&(0,a.jsxs)("div",{className:"flex flex-col gap-2 items-center transition-opacity duration-500 ".concat(S?"opacity-0":"opacity-100"),children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center pt-2 ",children:[(0,a.jsx)("p",{className:"sm:text-sm xs:text-[10px] text-[9px] xs:leading-snug leading-tight sm:tracking-wide text-muted-foreground  ".concat(N<100?"blink":""),children:N<100?"Uploading File:":"Upload complete"}),(0,a.jsx)("p",{className:"sm:text-sm xs:text-[10px] text-[9px] xs:leading-snug leading-tight sm:tracking-wide font-extralight text-xs",children:w.name}),(0,a.jsx)(c.z,{"aria-label":"Remove Voice",variant:"link",onClick:H,className:"text-[#a1a1a1] font-light  underline hover:text-red-700 h-fit  pt-1.5 text-xs tracking-wide ",size:"sm",children:"Remove Voice"})]}),(0,a.jsx)(l.E,{value:N,className:"h-1 sm:w-full w-44  bg-background rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-primary transition-all"})}),(0,a.jsx)(o,{consent:R,setConsent:A})]})]})}},5207:function(e,t,s){"use strict";var a=s(57437),l=s(16463);t.default=e=>{let{icon:t,text:s}=e,i=(0,l.useRouter)();return(0,a.jsxs)("button",{"aria-label":s,onClick:()=>{switch(s){case"Start Chatting":i.push("/");break;case"Create Character":case"Create your partner":case"Start Creating":localStorage.removeItem("character"),i.push("/create");break;case"Go to Guide":i.push("/guide")}},className:"flex h-[40px] gap-2 justify-center max-w-fit      items-center px-3 rounded-xl bg-linear-to-b from-[#d52a5d] to-[#bc1f4e] hover:from-[#b02851] hover:to-[#b02851]",children:[t&&t,(0,a.jsx)("span",{className:"font-bold text-base text-nowrap",children:s})]})}},84019:function(e,t,s){"use strict";var a=s(57437),l=s(36013),i=s(66648),n=s(2265);t.Z=e=>{var t;let{filepath:s,CategoryName:r,size:o="full",selected:c,scale:d}=e,[u,m]=(0,n.useState)(!1);return(0,a.jsx)(l.Zb,{className:"max-w-52  ".concat("realistic"===r||"anime"===r?"cursor-not-allowed":"cursor-pointer"),onMouseEnter:()=>m(!0),onMouseLeave:()=>m(!1),children:(0,a.jsx)(l.aY,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{alt:"Character portrait",className:"".concat(o," aspect-square object-cover ").concat(d),width:"150",height:"150",src:s||""}),(0,a.jsx)("div",{"aria-label":"".concat(r," style character"),className:"flex flex-row justify-center items-center\n							absolute bottom-0 left-0 right-0  text-center\n							 bg-opacity-65 ".concat(o,"\n							").concat("realistic"===r||"anime"===r?c?"bg-[#d62a5e]":"bg-black":u||c?"bg-[#d62a5e]":"bg-black"," sm:text-md text-xs text-white tracking-wide xl:py-[3px]\n							sm:py-px py-[.5px]  \n							"),children:"string"!=typeof r?"":(null==r?void 0:null===(t=r.charAt(0))||void 0===t?void 0:t.toUpperCase())+(null==r?void 0:r.slice(1))})]})})})}},37762:function(e,t,s){"use strict";var a=s(57437),l=s(3114);t.Z=e=>{let{heading:t,color:s="white",size:i="base"}=e;return(0,l.P)(),(0,a.jsx)("div",{className:"flex flex-row justify-center items-center sm:pt-4 pt-3 sm:pb-1 pb-1",children:(0,a.jsx)("h3",{className:"font-semibold text-".concat(s," \n			").concat("base"==i?"sm:text-".concat(i," text-xs"):"text-".concat(i)," "),children:t})})}},62009:function(e,t,s){"use strict";var a=s(57437);s(2265);var l=s(26869),i=s(47921),n=s(83092),r=s(51531);t.Z=e=>{let{content:t}=e;return(0,a.jsx)(l.U,{remarkPlugins:[i.Z,n.Z],rehypePlugins:[r.Z],children:t})}},4686:function(e,t,s){"use strict";s.d(t,{Y:function(){return f}});var a=s(57437),l=s(2265);s(90837);var i=s(14738),n=s(16880),r=s(57218),o=s(38857),c=s(18207),d=s(31231),u=s(50495),m=s(86466);function x(e){let{user:t,onSave:s,onDontShowBillingPopupChange:i}=e;var x=(0,n.Z)();try{let[e,t]=(0,l.useState)({cardHolderName:"",cardBrand:"",cardNumber:"",cardExpirationDate:"",cardCvv:"",firstName:"",lastName:""}),n=r.paymentStore.value.billingInformation,[x,h]=(0,l.useState)({}),[p,f]=(0,l.useState)(!1),[g,v]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(0,r.$P)(e)},[e]),(0,l.useEffect)(()=>{null==i||i(g)},[g,i]);let b=()=>{let t={},s={cardNumber:e.cardNumber,cardExpirationDate:e.cardExpirationDate,cardCvv:e.cardCvv,firstName:n.firstName,lastName:n.lastName,email:n.email,address:n.address,city:n.city,state:n.state,countryShort:n.countryShort,pinCode:n.pinCode};return Object.entries(s).forEach(e=>{let[s,a]=e;a&&""!==a.trim()||(t[s]="".concat((0,d.Wy)(s)," is required."))}),h(t),0===Object.keys(t).length},j=async()=>{b()&&(f(!0),s({paymentInfo:e,billingInfo:n,dontShowBillingPopup:g}),f(!1))};return(0,a.jsxs)("div",{className:"relative flex flex-col gap-y-4",children:[p&&(0,a.jsx)(o.Z,{}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,a.jsx)(c.Z,{info:e,setInfo:t}),Object.entries(x).map(e=>{let[t,s]=e;return(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:s},t)})]}),(0,a.jsx)("div",{className:"flex flex-row w-full gap-x-2 mt-4",children:(0,a.jsx)(u.z,{className:"rounded-full w-full",variant:"default",disabled:p,onClick:j,children:"Save"})}),(0,a.jsxs)("div",{className:"flex flex-row w-full gap-x-2 mt-4",children:[(0,a.jsx)(m.X,{id:"dont-show-again",checked:g,onCheckedChange:()=>{v(!g)}}),(0,a.jsx)("label",{htmlFor:"dont-show-again",className:"text-sm font-medium text-gray-500",children:"Don't show this again"})]})]})}finally{x.f()}}var h=s(74697),p=s(53130);let f=e=>{var t;let{open:s,setOpen:n,title:o,onSave:c,onClose:d}=e,u=(0,l.useRef)(null),{data:m}=(0,i.j)(),{mutate:f}=(0,p.I)(),[g,v]=(0,l.useState)(!1),b=()=>{n(!1),g&&f({updates:{dontShowBillingPopup:!0}}),null==d||d()};return t=()=>b(),(0,l.useEffect)(()=>{let e=e=>{!u.current||u.current.contains(e.target)||t(e)};return document.addEventListener("mousedown",e),document.addEventListener("touchstart",e),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("touchstart",e)}},[u,t]),(0,l.useEffect)(()=>(s?document.body.style.overflow="hidden":document.body.style.overflow="auto",()=>{document.body.style.overflow="auto"}),[s]),(0,l.useEffect)(()=>{var e,t;(0,r.sw)({firstName:null==m?void 0:null===(e=m.user_nickname)||void 0===e?void 0:e.split(" ")[0],lastName:null==m?void 0:null===(t=m.user_nickname)||void 0===t?void 0:t.split(" ")[1],email:null==m?void 0:m.email})},[m]),(0,a.jsxs)("div",{className:"fixed inset-0 z-60",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/50 z-10",onClick:()=>{console.log("overlay clicked"),b()}}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[1001] w-full max-w-xl px-4",onClick:e=>e.stopPropagation(),children:(0,a.jsxs)("div",{className:"relative flex flex-col bg-white dark:bg-neutral-900 rounded-md shadow-lg max-h-[90vh] md:max-h-[85vh]",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 pb-4 flex-shrink-0",children:[(0,a.jsx)("h1",{className:"text-xl md:text-2xl font-bold text-primary text-center flex-1 pr-8",children:o}),(0,a.jsx)("button",{onClick:()=>b(),className:"flex-shrink-0 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200","aria-label":"Close modal",children:(0,a.jsx)(h.Z,{size:20,className:"text-gray-500 dark:text-gray-400"})})]}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto relative px-6 pb-6 custom-scrollbar",children:(0,a.jsx)(x,{user:m,onSave:c,onDontShowBillingPopupChange:v})})]})})]})}},94693:function(e,t,s){"use strict";s.d(t,{C:function(){return r}});var a=s(57437);s(2265);var l=s(12218),i=s(13498);let n=(0,l.j)("inline-flex items-center rounded-full px-3 py-1 text-xs transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{promptSuggestion:"bg-card hover:bg-background rounded-lg px-3 text-xs xs:text-sm",transparent:"bg-black/40",default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"transparent"}});function r(e){let{className:t,variant:s,...l}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:s}),t),...l})}},36013:function(e,t,s){"use strict";s.d(t,{Ol:function(){return r},Zb:function(){return n},aY:function(){return c},eW:function(){return d},ll:function(){return o}});var a=s(57437),l=s(2265),i=s(13498);let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("transition-shadow rounded-lg overflow-hidden bg-card text-card-foreground shadow-lg",s),...l})});n.displayName="Card";let r=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-2 p-4",s),...l})});r.displayName="CardHeader";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("text-lg font-bold leading-none",s),...l})});o.displayName="CardTitle",l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-xs text-muted-foreground",s),...l})}).displayName="CardDescription";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("",s),...l})});c.displayName="CardContent";let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("w-full bg-card font-bold py-1.5 flex items-center justify-center",s),...l})});d.displayName="CardFooter"},70571:function(e,t,s){"use strict";s.d(t,{E:function(){return r}});var a=s(57437),l=s(2265),i=s(73730),n=s(13498);let r=l.forwardRef((e,t)=>{let{className:s,value:l,...r}=e;return(0,a.jsx)(i.fC,{ref:t,className:(0,n.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",s),...r,children:(0,a.jsx)(i.z$,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(l||0),"%)")}})})});r.displayName=i.fC.displayName},19024:function(e,t,s){"use strict";s.d(t,{S:function(){return n},c:function(){return r}});var a=s(57437),l=s(2265);let i=(0,l.createContext)({displayChars:[],setDisplayChars:()=>{}});function n(e){let{children:t}=e,[s,n]=(0,l.useState)([]);return(0,a.jsx)(i.Provider,{value:{displayChars:s,setDisplayChars:n},children:t})}function r(){return(0,l.useContext)(i)}},7432:function(e,t,s){"use strict";s.d(t,{D:function(){return o},d:function(){return c}});var a=s(57437),l=s(2265),i=s(99441),n=s(66511);let r=(0,l.createContext)(void 0),o=e=>{let{children:t}=e,[s,o]=(0,l.useState)({}),[c,d]=(0,l.useState)(""),[u,m]=(0,i.useRecoilState)(n.loggedIn);return(0,l.useEffect)(()=>{localStorage.getItem("access_token")&&m(!0)},[]),(0,a.jsx)(r.Provider,{value:{modalState:s,openModal:e=>{o(t=>({...t,[c]:!1,[e]:!0})),d(e)},closeModal:e=>{o(t=>({...t,[e]:!1}))},activeModal:c},children:t})},c=()=>{let e=(0,l.useContext)(r);if(!e)throw Error("useModal must be used within a ModalProvider");return e}},72224:function(e,t,s){"use strict";s.d(t,{N:function(){return r}});var a=s(76351),l=s(22351),i=s(99441),n=s(98280);let r=()=>{let e=(0,i.useSetRecoilState)(n.n8),t=(0,i.useSetRecoilState)(n.jo),s=(0,i.useSetRecoilState)(n.gn),r=(0,i.useSetRecoilState)(n.hE),o=async()=>{try{var a,i,n,o,c;let d=(await l.j.get("/character_options/")).data,u={Female:{Style:{Realistic:d.female_real.path,Anime:d.female_anime.path},Realistic:{age:"",...d.female_real.options},Anime:{age:"",...d.female_anime.options}},Male:{Style:{Realistic:d.male_real.path,Anime:d.male_anime.path},Realistic:{age:"",...d.male_real.options},Anime:{age:"",...d.male_anime.options}},hair_colors:d.hair_colors},m={relationships:{Male:null==d?void 0:null===(a=d.relationships)||void 0===a?void 0:a.filter(e=>"male"===e.gender),Female:null==d?void 0:null===(i=d.relationships)||void 0===i?void 0:i.filter(e=>"female"===e.gender),Neutral:null==d?void 0:null===(n=d.relationships)||void 0===n?void 0:n.filter(e=>"neutral"===e.gender)},personalities:null==d?void 0:d.personalities,voices:{Male:null==d?void 0:null===(o=d.voices[0])||void 0===o?void 0:o.male_voices,Female:null==d?void 0:null===(c=d.voices[0])||void 0===c?void 0:c.female_voices}},x={hobbies:null==d?void 0:d.hobbies,kinks:null==d?void 0:d.kinks,work:null==d?void 0:d.occupations},h={...null==d?void 0:d.review_page_icons};return e(u),t(m),s(x),r(h),{looks_data:u,personality_data:m,lifestyle_data:x,review_page_icons:h}}catch(e){console.log(e,"error")}};return(0,a.useQuery)({queryKey:["createOptions"],queryFn:o,staleTime:1/0,gcTime:1/0})}},59181:function(e,t,s){"use strict";s.d(t,{U:function(){return i}});var a=s(22351),l=s(76351);let i=()=>{let e=async()=>{try{return(await a.j.get("/character/public")).data}catch(e){}};return(0,l.useQuery)({queryKey:["public-chars"],queryFn:e,refetchOnWindowFocus:!1,staleTime:6e4,gcTime:18e5})}},69509:function(e,t,s){"use strict";s.d(t,{K:function(){return m}});var a=s(85499),l=s(22351),i=s(88726),n=s(99441),r=s(80217),o=s(25524),c=s(20708),d=s(61349),u=s(14738);let m=()=>{let e=(0,c.l)()({}),[t,s]=(0,n.useRecoilState)(a._A),m=(0,n.useSetRecoilState)(a.Md),{data:x}=(0,u.j)();(0,d.m)(x);let h=async a=>{let{image:n}=a;s(e=>({...e,src:""}));try{let t=await l.j.post("/char_image_gen/regenerate",{...e,ImagePrompt:n.image_prompt,img_id:n._id});t.data.runId&&((0,r.$C)({charId:null==e?void 0:e._id,prevImgId:n._id,data:{_id:t.data.newImage._id,created_date:t.data.newImage.created_date,imageUrl:"",videoUrl:"",imageType:"generated",image_prompt:n.image_prompt,live_photo:!1,current_step:0,progress:25,run_id:t.data.runId,status:"pending",step_name:""}}),s(e=>({...e,id:t.data.newImage._id||""})))}catch(e){var o,c,d;(null===(o=e.response)||void 0===o?void 0:o.status)===422?m(e.response.data.bannedWords):i.default.error((null==e?void 0:null===(d=e.response)||void 0===d?void 0:null===(c=d.data)||void 0===c?void 0:c.msg)||"Error regenerating image"),s(t)}};return(0,o.useMutation)({mutationFn:e=>{let{image:t}=e;return h({image:t})}})}},87497:function(e,t,s){"use strict";s.d(t,{L:function(){return f}});var a=s(16463),l=s(59181),i=s(88726),n=s(38472),r=s(13498),o=s(14738),c=s(85499),d=s(99441),u=s(22351),m=s(66511),x=s(34084),h=s(93191),p=s(67374);let f=()=>{let e=(0,a.useRouter)(),t=(0,a.useSearchParams)(),{data:s}=(0,o.j)(),{data:f}=(0,l.U)(),g=(0,d.useSetRecoilState)(c.$d),v=(0,h.useQueryClient)(),b=(0,d.useSetRecoilState)(x.AL),j=async()=>{try{return(await u.j.post("/character/user_characters",{last_char_id:""})).data}catch(e){}},y=a=>{let{charId:l,redirectTo:i}=a;l&&localStorage.setItem("sd_active_char_id",l),"chat"===i?(v.invalidateQueries({queryKey:["user-chars",null==s?void 0:s._id]}),e.push("/chat")):"generator"===i?(null==t?void 0:t.has("image-option"))?e.push("/generator/ai-images?image-option=".concat((null==t?void 0:t.get("image-option"))||"Action")):e.push("/generator/ai-images?image-option=Action"):e.push("/")},w=(0,d.useSetRecoilState)(c.PC),_=(0,d.useSetRecoilState)(c.Qg),N=(0,d.useRecoilState)(m.loggedIn)[0],C=(0,p.a)(),S=async e=>{let{charId:t,isPrivateChar:s}=e;try{let e=await j(),s=null==e?void 0:e.msg.find(e=>e._id===t);if(!N){w(()=>({open:!0,isSignup:!0,text:"Sign up to call ".concat(null==s?void 0:s.first_name,"!"),image:"".concat(null==s?void 0:s.initial_image),gaClass:"ga-reg_popup_calling"}));return}i.default.loading("Calling now ...",{id:"call"});let l=await n.default.post("".concat(r.fw,"/check/check_call"),{},{headers:{Authorization:localStorage.getItem("access_token")||""}});if(i.default.dismiss("call"),0==l.data.code){_({open:!0,text:"Upgrade to call ".concat(null==s?void 0:s.first_name,"!"),image:"".concat(null==s?void 0:s.initial_image),type:"subscription",gaClass:"ga-sub_popup_calling"});return}if(2==l.data.code){_({open:!0,text:"Grab Hearts to call ".concat(null==s?void 0:s.first_name,"!"),image:"".concat(null==s?void 0:s.initial_image),type:"hearts"});return}if(s){var a;localStorage.setItem("sd_active_char_id",t||""),b(!1),g({open:!0,receivingCall:!1,userCalling:!0,text:s.first_name||"",image:null!==(a=s.initial_image)&&void 0!==a?a:""})}}catch(e){i.default.error("Something went wrong. Please try again later.")}};return async e=>{let{charId:t,redirectTo:a,isPublicChar:l}=e;if(!l&&"homepage"!==a){y({charId:t,redirectTo:a});return}try{var n;let e=null==s?void 0:null===(n=s.conv_history)||void 0===n?void 0:n.find(e=>e.parent_char_id===t);if(e&&"homepage"!==a){y({charId:e.char_id,redirectTo:a});return}if(e&&"homepage"===a){S({charId:e.char_id,isPrivateChar:!0});return}let l=null==f?void 0:f.find(e=>e._id===t);if(!l){i.default.error("Public character not found!");return}let r=localStorage.getItem("access_token")||localStorage.getItem("visitor_token");if(!r){(0,i.default)("Please log in again"),C();return}i.default.loading("Please wait while ".concat(l.first_name," is getting ready for you..."),{id:"cloningChar"});let o=(await u.j.post("/character/private",l)).data;await v.invalidateQueries({queryKey:["user",r]}),await v.invalidateQueries({queryKey:["my-chars",null==s?void 0:s._id]}),await v.invalidateQueries({queryKey:["user-chars",null==s?void 0:s._id]}),"homepage"!==a&&y({charId:o.id,redirectTo:a}),await v.invalidateQueries({queryKey:["user"]}),await v.invalidateQueries({queryKey:["my-chars",null==s?void 0:s._id]}),await v.invalidateQueries({queryKey:["user-chars",null==s?void 0:s._id]}),"homepage"===a&&S({charId:o.id,isPrivateChar:!1})}catch(e){console.error(e)}finally{i.default.dismiss("cloningChar")}}}},67374:function(e,t,s){"use strict";s.d(t,{a:function(){return d}});var a=s(75735),l=s(39952),i=s(99441),n=s(66511),r=s(88726),o=s(16463),c=s(22351);let d=()=>{let e=(0,i.useSetRecoilState)(n.loggedIn),t=(0,o.useSearchParams)();return async()=>{await (0,a.w7)(l.I),localStorage.removeItem("access_token"),localStorage.removeItem("pref"),localStorage.removeItem("character_id"),localStorage.removeItem("character"),localStorage.removeItem("timezone"),e(!1),c.E.clear(),r.default.success("Logout Successful"),t.has("invite")?window.location.href="/?invite=".concat(t.get("invite")):window.location.href="/"}}},99010:function(e,t,s){"use strict";s.d(t,{n:function(){return i}});var a=s(2265),l=s(61349);let i=()=>{let[e,t]=(0,a.useState)([]),{addPendingGeneration:s,removePendingGeneration:i}=(0,l.s)();return(0,a.useEffect)(()=>{t(JSON.parse(localStorage.getItem("pendingRunIds")||"[]"))},[]),(0,a.useEffect)(()=>{let e=()=>{t(JSON.parse(localStorage.getItem("pendingRunIds")||"[]"))};window.addEventListener("storage",e);let s=setInterval(e,1e3);return()=>{window.removeEventListener("storage",e),clearInterval(s)}},[]),{pendingGenerations:e,addPendingGeneration:e=>{e&&(s(e),t(t=>t.includes(e)?t:[...t,e]))},removePendingGeneration:e=>{e&&(i(e),t(t=>t.filter(t=>t!==e)))},hasPendingGenerations:e.length>0}}},15964:function(e,t,s){"use strict";var a=s(2265),l=s(16463),i=s(14738),n=s(86460),r=s(99441),o=s(31231);t.Z=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,l.useRouter)(),s=(0,l.usePathname)(),c=(0,l.useSearchParams)(),{data:d}=(0,i.j)(),u=(0,a.useRef)(!0),m=(0,r.useSetRecoilState)(n.q),[x,h]=(0,a.useState)(()=>{let t={...e};return c.forEach((s,a)=>{let l=e[a];Array.isArray(l)||"tags"===a?t[a]=s?s.split(","):[]:"boolean"==typeof l?t[a]="true"===s:isNaN(Number(s))||""===s?"true"===s?t[a]=!0:"false"===s?t[a]=!1:t[a]=s:t[a]=Number(s)}),t});(0,a.useEffect)(()=>{if(u.current&&d){let e={...x};"Visitor"!==d.role&&(e.nsfw=e.nsfw||d.nsfw_preference,e.gender=e.gender||(0,o.LF)(d.gender_preference||"Both"),e.style=e.style||(0,o.LF)(d.style_preference||"Both"),u.current=!1,p(e,!0),m(e))}},[d,x]),(0,a.useEffect)(()=>{u.current||m(x)},[x]);let p=(0,a.useCallback)(function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l="function"==typeof e?e(x):e;if(a){h(l);return}let i=new URLSearchParams;Object.entries(l).forEach(e=>{let[t,s]=e;null==s||""===s||(Array.isArray(s)&&s.length>0?i.set(t,s.join(",")):Array.isArray(s)||i.set(t,String(s)))}),t.replace("".concat(s,"?").concat(i.toString())),h(l)},[x,s,t]);return{filter:x,setFilter:p}}},67910:function(e,t,s){"use strict";s.d(t,{Kf:function(){return n},ZQ:function(){return i},_0:function(){return r},cf:function(){return l},qY:function(){return o}});var a=s(99441);let l=(0,a.atom)({key:"fileAtom",default:null}),i=(0,a.atom)({key:"progressAtom",default:0}),n=(0,a.atom)({key:"isRemovingAtom",default:!1}),r=(0,a.atom)({key:"base64StringAtom",default:""}),o=(0,a.atom)({key:"consentAtom",default:!1})},86460:function(e,t,s){"use strict";s.d(t,{q:function(){return a}});let a=(0,s(99441).atom)({key:"homePageFiltersAtom",default:{}})},66519:function(e,t,s){"use strict";var a=s(38472),l=s(13498);async function i(){try{return(await a.default.get("".concat(l.fw,"/subscription/tiers"))).data}catch(e){return[]}}t.Z=i},56252:function(){}},function(e){e.O(0,[2454,1810,691,4898,9571,2516,4753,9842,4868,8472,8726,6015,9058,6648,3850,5901,3892,5287,4999,784,9183,7071,231,939,3858,1220,8972,7010,5687,9296,694,9322,3560,2215,4041,3340,2228,8795,2971,7023,1744],function(){return e(e.s=48938)}),_N_E=e.O()}]);