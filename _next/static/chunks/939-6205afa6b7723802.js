"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[939],{74697:function(e,r,t){t.d(r,{Z:function(){return n}});/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(78030).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},44458:function(e,r,t){t.d(r,{Ee:function(){return b},F$:function(){return g},NY:function(){return x},Q5:function(){return h},fC:function(){return w},qE:function(){return m}});var n=t(2265),o=t(98324),l=t(75137),i=t(1336),u=t(25171),a=t(57437),f="Avatar",[s,c]=(0,o.b)(f),[d,p]=s(f),m=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...o}=e,[l,i]=n.useState("idle");return(0,a.jsx)(d,{scope:t,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,a.jsx)(u.WV.span,{...o,ref:r})})});m.displayName=f;var v="AvatarImage",g=n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:o,onLoadingStatusChange:f=()=>{},...s}=e,c=p(v,t),d=function(e){let[r,t]=n.useState("idle");return(0,i.b)(()=>{if(!e){t("error");return}let r=!0,n=new window.Image,o=e=>()=>{r&&t(e)};return t("loading"),n.onload=o("loaded"),n.onerror=o("error"),n.src=e,()=>{r=!1}},[e]),r}(o),m=(0,l.W)(e=>{f(e),c.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==d&&m(d)},[d,m]),"loaded"===d?(0,a.jsx)(u.WV.img,{...s,ref:r,src:o}):null});g.displayName=v;var y="AvatarFallback",h=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:o,...l}=e,i=p(y,t),[f,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),f&&"loaded"!==i.imageLoadingStatus?(0,a.jsx)(u.WV.span,{...l,ref:r}):null});h.displayName=y;var w=m,b=g,x=h},87513:function(e,r,t){t.d(r,{gm:function(){return l}});var n=t(2265);t(57437);var o=n.createContext(void 0);function l(e){let r=n.useContext(o);return e||r||"ltr"}},69081:function(e,r,t){t.d(r,{ck:function(){return Y},fC:function(){return X}});var n=t(2265),o=t(57437);function l(e,r=[]){let t=[],l=()=>{let r=t.map(e=>n.createContext(e));return function(t){let o=t?.[e]||r;return n.useMemo(()=>({[`__scope${e}`]:{...t,[e]:o}}),[t,o])}};return l.scopeName=e,[function(r,l){let i=n.createContext(l),u=t.length;t=[...t,l];let a=r=>{let{scope:t,children:l,...a}=r,f=t?.[e]?.[u]||i,s=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(f.Provider,{value:s,children:l})};return a.displayName=r+"Provider",[a,function(t,o){let a=o?.[e]?.[u]||i,f=n.useContext(a);if(f)return f;if(void 0!==l)return l;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return n.useMemo(()=>({[`__scope${r.scopeName}`]:o}),[o])}};return t.scopeName=r.scopeName,t}(l,...r)]}function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function u(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function a(...e){return n.useCallback(u(...e),e)}t(54887);var f=n.forwardRef((e,r)=>{let{children:t,...l}=e,i=n.Children.toArray(t),u=i.find(d);if(u){let e=u.props.children,t=i.map(r=>r!==u?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(s,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,o.jsx)(s,{...l,ref:r,children:t})});f.displayName="Slot";var s=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref;return n.cloneElement(t,{...function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props),ref:r?u(r,i):i})}return n.Children.count(t)>1?n.Children.only(null):null});s.displayName="SlotClone";var c=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===c}var p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...l}=e,i=n?f:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i,{...l,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function m(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}var v=t(53201),g=t(75137),y=t(91715),h=t(87513),w="rovingFocusGroup.onEntryFocus",b={bubbles:!1,cancelable:!0},x="RovingFocusGroup",[R,C,j]=function(e){let r=e+"CollectionProvider",[t,i]=l(r),[u,s]=t(r,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:r,children:t}=e,l=n.useRef(null),i=n.useRef(new Map).current;return(0,o.jsx)(u,{scope:r,itemMap:i,collectionRef:l,children:t})};c.displayName=r;let d=e+"CollectionSlot",p=n.forwardRef((e,r)=>{let{scope:t,children:n}=e,l=a(r,s(d,t).collectionRef);return(0,o.jsx)(f,{ref:l,children:n})});p.displayName=d;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=n.forwardRef((e,r)=>{let{scope:t,children:l,...i}=e,u=n.useRef(null),c=a(r,u),d=s(m,t);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...i}),()=>void d.itemMap.delete(u))),(0,o.jsx)(f,{[v]:"",ref:c,children:l})});return g.displayName=m,[{Provider:c,Slot:p,ItemSlot:g},function(r){let t=s(e+"CollectionConsumer",r);return n.useCallback(()=>{let e=t.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(t.itemMap.values()).sort((e,t)=>r.indexOf(e.ref.current)-r.indexOf(t.ref.current))},[t.collectionRef,t.itemMap])},i]}(x),[E,I]=l(x,[j]),[N,S]=E(x),A=n.forwardRef((e,r)=>(0,o.jsx)(R.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(R.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,o.jsx)(D,{...e,ref:r})})}));A.displayName=x;var D=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:i=!1,dir:u,currentTabStopId:f,defaultCurrentTabStopId:s,onCurrentTabStopIdChange:c,onEntryFocus:d,preventScrollOnEntryFocus:v=!1,...x}=e,R=n.useRef(null),j=a(r,R),E=(0,h.gm)(u),[I=null,S]=(0,y.T)({prop:f,defaultProp:s,onChange:c}),[A,D]=n.useState(!1),T=(0,g.W)(d),_=C(t),k=n.useRef(!1),[F,M]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(w,T),()=>e.removeEventListener(w,T)},[T]),(0,o.jsx)(N,{scope:t,orientation:l,dir:E,loop:i,currentTabStopId:I,onItemFocus:n.useCallback(e=>S(e),[S]),onItemShiftTab:n.useCallback(()=>D(!0),[]),onFocusableItemAdd:n.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>M(e=>e-1),[]),children:(0,o.jsx)(p.div,{tabIndex:A||0===F?-1:0,"data-orientation":l,...x,ref:j,style:{outline:"none",...e.style},onMouseDown:m(e.onMouseDown,()=>{k.current=!0}),onFocus:m(e.onFocus,e=>{let r=!k.current;if(e.target===e.currentTarget&&r&&!A){let r=new CustomEvent(w,b);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=_().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),v)}}k.current=!1}),onBlur:m(e.onBlur,()=>D(!1))})})}),T="RovingFocusGroupItem",_=n.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:t,focusable:l=!0,active:i=!1,tabStopId:u,...a}=e,f=(0,v.M)(),s=u||f,c=S(T,t),d=c.currentTabStopId===s,g=C(t),{onFocusableItemAdd:y,onFocusableItemRemove:h}=c;return n.useEffect(()=>{if(l)return y(),()=>h()},[l,y,h]),(0,o.jsx)(R.ItemSlot,{scope:t,id:s,focusable:l,active:i,children:(0,o.jsx)(p.span,{tabIndex:d?0:-1,"data-orientation":c.orientation,...a,ref:r,onMouseDown:m(e.onMouseDown,e=>{l?c.onItemFocus(s):e.preventDefault()}),onFocus:m(e.onFocus,()=>c.onItemFocus(s)),onKeyDown:m(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){c.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,t){var n;let o=(n=e.key,"rtl"!==t?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return k[o]}(e,c.orientation,c.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)o.reverse();else if("prev"===r||"next"===r){var t,n;"prev"===r&&o.reverse();let l=o.indexOf(e.currentTarget);o=c.loop?(t=o,n=l+1,t.map((e,r)=>t[(n+r)%t.length])):o.slice(l+1)}setTimeout(()=>P(o))}})})})});_.displayName=T;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let n of e)if(n===t||(n.focus({preventScroll:r}),document.activeElement!==t))return}var F=t(59479),M="ToggleGroup",[U,V]=l(M,[I]),W=I(),G=n.forwardRef((e,r)=>{let{type:t,...n}=e;if("single"===t)return(0,o.jsx)($,{...n,ref:r});if("multiple"===t)return(0,o.jsx)(K,{...n,ref:r});throw Error("Missing prop `type` expected on `".concat(M,"`"))});G.displayName=M;var[O,L]=U(M),$=n.forwardRef((e,r)=>{let{value:t,defaultValue:l,onValueChange:i=()=>{},...u}=e,[a,f]=(0,y.T)({prop:t,defaultProp:l,onChange:i});return(0,o.jsx)(O,{scope:e.__scopeToggleGroup,type:"single",value:a?[a]:[],onItemActivate:f,onItemDeactivate:n.useCallback(()=>f(""),[f]),children:(0,o.jsx)(q,{...u,ref:r})})}),K=n.forwardRef((e,r)=>{let{value:t,defaultValue:l,onValueChange:i=()=>{},...u}=e,[a=[],f]=(0,y.T)({prop:t,defaultProp:l,onChange:i}),s=n.useCallback(e=>f(function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...r,e]}),[f]),c=n.useCallback(e=>f(function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return r.filter(r=>r!==e)}),[f]);return(0,o.jsx)(O,{scope:e.__scopeToggleGroup,type:"multiple",value:a,onItemActivate:s,onItemDeactivate:c,children:(0,o.jsx)(q,{...u,ref:r})})});G.displayName=M;var[Z,B]=U(M),q=n.forwardRef((e,r)=>{let{__scopeToggleGroup:t,disabled:n=!1,rovingFocus:l=!0,orientation:i,dir:u,loop:a=!0,...f}=e,s=W(t),c=(0,h.gm)(u),d={role:"group",dir:c,...f};return(0,o.jsx)(Z,{scope:t,rovingFocus:l,disabled:n,children:l?(0,o.jsx)(A,{asChild:!0,...s,orientation:i,dir:c,loop:a,children:(0,o.jsx)(p.div,{...d,ref:r})}):(0,o.jsx)(p.div,{...d,ref:r})})}),z="ToggleGroupItem",H=n.forwardRef((e,r)=>{let t=L(z,e.__scopeToggleGroup),l=B(z,e.__scopeToggleGroup),i=W(e.__scopeToggleGroup),u=t.value.includes(e.value),a=l.disabled||e.disabled,f={...e,pressed:u,disabled:a},s=n.useRef(null);return l.rovingFocus?(0,o.jsx)(_,{asChild:!0,...i,focusable:!a,active:u,ref:s,children:(0,o.jsx)(Q,{...f,ref:r})}):(0,o.jsx)(Q,{...f,ref:r})});H.displayName=z;var Q=n.forwardRef((e,r)=>{let{__scopeToggleGroup:t,value:n,...l}=e,i=L(z,t),u={role:"radio","aria-checked":e.pressed,"aria-pressed":void 0},a="single"===i.type?u:void 0;return(0,o.jsx)(F.Z,{...a,...l,ref:r,onPressedChange:e=>{e?i.onItemActivate(n):i.onItemDeactivate(n)}})}),X=G,Y=H},59479:function(e,r,t){t.d(r,{f:function(){return p},Z:function(){return d}});var n=t(2265),o=t(91715);function l(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}t(54887);var i=t(57437),u=n.forwardRef((e,r)=>{let{children:t,...o}=e,l=n.Children.toArray(t),u=l.find(s);if(u){let e=u.props.children,t=l.map(r=>r!==u?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(a,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,t):null})}return(0,i.jsx)(a,{...o,ref:r,children:t})});u.displayName="Slot";var a=n.forwardRef((e,r)=>{let{children:t,...o}=e;if(n.isValidElement(t)){let e,i;let u=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref;return n.cloneElement(t,{...function(e,r){let t={...r};for(let n in r){let o=e[n],l=r[n];/^on[A-Z]/.test(n)?o&&l?t[n]=(...e)=>{l(...e),o(...e)}:o&&(t[n]=o):"style"===n?t[n]={...o,...l}:"className"===n&&(t[n]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props),ref:r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=l(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():l(e[r],null)}}}}(r,u):u})}return n.Children.count(t)>1?n.Children.only(null):null});a.displayName="SlotClone";var f=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function s(e){return n.isValidElement(e)&&e.type===f}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=n.forwardRef((e,t)=>{let{asChild:n,...o}=e,l=n?u:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l,{...o,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{}),d=n.forwardRef((e,r)=>{let{pressed:t,defaultPressed:n=!1,onPressedChange:l,...u}=e,[a=!1,f]=(0,o.T)({prop:t,onChange:l,defaultProp:n});return(0,i.jsx)(c.button,{type:"button","aria-pressed":a,"data-state":a?"on":"off","data-disabled":e.disabled?"":void 0,...u,ref:r,onClick:function(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}(e.onClick,()=>{e.disabled||f(!a)})})});d.displayName="Toggle";var p=d},28590:function(e,r,t){let n;t.d(r,{Z:function(){return u}});var o={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let l=new Uint8Array(16),i=[];for(let e=0;e<256;++e)i.push((e+256).toString(16).slice(1));var u=function(e,r,t){if(o.randomUUID&&!r&&!e)return o.randomUUID();let u=(e=e||{}).random??e.rng?.()??function(){if(!n){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");n=crypto.getRandomValues.bind(crypto)}return n(l)}();if(u.length<16)throw Error("Random bytes length must be >= 16");if(u[6]=15&u[6]|64,u[8]=63&u[8]|128,r){if((t=t||0)<0||t+16>r.length)throw RangeError(`UUID byte range ${t}:${t+15} is out of buffer bounds`);for(let e=0;e<16;++e)r[t+e]=u[e];return r}return function(e,r=0){return(i[e[r+0]]+i[e[r+1]]+i[e[r+2]]+i[e[r+3]]+"-"+i[e[r+4]]+i[e[r+5]]+"-"+i[e[r+6]]+i[e[r+7]]+"-"+i[e[r+8]]+i[e[r+9]]+"-"+i[e[r+10]]+i[e[r+11]]+i[e[r+12]]+i[e[r+13]]+i[e[r+14]]+i[e[r+15]]).toLowerCase()}(u)}}}]);