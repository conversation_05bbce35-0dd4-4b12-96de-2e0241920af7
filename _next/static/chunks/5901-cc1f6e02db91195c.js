"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5901],{78030:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(2265);/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),u=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ")};/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:l="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:n,strokeWidth:o?24*Number(a)/Number(i):a,className:u("lucide",l),...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let n=(0,r.forwardRef)((n,s)=>{let{className:o,...l}=n;return(0,r.createElement)(a,{ref:s,iconNode:t,className:u("lucide-".concat(i(e)),o),...l})});return n.displayName="".concat(e),n}},3274:function(e,t,n){n.d(t,{Z:function(){return r}});/**
 * @license lucide-react v0.379.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(78030).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},78149:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},1584:function(e,t,n){n.d(t,{F:function(){return i},e:function(){return u}});var r=n(2265);function i(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function u(...e){return r.useCallback(i(...e),e)}},98324:function(e,t,n){n.d(t,{b:function(){return s},k:function(){return u}});var r=n(2265),i=n(57437);function u(e,t){let n=r.createContext(t);function u(e){let{children:t,...u}=e,s=r.useMemo(()=>u,Object.values(u));return(0,i.jsx)(n.Provider,{value:s,children:t})}return u.displayName=e+"Provider",[u,function(i){let u=r.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let n=[],u=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return u.scopeName=e,[function(t,u){let s=r.createContext(u),a=n.length;function o(t){let{scope:n,children:u,...o}=t,l=n?.[e][a]||s,c=r.useMemo(()=>o,Object.values(o));return(0,i.jsx)(l.Provider,{value:c,children:u})}return n=[...n,u],o.displayName=t+"Provider",[o,function(n,i){let o=i?.[e][a]||s,l=r.useContext(o);if(l)return l;if(void 0!==u)return u;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(u,...t)]}},53938:function(e,t,n){n.d(t,{XB:function(){return f}});var r,i=n(2265),u=n(78149),s=n(25171),a=n(1584),o=n(75137),l=n(57437),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:p,onPointerDownOutside:v,onFocusOutside:b,onInteractOutside:g,onDismiss:C,...E}=e,w=i.useContext(d),[O,q]=i.useState(null),D=null!==(f=null==O?void 0:O.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,P]=i.useState({}),Q=(0,a.e)(t,e=>q(e)),x=Array.from(w.layers),[A]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),M=x.indexOf(A),N=O?x.indexOf(O):-1,k=w.layersWithOutsidePointerEventsDisabled.size>0,R=N>=M,T=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,o.W)(e),u=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!u.current){let t=function(){y("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",s.current),s.current=t,n.addEventListener("click",s.current,{once:!0})):t()}else n.removeEventListener("click",s.current);u.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",s.current)}},[n,r]),{onPointerDownCapture:()=>u.current=!0}}(e=>{let t=e.target,n=[...w.branches].some(e=>e.contains(t));!R||n||(null==v||v(e),null==g||g(e),e.defaultPrevented||null==C||C())},D),S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,o.W)(e),u=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!u.current&&y("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>u.current=!0,onBlurCapture:()=>u.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(null==b||b(e),null==g||g(e),e.defaultPrevented||null==C||C())},D);return!function(e,t=globalThis?.document){let n=(0,o.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N!==w.layers.size-1||(null==p||p(e),!e.defaultPrevented&&C&&(e.preventDefault(),C()))},D),i.useEffect(()=>{if(O)return m&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(r=D.body.style.pointerEvents,D.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(O)),w.layers.add(O),h(),()=>{m&&1===w.layersWithOutsidePointerEventsDisabled.size&&(D.body.style.pointerEvents=r)}},[O,D,m,w]),i.useEffect(()=>()=>{O&&(w.layers.delete(O),w.layersWithOutsidePointerEventsDisabled.delete(O),h())},[O,w]),i.useEffect(()=>{let e=()=>P({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,l.jsx)(s.WV.div,{...E,ref:Q,style:{pointerEvents:k?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.M)(e.onFocusCapture,S.onFocusCapture),onBlurCapture:(0,u.M)(e.onBlurCapture,S.onBlurCapture),onPointerDownCapture:(0,u.M)(e.onPointerDownCapture,T.onPointerDownCapture)})});function h(){let e=new CustomEvent(c);document.dispatchEvent(e)}function y(e,t,n,r){let{discrete:i}=r,u=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&u.addEventListener(e,t,{once:!0}),i?(0,s.jH)(u,a):u.dispatchEvent(a)}f.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(d),r=i.useRef(null),u=(0,a.e)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(s.WV.div,{...e,ref:u})}).displayName="DismissableLayerBranch"},53201:function(e,t,n){n.d(t,{M:function(){return o}});var r,i=n(2265),u=n(1336),s=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function o(e){let[t,n]=i.useState(s());return(0,u.b)(()=>{e||n(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},56935:function(e,t,n){n.d(t,{h:function(){return o}});var r=n(2265),i=n(54887),u=n(25171),s=n(1336),a=n(57437),o=r.forwardRef((e,t)=>{var n,o;let{container:l,...c}=e,[d,f]=r.useState(!1);(0,s.b)(()=>f(!0),[]);let h=l||d&&(null===(o=globalThis)||void 0===o?void 0:null===(n=o.document)||void 0===n?void 0:n.body);return h?i.createPortal((0,a.jsx)(u.WV.div,{...c,ref:t}),h):null});o.displayName="Portal"},25171:function(e,t,n){n.d(t,{WV:function(){return a},jH:function(){return o}});var r=n(2265),i=n(54887),u=n(71538),s=n(57437),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...i}=e,a=r?u.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function o(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},71538:function(e,t,n){n.d(t,{A4:function(){return o},g7:function(){return s}});var r=n(2265),i=n(1584),u=n(57437),s=r.forwardRef((e,t)=>{let{children:n,...i}=e,s=r.Children.toArray(n),o=s.find(l);if(o){let e=o.props.children,n=s.map(t=>t!==o?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,u.jsx)(a,{...i,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,u.jsx)(a,{...i,ref:t,children:n})});s.displayName="Slot";var a=r.forwardRef((e,t)=>{let{children:n,...u}=e;if(r.isValidElement(n)){let e,s;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let i=e[r],u=t[r];/^on[A-Z]/.test(r)?i&&u?n[r]=(...e)=>{u(...e),i(...e)}:i&&(n[r]=i):"style"===r?n[r]={...i,...u}:"className"===r&&(n[r]=[i,u].filter(Boolean).join(" "))}return{...e,...n}}(u,n.props),ref:t?(0,i.F)(t,a):a})}return r.Children.count(n)>1?r.Children.only(null):null});a.displayName="SlotClone";var o=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});function l(e){return r.isValidElement(e)&&e.type===o}},75137:function(e,t,n){n.d(t,{W:function(){return i}});var r=n(2265);function i(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},91715:function(e,t,n){n.d(t,{T:function(){return u}});var r=n(2265),i=n(75137);function u({prop:e,defaultProp:t,onChange:n=()=>{}}){let[u,s]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[u]=n,s=r.useRef(u),a=(0,i.W)(t);return r.useEffect(()=>{s.current!==u&&(a(u),s.current=u)},[u,s,a]),n}({defaultProp:t,onChange:n}),a=void 0!==e,o=a?e:u,l=(0,i.W)(n);return[o,r.useCallback(t=>{if(a){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else s(t)},[a,e,s,l])]}},1336:function(e,t,n){n.d(t,{b:function(){return i}});var r=n(2265),i=globalThis?.document?r.useLayoutEffect:()=>{}},58421:function(e,t,n){n.d(t,{S:function(){return y}});var r=n(56298),i=n(2459),u=n(69948),s=n(49010),a=class extends s.l{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,n){let u=t.queryKey,s=t.queryHash??(0,r.Rm)(u,t),a=this.get(s);return a||(a=new i.A({cache:this,queryKey:u,queryHash:s,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(u)}),this.add(a)),a}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){u.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r._x)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r._x)(e,t)):t}notify(e){u.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){u.V.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){u.V.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},o=n(92812),l=class extends s.l{constructor(e={}){super(),this.config=e,this.#t=new Map,this.#n=Date.now()}#t;#n;build(e,t,n){let r=new o.m({mutationCache:this,mutationId:++this.#n,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){let t=c(e),n=this.#t.get(t)??[];n.push(e),this.#t.set(t,n),this.notify({type:"added",mutation:e})}remove(e){let t=c(e);if(this.#t.has(t)){let n=this.#t.get(t)?.filter(t=>t!==e);n&&(0===n.length?this.#t.delete(t):this.#t.set(t,n))}this.notify({type:"removed",mutation:e})}canRun(e){let t=this.#t.get(c(e))?.find(e=>"pending"===e.state.status);return!t||t===e}runNext(e){let t=this.#t.get(c(e))?.find(t=>t!==e&&t.state.isPaused);return t?.continue()??Promise.resolve()}clear(){u.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#t.values()].flat()}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.X7)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,r.X7)(e,t))}notify(e){u.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return u.V.batch(()=>Promise.all(e.map(e=>e.continue().catch(r.ZT))))}};function c(e){return e.options.scope?.id??String(e.mutationId)}var d=n(34939),f=n(49937),h=n(50836),y=class{#r;#i;#u;#s;#a;#o;#l;#c;constructor(e={}){this.#r=e.queryCache||new a,this.#i=e.mutationCache||new l,this.#u=e.defaultOptions||{},this.#s=new Map,this.#a=new Map,this.#o=0}mount(){this.#o++,1===this.#o&&(this.#l=d.j.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onFocus())}),this.#c=f.N.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onOnline())}))}unmount(){this.#o--,0===this.#o&&(this.#l?.(),this.#l=void 0,this.#c?.(),this.#c=void 0)}isFetching(e){return this.#r.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.getQueryData(e.queryKey);if(void 0===t)return this.fetchQuery(e);{let n=this.defaultQueryOptions(e),i=this.#r.build(this,n);return e.revalidateIfStale&&i.isStaleByTime((0,r.KC)(n.staleTime,i))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return this.#r.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){let i=this.defaultQueryOptions({queryKey:e}),u=this.#r.get(i.queryHash),s=u?.state.data,a=(0,r.SE)(t,s);if(void 0!==a)return this.#r.build(this,i).setData(a,{...n,manual:!0})}setQueriesData(e,t,n){return u.V.batch(()=>this.#r.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state}removeQueries(e){let t=this.#r;u.V.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let n=this.#r,r={type:"active",...e};return u.V.batch(()=>(n.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){let n={revert:!0,...t};return Promise.all(u.V.batch(()=>this.#r.findAll(e).map(e=>e.cancel(n)))).then(r.ZT).catch(r.ZT)}invalidateQueries(e={},t={}){return u.V.batch(()=>{if(this.#r.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){let n={...t,cancelRefetch:t?.cancelRefetch??!0};return Promise.all(u.V.batch(()=>this.#r.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(r.ZT)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.ZT)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let n=this.#r.build(this,t);return n.isStaleByTime((0,r.KC)(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.ZT).catch(r.ZT)}fetchInfiniteQuery(e){return e.behavior=(0,h.Gm)(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.ZT).catch(r.ZT)}resumePausedMutations(){return f.N.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#r}getMutationCache(){return this.#i}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,t){this.#s.set((0,r.Ym)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#s.values()],n={};return t.forEach(t=>{(0,r.to)(e,t.queryKey)&&(n={...n,...t.defaultOptions})}),n}setMutationDefaults(e,t){this.#a.set((0,r.Ym)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#a.values()],n={};return t.forEach(t=>{(0,r.to)(e,t.mutationKey)&&(n={...n,...t.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),!0!==t.enabled&&t.queryFn===r.CN&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#r.clear(),this.#i.clear()}}},76351:function(e,t,n){n.d(t,{useQuery:function(){return u}});var r=n(83667),i=n(91235);function u(e,t){return(0,i.r)(e,r.z,t)}},86623:function(e,t,n){n.d(t,{d7:function(){return i},uu:function(){return y}});let{Axios:r,AxiosError:i,CanceledError:u,isCancel:s,CancelToken:a,VERSION:o,all:l,Cancel:c,isAxiosError:d,spread:f,toFormData:h,AxiosHeaders:y,HttpStatusCode:m,formToJSON:p,getAdapter:v,mergeConfig:b}=n(38472).default}}]);