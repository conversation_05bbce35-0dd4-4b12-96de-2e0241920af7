"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3892],{28063:function(t,e,n){n.d(e,{Fl:function(){return g},MZ:function(){return d},cE:function(){return x},td:function(){return v}});var r=Symbol.for("preact-signals");function o(){if(c>1)c--;else{for(var t,e=!1;void 0!==u;){var n=u;for(u=void 0,s++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&h(n))try{n.c()}catch(n){e||(t=n,e=!0)}n=r}}if(s=0,c--,e)throw t}}var i=void 0;function a(t){var e=i;i=void 0;try{return t()}finally{i=e}}var u=void 0,c=0,s=0,f=0;function l(t){if(void 0!==i){var e=t.n;if(void 0===e||e.t!==i)return e={i:0,S:t,p:i.s,n:void 0,t:i,e:void 0,x:void 0,r:e},void 0!==i.s&&(i.s.n=e),i.s=e,t.n=e,32&i.f&&t.S(e),e;if(-1===e.i)return e.i=0,void 0!==e.n&&(e.n.p=e.p,void 0!==e.p&&(e.p.n=e.n),e.p=i.s,e.n=void 0,i.s.n=e,i.s=e),e}}function d(t,e){this.v=t,this.i=0,this.n=void 0,this.t=void 0,this.W=null==e?void 0:e.watched,this.Z=null==e?void 0:e.unwatched}function v(t,e){return new d(t,e)}function h(t){for(var e=t.s;void 0!==e;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function p(t){for(var e=t.s;void 0!==e;e=e.n){var n=e.S.n;if(void 0!==n&&(e.r=n),e.S.n=e,e.i=-1,void 0===e.n){t.s=e;break}}}function m(t){for(var e=t.s,n=void 0;void 0!==e;){var r=e.p;-1===e.i?(e.S.U(e),void 0!==r&&(r.n=e.n),void 0!==e.n&&(e.n.p=r)):n=e,e.S.n=e.r,void 0!==e.r&&(e.r=void 0),e=r}t.s=n}function y(t,e){d.call(this,void 0),this.x=t,this.s=void 0,this.g=f-1,this.f=4,this.W=null==e?void 0:e.watched,this.Z=null==e?void 0:e.unwatched}function g(t,e){return new y(t,e)}function b(t){var e=t.u;if(t.u=void 0,"function"==typeof e){c++;var n=i;i=void 0;try{e()}catch(e){throw t.f&=-2,t.f|=8,E(t),e}finally{i=n,o()}}}function E(t){for(var e=t.s;void 0!==e;e=e.n)e.S.U(e);t.x=void 0,t.s=void 0,b(t)}function w(t){if(i!==this)throw Error("Out-of-order effect");m(this),i=t,this.f&=-2,8&this.f&&E(this),o()}function S(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function x(t){var e=new S(t);try{e.c()}catch(t){throw e.d(),t}return e.d.bind(e)}d.prototype.brand=r,d.prototype.h=function(){return!0},d.prototype.S=function(t){var e=this,n=this.t;n!==t&&void 0===t.e&&(t.x=n,this.t=t,void 0!==n?n.e=t:a(function(){var t;null==(t=e.W)||t.call(e)}))},d.prototype.U=function(t){var e=this;if(void 0!==this.t){var n=t.e,r=t.x;void 0!==n&&(n.x=r,t.e=void 0),void 0!==r&&(r.e=n,t.x=void 0),t===this.t&&(this.t=r,void 0===r&&a(function(){var t;null==(t=e.Z)||t.call(e)}))}},d.prototype.subscribe=function(t){var e=this;return x(function(){var n=e.value,r=i;i=void 0;try{t(n)}finally{i=r}})},d.prototype.valueOf=function(){return this.value},d.prototype.toString=function(){return this.value+""},d.prototype.toJSON=function(){return this.value},d.prototype.peek=function(){var t=i;i=void 0;try{return this.value}finally{i=t}},Object.defineProperty(d.prototype,"value",{get:function(){var t=l(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(t!==this.v){if(s>100)throw Error("Cycle detected");this.v=t,this.i++,f++,c++;try{for(var e=this.t;void 0!==e;e=e.x)e.t.N()}finally{o()}}}}),y.prototype=new d,y.prototype.h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f)||(this.f&=-5,this.g===f))return!0;if(this.g=f,this.f|=1,this.i>0&&!h(this))return this.f&=-2,!0;var t=i;try{p(this),i=this;var e=this.x();(16&this.f||this.v!==e||0===this.i)&&(this.v=e,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return i=t,m(this),this.f&=-2,!0},y.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var e=this.s;void 0!==e;e=e.n)e.S.S(e)}d.prototype.S.call(this,t)},y.prototype.U=function(t){if(void 0!==this.t&&(d.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var e=this.s;void 0!==e;e=e.n)e.S.U(e)}},y.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Object.defineProperty(y.prototype,"value",{get:function(){if(1&this.f)throw Error("Cycle detected");var t=l(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),S.prototype.c=function(){var t=this.S();try{if(8&this.f||void 0===this.x)return;var e=this.x();"function"==typeof e&&(this.u=e)}finally{t()}},S.prototype.S=function(){if(1&this.f)throw Error("Cycle detected");this.f|=1,this.f&=-9,b(this),p(this),c++;var t=i;return i=this,w.bind(this,t)},S.prototype.N=function(){2&this.f||(this.f|=2,this.o=u,u=this)},S.prototype.d=function(){this.f|=8,1&this.f||E(this)}},78369:function(t,e,n){n.d(e,{Ry:function(){return s}});var r=new WeakMap,o=new WeakMap,i={},a=0,u=function(t){return t&&(t.host||u(t.parentNode))},c=function(t,e,n,c){var s=(Array.isArray(t)?t:[t]).map(function(t){if(e.contains(t))return t;var n=u(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)}).filter(function(t){return!!t});i[n]||(i[n]=new WeakMap);var f=i[n],l=[],d=new Set,v=new Set(s),h=function(t){!t||d.has(t)||(d.add(t),h(t.parentNode))};s.forEach(h);var p=function(t){!t||v.has(t)||Array.prototype.forEach.call(t.children,function(t){if(d.has(t))p(t);else try{var e=t.getAttribute(c),i=null!==e&&"false"!==e,a=(r.get(t)||0)+1,u=(f.get(t)||0)+1;r.set(t,a),f.set(t,u),l.push(t),1===a&&i&&o.set(t,!0),1===u&&t.setAttribute(n,"true"),i||t.setAttribute(c,"true")}catch(e){console.error("aria-hidden: cannot operate on ",t,e)}})};return p(e),d.clear(),a++,function(){l.forEach(function(t){var e=r.get(t)-1,i=f.get(t)-1;r.set(t,e),f.set(t,i),e||(o.has(t)||t.removeAttribute(c),o.delete(t)),i||t.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},s=function(t,e,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(t)?t:[t]),o=e||("undefined"==typeof document?null:(Array.isArray(t)?t[0]:t).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),c(r,o,n,"aria-hidden")):function(){return null}}},17590:function(t,e,n){n.d(e,{Z:function(){return z}});var r,o,i,a,u,c,s,f=n(11735),l=n(2265),d="right-scroll-bar-position",v="width-before-scroll-bar";function h(t,e){return"function"==typeof t?t(e):t&&(t.current=e),t}var p="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,m=new WeakMap,y=(void 0===r&&(r={}),(void 0===o&&(o=function(t){return t}),i=[],a=!1,u={read:function(){if(a)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return i.length?i[i.length-1]:null},useMedium:function(t){var e=o(t,a);return i.push(e),function(){i=i.filter(function(t){return t!==e})}},assignSyncMedium:function(t){for(a=!0;i.length;){var e=i;i=[],e.forEach(t)}i={push:function(e){return t(e)},filter:function(){return i}}},assignMedium:function(t){a=!0;var e=[];if(i.length){var n=i;i=[],n.forEach(t),e=i}var r=function(){var n=e;e=[],n.forEach(t)},o=function(){return Promise.resolve().then(r)};o(),i={push:function(t){e.push(t),o()},filter:function(t){return e=e.filter(t),i}}}}).options=(0,f.pi)({async:!0,ssr:!1},r),u),g=function(){},b=l.forwardRef(function(t,e){var n,r,o,i,a=l.useRef(null),u=l.useState({onScrollCapture:g,onWheelCapture:g,onTouchMoveCapture:g}),c=u[0],s=u[1],d=t.forwardProps,v=t.children,b=t.className,E=t.removeScrollBar,w=t.enabled,S=t.shards,x=t.sideCar,C=t.noIsolation,k=t.inert,N=t.allowPinchZoom,M=t.as,T=t.gapMode,A=(0,f._T)(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[a,e],r=function(t){return n.forEach(function(e){return h(e,t)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var t=o.value;t!==value&&(o.value=value,o.callback(value,t))}}}})[0]).callback=r,i=o.facade,p(function(){var t=m.get(i);if(t){var e=new Set(t),r=new Set(n),o=i.current;e.forEach(function(t){r.has(t)||h(t,null)}),r.forEach(function(t){e.has(t)||h(t,o)})}m.set(i,n)},[n]),i),R=(0,f.pi)((0,f.pi)({},A),c);return l.createElement(l.Fragment,null,w&&l.createElement(x,{sideCar:y,removeScrollBar:E,shards:S,noIsolation:C,inert:k,setCallbacks:s,allowPinchZoom:!!N,lockRef:a,gapMode:T}),d?l.cloneElement(l.Children.only(v),(0,f.pi)((0,f.pi)({},R),{ref:L})):l.createElement(void 0===M?"div":M,(0,f.pi)({},R,{className:b,ref:L}),v))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:v,zeroRight:d};var E=function(t){var e=t.sideCar,n=(0,f._T)(t,["sideCar"]);if(!e)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=e.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,(0,f.pi)({},n))};E.isSideCarExport=!0;var w=function(){var t=0,e=null;return{add:function(r){if(0==t&&(e=function(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var e=s||n.nc;return e&&t.setAttribute("nonce",e),t}())){var o,i;(o=e).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=e,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}t++},remove:function(){--t||!e||(e.parentNode&&e.parentNode.removeChild(e),e=null)}}},S=function(){var t=w();return function(e,n){l.useEffect(function(){return t.add(e),function(){t.remove()}},[e&&n])}},x=function(){var t=S();return function(e){return t(e.styles,e.dynamic),null}},C={left:0,top:0,right:0,gap:0},k=function(t){return parseInt(t||"",10)||0},N=function(t){var e=window.getComputedStyle(document.body),n=e["padding"===t?"paddingLeft":"marginLeft"],r=e["padding"===t?"paddingTop":"marginTop"],o=e["padding"===t?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},M=function(t){if(void 0===t&&(t="margin"),"undefined"==typeof window)return C;var e=N(t),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:e[0],top:e[1],right:e[2],gap:Math.max(0,r-n+e[2]-e[0])}},T=x(),A="data-scroll-locked",L=function(t,e,n,r){var o=t.left,i=t.top,a=t.right,u=t.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([e&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(d," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(v," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(d," .").concat(d," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(v," .").concat(v," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},R=function(){var t=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(t)?t:0},W=function(){l.useEffect(function(){return document.body.setAttribute(A,(R()+1).toString()),function(){var t=R()-1;t<=0?document.body.removeAttribute(A):document.body.setAttribute(A,t.toString())}},[])},P=function(t){var e=t.noRelative,n=t.noImportant,r=t.gapMode,o=void 0===r?"margin":r;W();var i=l.useMemo(function(){return M(o)},[o]);return l.createElement(T,{styles:L(i,!e,o,n?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var I=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",I,I),window.removeEventListener("test",I,I)}catch(t){F=!1}var O=!!F&&{passive:!1},U=function(t,e){var n=window.getComputedStyle(t);return"hidden"!==n[e]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==t.tagName&&"visible"===n[e])},D=function(t,e){var n=e.ownerDocument,r=e;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Z(t,r)){var o=j(t,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Z=function(t,e){return"v"===t?U(e,"overflowY"):U(e,"overflowX")},j=function(t,e){return"v"===t?[e.scrollTop,e.scrollHeight,e.clientHeight]:[e.scrollLeft,e.scrollWidth,e.clientWidth]},_=function(t,e,n,r,o){var i,a=(i=window.getComputedStyle(e).direction,"h"===t&&"rtl"===i?-1:1),u=a*r,c=n.target,s=e.contains(c),f=!1,l=u>0,d=0,v=0;do{var h=j(t,c),p=h[0],m=h[1]-h[2]-a*p;(p||m)&&Z(t,c)&&(d+=m,v+=p),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!s&&c!==document.body||s&&(e.contains(c)||e===c));return l&&(o&&1>Math.abs(d)||!o&&u>d)?f=!0:!l&&(o&&1>Math.abs(v)||!o&&-u>v)&&(f=!0),f},B=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},K=function(t){return[t.deltaX,t.deltaY]},X=function(t){return t&&"current"in t?t.current:t},Y=0,H=[],q=(c=function(t){var e=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(Y++)[0],i=l.useState(x)[0],a=l.useRef(t);l.useEffect(function(){a.current=t},[t]),l.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(o));var e=(0,f.ev)([t.lockRef.current],(t.shards||[]).map(X),!0).filter(Boolean);return e.forEach(function(t){return t.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),e.forEach(function(t){return t.classList.remove("allow-interactivity-".concat(o))})}}},[t.inert,t.lockRef.current,t.shards]);var u=l.useCallback(function(t,e){if("touches"in t&&2===t.touches.length)return!a.current.allowPinchZoom;var o,i=B(t),u=n.current,c="deltaX"in t?t.deltaX:u[0]-i[0],s="deltaY"in t?t.deltaY:u[1]-i[1],f=t.target,l=Math.abs(c)>Math.abs(s)?"h":"v";if("touches"in t&&"h"===l&&"range"===f.type)return!1;var d=D(l,f);if(!d)return!0;if(d?o=l:(o="v"===l?"h":"v",d=D(l,f)),!d)return!1;if(!r.current&&"changedTouches"in t&&(c||s)&&(r.current=o),!o)return!0;var v=r.current||o;return _(v,e,t,"h"===v?c:s,!0)},[]),c=l.useCallback(function(t){if(H.length&&H[H.length-1]===i){var n="deltaY"in t?K(t):B(t),r=e.current.filter(function(e){var r;return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(r=e.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){t.cancelable&&t.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(X).filter(Boolean).filter(function(e){return e.contains(t.target)});(o.length>0?u(t,o[0]):!a.current.noIsolation)&&t.cancelable&&t.preventDefault()}}},[]),s=l.useCallback(function(t,n,r,o){var i={name:t,delta:n,target:r,should:o,shadowParent:function(t){for(var e=null;null!==t;)t instanceof ShadowRoot&&(e=t.host,t=t.host),t=t.parentNode;return e}(r)};e.current.push(i),setTimeout(function(){e.current=e.current.filter(function(t){return t!==i})},1)},[]),d=l.useCallback(function(t){n.current=B(t),r.current=void 0},[]),v=l.useCallback(function(e){s(e.type,K(e),e.target,u(e,t.lockRef.current))},[]),h=l.useCallback(function(e){s(e.type,B(e),e.target,u(e,t.lockRef.current))},[]);l.useEffect(function(){return H.push(i),t.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:h}),document.addEventListener("wheel",c,O),document.addEventListener("touchmove",c,O),document.addEventListener("touchstart",d,O),function(){H=H.filter(function(t){return t!==i}),document.removeEventListener("wheel",c,O),document.removeEventListener("touchmove",c,O),document.removeEventListener("touchstart",d,O)}},[]);var p=t.removeScrollBar,m=t.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?l.createElement(P,{gapMode:t.gapMode}):null)},y.useMedium(c),E),$=l.forwardRef(function(t,e){return l.createElement(b,(0,f.pi)({},t,{ref:e,sideCar:q}))});$.classNames=b.classNames;var z=$},46246:function(t,e,n){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},i=r.useState,a=r.useEffect,u=r.useLayoutEffect,c=r.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!o(t,n)}catch(t){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var n=e(),r=i({inst:{value:n,getSnapshot:e}}),o=r[0].inst,f=r[1];return u(function(){o.value=n,o.getSnapshot=e,s(o)&&f({inst:o})},[t,n,e]),a(function(){return s(o)&&f({inst:o}),t(function(){s(o)&&f({inst:o})})},[t]),c(n),n};e.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:f},10554:function(t,e,n){t.exports=n(46246)},16880:function(t,e,n){let r;n.d(e,{Z:function(){return p}});var o=n(28063),i=n(2265),a=n(10554);let u=Symbol.for("react.element"),c=Symbol.dispose||Symbol.for("Symbol.dispose"),s=Promise.prototype.then.bind(Promise.resolve()),f=new Set,l=!1,d=()=>{l=!1,f.forEach(t=>{t.r()}),f.clear()},v=t=>{l||(l=!0,s(d)),f.has(t)||f.add(t)},h=0;function p(){let t=(0,i.useRef)();if(null==t.current){let e,n,i,a,u,s;t.current=(i=0,a=(0,o.cE)(function(){e=this}),u=!1,s=0,e.c=function(){if(!u){if(s>25)throw Error(`preact-signals: Too many sync rerenders (${s}), you might change parent component signal dependencies in render of child component.`);i=i+1|0,n&&n()}},{effect:e,subscribe:t=>(n=t,function(){i=i+1|0,n=void 0,a()}),r(){s=0},s(){if(u=!0,s++,v(this),!h&&r)throw Error("cleanUpFn should be undefined");if(h&&!r)throw Error("cleanUpFn should be defined with depth");h||(r=e.S()),h++},getSnapshot:()=>i,f(){if(h<1)throw Error("useSignalsDepth should be non-negative");try{if(1===h&&!r)throw Error("cleanUpFn should be defined with depth");if(1===h&&r)try{r()}finally{u=!1,r=void 0}}finally{h--}},[c](){this.f()}})}let e=t.current;return(0,a.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot),e.s(),e}Object.defineProperties(o.MZ.prototype,{$$typeof:{configurable:!0,value:u},type:{configurable:!0,value:function(t){let e=p();try{return t.data.value}finally{e.f()}}},props:{configurable:!0,get(){return{data:this}}},ref:{configurable:!0,value:null}})},20589:function(t,e,n){n.d(e,{EW:function(){return i}});var r=n(2265),o=0;function i(){r.useEffect(()=>{var t,e;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(t=n[0])&&void 0!==t?t:a()),document.body.insertAdjacentElement("beforeend",null!==(e=n[1])&&void 0!==e?e:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),o--}},[])}function a(){let t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",t}},80467:function(t,e,n){let r;n.d(e,{M:function(){return d}});var o=n(2265),i=n(1584),a=n(25171),u=n(75137),c=n(57437),s="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",l={bubbles:!1,cancelable:!0},d=o.forwardRef((t,e)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:d,onUnmountAutoFocus:y,...g}=t,[b,E]=o.useState(null),w=(0,u.W)(d),S=(0,u.W)(y),x=o.useRef(null),C=(0,i.e)(e,t=>E(t)),k=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let t=function(t){if(k.paused||!b)return;let e=t.target;b.contains(e)?x.current=e:p(x.current,{select:!0})},e=function(t){if(k.paused||!b)return;let e=t.relatedTarget;null===e||b.contains(e)||p(x.current,{select:!0})};document.addEventListener("focusin",t),document.addEventListener("focusout",e);let n=new MutationObserver(function(t){if(document.activeElement===document.body)for(let e of t)e.removedNodes.length>0&&p(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",t),document.removeEventListener("focusout",e),n.disconnect()}}},[r,b,k.paused]),o.useEffect(()=>{if(b){m.add(k);let t=document.activeElement;if(!b.contains(t)){let e=new CustomEvent(s,l);b.addEventListener(s,w),b.dispatchEvent(e),e.defaultPrevented||(function(t){let{select:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of t)if(p(r,{select:e}),document.activeElement!==n)return}(v(b).filter(t=>"A"!==t.tagName),{select:!0}),document.activeElement===t&&p(b))}return()=>{b.removeEventListener(s,w),setTimeout(()=>{let e=new CustomEvent(f,l);b.addEventListener(f,S),b.dispatchEvent(e),e.defaultPrevented||p(null!=t?t:document.body,{select:!0}),b.removeEventListener(f,S),m.remove(k)},0)}}},[b,w,S,k]);let N=o.useCallback(t=>{if(!n&&!r||k.paused)return;let e="Tab"===t.key&&!t.altKey&&!t.ctrlKey&&!t.metaKey,o=document.activeElement;if(e&&o){let e=t.currentTarget,[r,i]=function(t){let e=v(t);return[h(e,t),h(e.reverse(),t)]}(e);r&&i?t.shiftKey||o!==i?t.shiftKey&&o===r&&(t.preventDefault(),n&&p(i,{select:!0})):(t.preventDefault(),n&&p(r,{select:!0})):o===e&&t.preventDefault()}},[n,r,k.paused]);return(0,c.jsx)(a.WV.div,{tabIndex:-1,...g,ref:C,onKeyDown:N})});function v(t){let e=[],n=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:t=>{let e="INPUT"===t.tagName&&"hidden"===t.type;return t.disabled||t.hidden||e?NodeFilter.FILTER_SKIP:t.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)e.push(n.currentNode);return e}function h(t,e){for(let n of t)if(!function(t,e){let{upTo:n}=e;if("hidden"===getComputedStyle(t).visibility)return!0;for(;t&&(void 0===n||t!==n);){if("none"===getComputedStyle(t).display)return!0;t=t.parentElement}return!1}(n,{upTo:e}))return n}function p(t){let{select:e=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t&&t.focus){var n;let r=document.activeElement;t.focus({preventScroll:!0}),t!==r&&(n=t)instanceof HTMLInputElement&&"select"in n&&e&&t.select()}}d.displayName="FocusScope";var m=(r=[],{add(t){let e=r[0];t!==e&&(null==e||e.pause()),(r=y(r,t)).unshift(t)},remove(t){var e;null===(e=(r=y(r,t))[0])||void 0===e||e.resume()}});function y(t,e){let n=[...t],r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}}}]);