"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8795],{28297:function(e,t,a){var n=a(57437),r=a(66648);t.Z=e=>{let{name:t,image:a,isCalling:s,isFullscreen:o,showTextBox:l,messages:i,time:c,showText:d}=e;return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center sm:-mt-12 -mt-3",children:[(0,n.jsx)("div",{className:"".concat(o?"2xl:w-56 2xl:h-56 w-48 h-48 ":"w-20 h-20 "," rounded-full overflow-hidden"),children:(0,n.jsx)(r.default,{src:a,width:128,height:128,alt:"".concat(t,"'s profile picture"),className:"aspect-square h-full w-full object-cover object-top",unoptimized:!0})}),(0,n.jsx)("h4",{className:"text-xs font-extralight pt-1.5 ".concat(""," ").concat(l&&!o?"pb-0.5":"pb-0"),children:s?"Calling...":c}),(0,n.jsx)("h2",{className:"".concat(o?"2xl:text-3xl sm:text-2xl ":"sm:text-xl text-base"," text-center ").concat(s?o?"sm:pt-4 pt-6":"sm:pt-8 pt-0":o?"sm:pt-4":l?"sm:pt-0 sm:pb-0":"sm:pt-6 sm:pb-4 pt-[4px]"),children:t})]})}},17116:function(e,t,a){var n=a(57437),r=a(66648),s=a(50495);t.Z=e=>{let{isCallActive:t,isCalling:a,showTextBox:o,isFullscreen:l,handleAcceptCall:i,handleEndCall:c,handleMute:d,mute:u,mobile:m=!1}=e;return(0,n.jsx)("div",{className:"w-full flex justify-center items-center sm:mx-0 ".concat(a?l?"pt-10":"sm:absolute bottom-3 pt-3 mb-1":l?"pt-10":"".concat(o?"pt-6 sm:-mb-0":"pt-3 sm:absolute bottom-3"," mb-1")),children:t?(0,n.jsxs)("div",{className:"flex items-center justify-center gap-4",children:[(0,n.jsx)(r.default,{src:"/icons/decline_call.svg",width:48,height:48,alt:"End Call",className:"cursor-pointer hover:opacity-80",onClick:c}),(0,n.jsx)(r.default,{src:"/icons/accept_call.svg",width:48,height:48,alt:"Active Call",className:"cursor-pointer hover:opacity-80",onClick:i})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(s.z,{variant:"ghost",className:"flex flex-col ".concat(l?"2xl:gap-6 sm:gap-4 gap-2 sm:w-28 w-24":"sm:gap-1 sm:w-20"," focus-visible:ring-0 hover:bg-transparent items-center group end_button"),onClick:c,children:[(0,n.jsxs)("div",{className:"hover:brightness-[.9] transition-colors ease-in-out duration-300 relative ".concat(l?"2xl:scale-[1.7] sm:scale-[1.5] scale-[1.3]":"sm:scale-[1] scale-[.85]"),children:[(0,n.jsx)("div",{className:"bg-primary p-4 rounded-full"}),(0,n.jsx)(r.default,{src:"/icons/endCallVector.svg",width:"24",height:"24",alt:"end call",className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",unoptimized:!0})]}),(0,n.jsx)("span",{className:"".concat(l?"2xl:text-lg sm:text-sm text-[9px]":"sm:text-xs text-[9px]"," sm:font-extralight font-extralight"),children:"End Call"})]}),!a&&(0,n.jsxs)(s.z,{"aria-label":"Mute/Unmute",variant:"ghost",className:" flex flex-col ".concat(l?"2xl:gap-6 sm:gap-4 gap-2 sm:w-28 w-24 ":"sm:gap-1 sm:w-20","  focus-visible:ring-0 hover:bg-transparent items-center"),onClick:d,children:[(0,n.jsxs)("div",{className:"hover:brightness-[.9] transition-colors ease-in-out duration-300 relative ".concat(l?"2xl:scale-[1.7] sm:scale-[1.5] scale-[1.3]":"sm:scale-[1] scale-[.85]"),children:[(0,n.jsx)("div",{className:"".concat(u?"bg-foreground":"bg-primary"," p-4 rounded-full")}),u?(0,n.jsx)(r.default,{src:"/icons/microphone-slash.svg",width:"24",height:"24",alt:"mute/unmute",className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",unoptimized:!0}):(0,n.jsx)(r.default,{src:"/icons/microphone.svg",width:"24",height:"24",alt:"mute/unmute",className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",priority:!0})]}),(0,n.jsx)("span",{className:"".concat(l?"2xl:text-lg sm:text-sm text-[9px]":"sm:text-xs text-[9px]"," sm:font-extralight font-extralight"),children:u?"Unmute":"Mute"})]})]})})}},48795:function(e,t,a){a.d(t,{Z:function(){return Q}});var n=a(57437),r=a(85499),s=a(99441),o=a(63858),l=a(2265),i=a(90837),c=a(28297),d=a(17116),u=a(47898),m=a(50495),p=a(3114),h=e=>{let{isFullscreen:t,isCalling:a,toggleTextBoxVisibility:r,handleFullScreen:s,toggleVolumeBarVisibility:o}=e,l=(0,p.P)();return(0,n.jsxs)("div",{className:"absolute  ".concat(t?"right-0 m-4 2xl:gap-6 md:gap-5 sm:gap-4 gap-7 top-0":"sm:right-0 sm:left-auto sm:m-4 sm:translate-x-0 right-auto left-1/2 -translate-x-1/2 my-4 mx-1.5 sm:gap-2 gap-2.5 -top-[3px]","  flex  "),children:[(0,n.jsx)(m.z,{"aria-label":"Send Message",variant:"ghost",size:"icon",title:"Send Message",className:"hover:bg-transparent hover:text-primary transition-colors ease-in-out duration-300 h-4 w-4  ".concat(t?"2xl:scale-[1.5] md:scale-[1.4] sm:scale-[1.2] scale-[1.7]":"sm:scale-[1] scale-[1.05]"," ").concat(a?"pointer-events-none opacity-50":""),onClick:r,children:(0,n.jsx)("span",{className:"w-fit h-fit",children:l.messageBubbleDotted})}),(0,n.jsx)(m.z,{"aria-label":"Toggle Fullscreen",variant:"ghost",size:"icon",title:"Maximize/Minimize",onClick:s,className:"hover:bg-transparent hover:text-primary transition-colors ease-in-out duration-300 h-4 w-4  ".concat(t?" 2xl:scale-[1.65] md:scale-[1.55] sm:scale-[1.35] scale-[1.7]":"sm:scale-[1] scale-[.9]"),children:t?(0,n.jsx)("span",{className:"w-fit h-fit",children:l.minimize}):(0,n.jsx)("span",{className:"w-fit h-fit",children:l.maximize})}),(0,n.jsx)("div",{className:"show-desktop",children:(0,n.jsx)(m.z,{"aria-label":"Volume Control",variant:"ghost",size:"icon",title:"Volume Control",onClick:o,className:"hover:bg-transparent hover:text-primary transition-colors ease-in-out duration-300 h-4 w-4  ".concat(t?" 2xl:scale-[1.5] md:scale-[1.4] sm:scale-[1.2] scale-[1.7]":"sm:scale-[1] scale-[1.05]"," ").concat(a?"pointer-events-none opacity-50":""),children:(0,n.jsx)("span",{className:"w-fit h-fit",children:l.volumeSpeaker})})})]})},g=a(55189),f=a(16880);let x=(0,s.atom)({key:"callVolumeAtom",default:.5});function w(){var e,t=(0,f.Z)();try{let[t,r]=(0,l.useState)(!1),o=(0,s.useRecoilValue)(x),i=(0,l.useRef)(null),c=(0,l.useRef)(null),d=(0,l.useRef)(null),u=(0,l.useRef)(!1);async function a(e,t){try{n(),i.current=new AudioContext({sampleRate:24e3}),d.current=i.current.createGain(),d.current.gain.setValueAtTime(o,i.current.currentTime);let a=i.current.currentTime,s=e.getReader(),l=new Uint8Array,m=await s.read();if(u.current)return;for(r(!0);!m.done&&i.current;){if(0===m.value.length){if(console.error("Received empty data chunk"),m=await s.read(),u.current)return;continue}let e=new Uint8Array(l.length+m.value.length);e.set(l),e.set(m.value,l.length);let r=4*Math.floor(e.length/4),o=e.length%4,p=new Float32Array(e.buffer,0,r/4);if(0===p.length){if(console.error("Converted buffer length is 0"),m=await s.read(),u.current)return;continue}l=new Uint8Array(e.buffer,r,o);let h=i.current.createBuffer(1,p.length,i.current.sampleRate);if(h.copyToChannel(p,0),c.current=i.current.createBufferSource(),c.current.buffer=h,c.current.connect(d.current),d.current.connect(i.current.destination),c.current.start(a),a+=h.duration,m=await s.read(),u.current)return;m.done&&(c.current.onended=()=>{n(),t()})}}catch(e){}}function n(e){var t;e&&(u.current=!0),null===(t=i.current)||void 0===t||t.close(),i.current=null,r(!1)}return(0,l.useEffect)(()=>{u.current=!1},[]),(0,l.useEffect)(()=>{d.current&&i.current&&d.current.gain.setValueAtTime(o,i.current.currentTime)},[o,null===(e=d.current)||void 0===e?void 0:e.gain]),{isPlaying:t,play:a,stop:n,changeVolume:function(e){d.current&&i.current&&d.current.gain.setValueAtTime(e,i.current.currentTime)}}}finally{t.f()}}var v=a(38472),b=a(13498),y=a(88726),_=a(40282),j=a(41829),S=a(16463),k=a(13304),N=a(84216),A=a(48059),C=a.n(A);function R(){var e=(0,f.Z)();try{let e=w(),[t,a]=(0,s.useRecoilState)(x),r=t=>{let n=Math.round(100*Number(t))/100;a(n),e.changeVolume(n)},[i,c]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{o.gn||o.Q5&&o.G6?c(!0):o.Dt&&c(!1)},[]),(0,n.jsxs)("div",{className:C().dynamic([["e96234a90046655e",[100*t,100*t,100*t,100*t,100*t,100*t]]])+" absolute right-1 top-6 p-4 rounded-lg",children:[(0,n.jsx)("input",{type:"range",value:1-t,onChange:e=>r(1-Number(e.target.value)),min:0,max:1,step:.01,style:{writingMode:"vertical-rl",background:"#eaeaea",borderRadius:"0.25rem"},className:C().dynamic([["e96234a90046655e",[100*t,100*t,100*t,100*t,100*t,100*t]]])+" w-2 h-28 appearance-none mt-5"}),(0,n.jsx)(C(),{id:"e96234a90046655e",dynamic:[100*t,100*t,100*t,100*t,100*t,100*t],children:'input[type="range"].__jsx-style-dynamic-selector::-webkit-slider-runnable-track{background:-webkit-linear-gradient(\n							to top,\n							#d62a5e '.concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-moz-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-o-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,'%\n						);width:100%;height:100%;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem}input[type="range"].__jsx-style-dynamic-selector::-webkit-slider-thumb{-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;appearance:none;width:20px;height:20px;background-color:#d62a5e;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;cursor:pointer;-webkit-box-shadow:0px 0px 5px rgba(0,0,0,.4);-moz-box-shadow:0px 0px 5px rgba(0,0,0,.4);box-shadow:0px 0px 5px rgba(0,0,0,.4);-webkit-transform:translatex(30%);-moz-transform:translatex(30%);-ms-transform:translatex(30%);-o-transform:translatex(30%);transform:translatex(30%)}input[type="range"].__jsx-style-dynamic-selector::-moz-range-track{background:-webkit-linear-gradient(\n							to top,\n							#d62a5e ').concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-moz-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-o-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,'%\n						);width:100%;height:100%;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem}input[type="range"].__jsx-style-dynamic-selector::-moz-range-thumb{width:16px;height:16px;background-color:transparent;opacity:0;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;cursor:pointer}input[type="range"].__jsx-style-dynamic-selector::-ms-track{background:transparent;border-color:transparent;color:transparent;width:100%;height:100%}input[type="range"].__jsx-style-dynamic-selector::-ms-fill-lower{background:-webkit-linear-gradient(\n							to top,\n							#d62a5e ').concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-moz-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:-o-linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,"%\n						);background:linear-gradient(\n							to top,\n							#d62a5e ").concat(100*t,"%,\n							#eaeaea ").concat(100*t,'%\n						);-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem}input[type="range"].__jsx-style-dynamic-selector::-ms-fill-upper{background:#eaeaea;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem}input[type="range"].__jsx-style-dynamic-selector::-ms-thumb{width:16px;height:16px;background-color:transparent;opacity:0;-webkit-border-radius:50%;-moz-border-radius:50%;border-radius:50%;cursor:pointer}input[type="range"].__jsx-style-dynamic-selector::-webkit-slider-thumb:hover,input[type="range"].__jsx-style-dynamic-selector::-moz-range-thumb:hover,input[type="range"].__jsx-style-dynamic-selector::-ms-thumb:hover{opacity:1}')})]})}finally{e.f()}}var E=a(64269),T=a(34084),z=a(73404),D=a(46786),I=a(76383);function M(e){let{open:t,setOpen:a}=e;(0,s.useSetRecoilState)(r.$d);let[o,m]=(0,l.useState)(!1),[p,f]=(0,l.useState)(!1),[A,C]=(0,l.useState)(!0),[M,F]=(0,l.useState)(!1),[P,V]=(0,l.useState)(""),[O,U]=(0,l.useState)(""),[L,Z]=(0,s.useRecoilState)(E.S),B=(0,s.useSetRecoilState)(x);new Audio("/notif.wav"),new Audio("/call_connected.mp3");let q=(0,l.useRef)(new Audio("/call_connecting.wav")),W=new Audio("/call_disconnected.wav"),[H,G]=(0,s.useRecoilState)(T.zK),J=(0,S.useRouter)(),X=Date.now(),K=(0,s.useSetRecoilState)(T.AL);(0,s.useRecoilValue)(T.AL);let Q=(0,z.Z)(),{handleAddReplyToMsgHistoryCache:Y}=(0,D.Q)(),$=(0,s.useSetRecoilState)(r.Qg),[ee,et]=(0,l.useState)([]),ea=(0,s.useSetRecoilState)(r.Md),[en,er]=(0,l.useState)(!1),[es,eo]=(0,l.useState)(new Date),[el,ei]=(0,l.useState)(!1),[ec,ed]=(0,l.useState)("00:00"),eu=(0,_.F)({})(),{data:em,isLoading:ep}=(0,j.n)({convId:eu}),[eh,eg]=(0,l.useState)([]),ef=localStorage.getItem("sd_active_char_id")||"",[ex,ew]=(0,l.useState)(Date.now()),ev=w(),eb=(0,l.useRef)(!1),ey=(0,N.I)(),[e_,ej]=(0,l.useState)(!1),[eS,ek]=(0,l.useState)(!1);(0,l.useEffect)(()=>{var e,t,a,n;!ep&&(null==em?void 0:null===(t=em.pages)||void 0===t?void 0:null===(e=t[0])||void 0===e?void 0:e.msg)&&(null==em?void 0:null===(n=em.pages)||void 0===n?void 0:null===(a=n[0])||void 0===a?void 0:a.msg.length)>0&&et(ey({}))},[ep]),(0,l.useEffect)(()=>{eS&&!ev.isPlaying&&eT()},[eS,ev.isPlaying]),(0,l.useEffect)(()=>{if(!A){let e=setInterval(async()=>{let[t,a]=ec.split(":").map(e=>parseInt(e));a<59?ed("".concat(t.toString().padStart(2,"0"),":").concat((a+1).toString().padStart(2,"0"))):ed("".concat(t+1,":00"));let n=(new Date().getTime()-es.getTime())/1e3;n>=180&&!el&&(ei(!0),eR("no_response")),n>=240&&(eT(),ei(!1),clearInterval(e))},1e3);return()=>clearInterval(e)}},[A,ec]),(0,l.useEffect)(()=>(t.open&&(t.receivingCall?(q.current.loop=!0,q.current.play()):!t.userCalling||ep||eb.current||(q.current.loop=!0,q.current.play(),eA(),eb.current=!0)),()=>{}),[t.open,ep]),(0,l.useEffect)(()=>{t.open||(eb.current=!1)},[t.open]);let eN=async()=>{try{let e=new FormData;e.append("input",""),e.append("conv_id",eu),e.append("char_id",ef),e.append("msg_history",JSON.stringify(ee)),e.append("textMessage",L),e.append("isFirstMessage","true");let t=Q();if(e.append("timeAndDate",t),eE.start(),!eE.userSpeaking){let t=await fetch("".concat(b.fw,"/voice/voice_call"),{method:"POST",body:e,headers:{Authorization:localStorage.getItem("access_token")||""}});await eC(t)}}catch(e){console.error("Error in character initial call:",e),y.default.error("An error occurred while initiating the call.")}},eA=async()=>{er(!0),V("Wait..."),q.current.loop=!0,q.current.play();let e=Math.floor(2e3*Math.random())+1e3;try{let t=new FormData;t.append("input",""),t.append("conv_id",eu),t.append("char_id",ef),t.append("msg_history",JSON.stringify(ey({}))),t.append("textMessage",L),t.append("isFirstMessage","true");let a=Q();if(t.append("timeAndDate",a),eE.start(),!eE.userSpeaking){let a=await fetch("".concat(b.fw,"/voice/voice_call"),{method:"POST",body:t,headers:{Authorization:localStorage.getItem("access_token")||""}}),n=setTimeout(async()=>{await eC(a)},e);return()=>clearTimeout(n)}}catch(e){console.error("Error in initial call:",e),y.default.error("An error occurred while initiating the call.")}finally{let t=setTimeout(()=>{er(!1),V(""),q.current.pause()},e);return()=>clearTimeout(t)}},eC=async e=>{e.headers.forEach((e,t)=>{});let t=decodeURIComponent(e.headers.get("X-Transcript")||""),a=decodeURIComponent(e.headers.get("X-Response")||""),n=decodeURIComponent(e.headers.get("X-LLM-Model")||""),r=Number(e.headers.get("X-Completion-Tokens")||0),s=Number(e.headers.get("X-Prompt-Tokens")||0),o="true"===e.headers.get("X-End-Call");if(!e.ok||!a||!e.body||!n)throw Error(429===e.status?"Too many requests. Please try again later.":"An error occurred while processing the audio.");""===a&&o&&eT();let l=Date.now();ev.play(e.body,()=>{navigator.userAgent.includes("Firefox")&&eE.start()}),""===t?(et(e=>[...e,{role:"assistant",content:a}]),eg(e=>[...e,{role:"assistant",content:a,llm_model:n,tokens_usage:r,latency:l}])):(et(e=>[...e,{role:"user",content:t},{role:"assistant",content:a}]),eg(e=>[...e,{role:"user",content:t,llm_model:n,tokens_usage:s,latency:l},{role:"assistant",content:a,llm_model:n,tokens_usage:r,latency:l}])),o&&ek(!0),C(!1)},eR=async e=>{V("Wait..."),Date.now(),er(!0);try{if(Date.now()-ex>=6e4){let e=await v.default.post("".concat(b.fw,"/check/continuous_check_call"),{},{headers:{Authorization:localStorage.getItem("access_token")||""}});if(0===e.data.code){$({open:!0,text:"Grab Hearts to continue talking ".concat((null==t?void 0:t.text)||"","!"),image:"".concat((null==t?void 0:t.image)||""),type:"hearts"}),eT();return}if(2===e.data.code){(0,y.default)("Low Hearts Balance! Please top up to continue calling",{style:{background:"#D62A5E",color:"white"}}),J.push("/subscription");return}ew(Date.now())}if("string"==typeof e&&""!==e.trim()){let t=new FormData;t.append("conv_id",eu),t.append("char_id",ef),t.append("msg_history",JSON.stringify(ee)),t.append("textMessage",e),t.append("isFirstMessage","false");let a=Q();t.append("timeAndDate",a);let n=await fetch("".concat(b.fw,"/voice/voice_call"),{method:"POST",body:t,headers:{Authorization:localStorage.getItem("access_token")||""}});n.ok||await n.text(),await eC(n)}else{let t=new FormData;t.append("input",e),t.append("conv_id",eu),t.append("char_id",ef),t.append("msg_history",JSON.stringify(ee)),t.append("textMessage",L),t.append("isFirstMessage","false");let a=Q();t.append("timeAndDate",a);let n=await fetch("".concat(b.fw,"/voice/voice_call"),{method:"POST",body:t,headers:{Authorization:localStorage.getItem("access_token")||""}});if(!n.ok){if(422===n.status){let e=await n.json();ea(null==e?void 0:e.msg);return}{let e=await n.text();console.log("errorMessage",e)}}await eC(n)}eo(new Date)}catch(e){console.error("Error in submit:",e),y.default.error("An error occurred while processing the audio. Please try again.")}finally{er(!1),Z(""),V("")}};(0,l.useEffect)(()=>{""!==L.trim()&&eR(L)},[L]);let eE=(0,g.useMicVAD)({startOnLoad:!0,onSpeechEnd:e=>{ev.stop(),A||eR(new Blob([g.utils.encodeWAV(e)],{type:"audio/wav"}))},onSpeechStart:()=>{},modelURL:"/silero_vad.onnx",workletURL:"/vad.worklet.bundle.min.js",positiveSpeechThreshold:.6,minSpeechFrames:6,ortConfig(e){let t=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);e.env.wasm={wasmPaths:{"ort-wasm-simd-threaded.wasm":"/ort-wasm-simd-threaded.wasm","ort-wasm-simd.wasm":"/ort-wasm-simd.wasm","ort-wasm.wasm":"/ort-wasm.wasm","ort-wasm-threaded.wasm":"/ort-wasm-threaded.wasm"},numThreads:t?1:4}}});(0,l.useEffect)(()=>{eE.userSpeaking&&ev.stop()},[eE]);let eT=async()=>{try{ev.stop(!0),eE.pause(),K(!0),q.current.pause(),W.play(),a(e=>({...e,open:!1})),m(!1),F(!1),C(!1);let e=Date.now()-X,t="".concat(Math.floor(e/6e4),".").concat(Math.floor(e%6e4/1e3).toString().padStart(2,"0")),n=await v.default.post("".concat(b.fw,"/voice/voice_call_save"),{conv_id:eu,char_id:ef,msg_history:eh,duration:t},{headers:{Authorization:localStorage.getItem("access_token")||""}});Y({msgs:n.data.messages,charId:ef})}catch(a){var e,t;console.error("Error ending call:",a),y.default.error((null==a?void 0:null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==a?void 0:a.message)||"An error occurred while saving the conversation",{id:"ending_call"})}};(0,l.useEffect)(()=>{window.innerWidth<640&&m(!0),B(.5)},[t]),(0,l.useEffect)(()=>{let e=()=>{!o&&M&&window.innerWidth<640&&m(!0)};return window.addEventListener("resize",e),e(),()=>{window.removeEventListener("resize",e)}},[o,M]);let[ez,eD]=(0,l.useState)(!1);return(0,l.useEffect)(()=>{(0,I.R)()},[]),(0,n.jsxs)(i.Vq,{modal:!0,open:t.open,onOpenChange:()=>a(e=>({...e,open:!e.open})),children:[(0,n.jsx)(k.$N,{className:"hidden","aria-label":"Call Dialog"}),(0,n.jsxs)(i.cZ,{closeBtn:"hidden",onInteractOutside:e=>e.preventDefault(),className:"p-0  right-0 left-auto translate-x-0 translate-y-0  bg-background overflow-clip ".concat(o?"w-screen h-full max-w-none top-0 ":"sm:w-full sm:max-w-lg max-w-36 w-36 sm:h-64 sm:min-h-64 sm:max-h-64 min-h-56 max-h-56 top-12 "," "),children:[!A&&(0,n.jsx)("div",{className:"absolute  bg-primary  rounded-full transition-all ease-in-out duration-500 overflow-hidden ".concat(eE.loading||eE.errored||eE.userSpeaking||ev.isPlaying?eE.userSpeaking||ev.isPlaying?"opacity-50 ".concat(o?"w-2/3 sm:aspect-square h-2/3 blur-3xl":"w-3/5 h-4/5 sm:blur-3xl blur-xl","  "):"opacity-20  ".concat(o?"w-2/4 sm:aspect-square h-2/4 blur-2xl":"w-2/4 h-4/5 sm:blur-2xl blur-xl","  "):"opacity-40  ".concat(o?"w-2/4 sm:aspect-square h-2/4 blur-2xl":"w-2/4 h-4/5 sm:blur-2xl blur-xl"," ")),style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:-1}}),(0,n.jsxs)("div",{className:"\n							 ".concat(A&&"background"," w-full h-full text-white rounded-lg  border-[3.5px] border-primary"),children:[(0,n.jsx)(h,{isFullscreen:o,isCalling:A,toggleTextBoxVisibility:()=>{window.innerWidth<640&&m(!0),F(!M),M?(f(!1),eE.start()):(f(!0),eE.pause())},handleFullScreen:()=>{window.innerWidth<640&&o&&F(!1),m(!o)},toggleVolumeBarVisibility:()=>{window.innerWidth<640&&m(!0),eD(!ez)}}),(0,n.jsxs)("div",{className:"w-full h-full flex flex-col justify-center items-center sm:mt-0 mt-6 ",children:[(0,n.jsx)(c.Z,{name:t.text,image:t.image,isCalling:A,isFullscreen:o,showTextBox:M,messages:ee,time:ec,showText:P}),(0,n.jsx)(d.Z,{isCallActive:t.receivingCall,isCalling:A,showTextBox:M,isFullscreen:o,handleEndCall:eT,handleAcceptCall:()=>{q.current.pause(),C(!1),ej(!0),a(e=>({...e,receivingCall:!1})),eN()},handleMute:()=>{f(e=>!e),p?(eE.start(),F(!1)):(eE.pause(),F(!0))},mute:p}),ez&&!A&&(0,n.jsx)(R,{}),M&&!A&&(0,n.jsx)(u.Z,{isFullscreen:o,msg:O,setMsg:U})]})]})]})]})}var F=a(66648),P=e=>{let{name:t,image:a,isFullscreen:r,loader:o,showtext:l}=e,i=(0,s.useRecoilValue)(T.sE);return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center sm:-mt-12 -mt-3",children:[(0,n.jsx)("div",{className:"".concat(r?"2xl:w-56 2xl:h-56 w-48 h-48 ":"w-20 h-20 "," rounded-full overflow-hidden ").concat(o&&i?"shadow-[0_0_35px_rgba(214,42,94,0.5)]":""," ").concat(o&&"Wait"===l?"shadow-[0_0_35px_rgba(214,42,94,0.5)]":""),children:(0,n.jsx)(F.default,{src:a,width:128,height:128,alt:"".concat(t,"'s profile picture"),className:"aspect-square h-full w-full object-cover object-top",unoptimized:!0})}),l&&(0,n.jsx)("div",{className:"flex items-center justify-center mt-4",children:(0,n.jsxs)("div",{className:"flex gap-1",children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary animate-[fadeIn_1.5s_infinite_0ms]"}),(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary animate-[fadeIn_1.5s_infinite_500ms]"}),(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary animate-[fadeIn_1.5s_infinite_1000ms]"})]})})]})},V=function(e){let{handleEndCall:t,profileImage:a,name:r}=e;return(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-4 background border-[3.5px] border-primary",children:[(0,n.jsx)("div",{children:(0,n.jsx)(P,{name:"John Doe",image:a,isFullscreen:!1})}),(0,n.jsx)("h1",{className:"text-center",children:r}),(0,n.jsx)("h2",{className:"text-center",children:"Calling..."}),(0,n.jsx)("div",{className:"flex items-center justify-center gap-4",children:(0,n.jsx)(F.default,{src:"/icons/decline_call.svg",width:48,height:48,alt:"End Call",className:"cursor-pointer hover:opacity-80",onClick:t})})]})},O=function(e){let{isFullscreen:t,handleEndCall:a}=e;return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(m.z,{variant:"ghost",className:"flex flex-col ".concat(t?"2xl:gap-6 sm:gap-4 gap-2 sm:w-28 w-24":"sm:gap-1 sm:w-20"," focus-visible:ring-0 hover:bg-transparent items-center group end_button"),onClick:a,children:[(0,n.jsxs)("div",{className:"hover:brightness-[.9] transition-colors ease-in-out duration-300 relative ".concat(t?"2xl:scale-[1.7] sm:scale-[1.5] scale-[1.3]":"sm:scale-[1] scale-[.85]"),children:[(0,n.jsx)("div",{className:"bg-primary p-4 rounded-full"}),(0,n.jsx)(F.default,{src:"/icons/endCallVector.svg",width:"24",height:"24",alt:"end call",className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",unoptimized:!0})]}),(0,n.jsx)("span",{className:"".concat(t?"2xl:text-lg sm:text-sm text-[9px]":"sm:text-xs text-[9px]"," sm:font-extralight font-extralight"),children:"End Call"})]})})},U=a(57251),L=function(e){let{stopResponse:t,setStopResponse:a,showText:r,handleEndCall:o,isMuted:i,setIsMuted:c,transcript:d,response:u,audioAnimation:h,profileImage:g}=e,[f,x]=(0,s.useRecoilState)(T.FG),w=(0,p.P)();async function v(){try{let e=(await navigator.mediaDevices.enumerateDevices()).some(e=>""!==e.deviceId);return c(!e),e}catch(e){return console.error("Error checking microphone:",e),c(!0),!1}}return(0,l.useEffect)(()=>{v()},[]),(0,n.jsxs)("div",{className:"flex flex-col h-screen justify-center py-[100px] border-[3.5px] border-primary",children:[(0,n.jsxs)("div",{className:"flex flex-col justify-center pt-8",children:[(0,n.jsx)(P,{name:"John Doe",image:g,isFullscreen:!1,loader:!0,showtext:r}),f&&(0,n.jsxs)("h4",{className:"text-xs text-white font-extralight pt-1.5 text-center w-full",children:["Please click ",(0,n.jsx)("u",{className:"text-primary",onClick:()=>x(!1),children:"Here"})," to enable microphone"]})]}),(0,n.jsx)("div",{className:"flex-1 a overflow-y-auto px-4 py-4 flex justify-center items-center",children:h&&!i&&!t&&(0,n.jsx)(U.nI,{src:"https://lottie.host/8b977034-7d24-416f-aa4c-3292894a842f/juZYUkk0HJ.lottie",loop:!0,autoplay:!0,style:{width:"100px",height:"50px"}})}),(0,n.jsxs)("div",{className:"flex justify-center gap-3",children:[(0,n.jsx)(O,{isFullscreen:!1,handleEndCall:o}),(0,n.jsxs)(m.z,{variant:"ghost",className:"flex flex-col sm:gap-1 sm:w-20 focus-visible:ring-0 hover:bg-transparent items-center group",onClick:async()=>{await v()?a(!t):(0,y.default)("Please allow microphone access")},children:[(0,n.jsxs)("div",{className:"hover:brightness-[.9] transition-colors ease-in-out duration-300 relative sm:scale-[1] scale-[.85]",children:[(0,n.jsx)("div",{className:"bg-primary p-4 rounded-full"}),(0,n.jsx)("div",{className:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-white",children:i||t?w.MicrophoneOffSVG:w.MicrophoneOnSVG})]}),(0,n.jsx)("span",{className:"sm:text-xs text-[9px] sm:font-extralight font-extralight",children:i||t?"Unmute":"Mute"})]})]})]})},Z=a(80223),B=a(14738),q=a(18535),W=a(61023),H=a(22351),G=a(28798),J=a(82912);async function X(e,t,a){let n=e.replace(/(\*{1,2}[^*]*\*{1,2}|\[[^\]]*\]|\([^\)]*\))/g,"").trim();if(!n){console.error("Transcript empty");return}try{let e=new AbortController,r=setTimeout(()=>e.abort(),15e3),s=new G.Howl({src:["data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"]});s.play(),s.stop();let o=await (0,v.default)("https://api.cartesia.ai/tts/bytes",{method:"POST",headers:{"Cartesia-Version":"2024-06-30","Content-Type":"application/json","X-API-Key":"15ad5330-655a-445c-8d63-4abf3d79f915"},responseType:"arraybuffer",signal:e.signal,data:JSON.stringify({model_id:"sonic",transcript:n.replace(/\*/g,""),voice:{mode:"embedding",embedding:JSON.parse(t.voice_embedding||"")},output_format:{container:"wav",encoding:"pcm_f32le",sample_rate:24e3,streaming:!1}})});clearTimeout(r);let l=o.data,i=new Blob([l],{type:"audio/wav"}),c=URL.createObjectURL(i);a(!1);let d=new G.Howl({src:[c],format:["wav"],html5:!0,preload:!0,pool:1,onplayerror:function(){(0,J.setRecoil)(T.FG,!0),d.once("unlock",function(){(0,J.setRecoil)(T.FG,!1),d.play()})},onplay:()=>{(0,J.setRecoil)(T.sE,!0)},onend:()=>{(0,J.setRecoil)(T.sE,!1),URL.revokeObjectURL(c),d.unload()},onloaderror:()=>{console.error("Error loading audio"),URL.revokeObjectURL(c),a(!1)}});d.load(),d.play()}catch(e){v.default.isCancel(e)?console.log("Request cancelled:",e.message):console.error("Error playing Cartesia TTS:",e),a(!1)}}G.Howler.autoUnlock=!0;var K=function(e){let{open:t,setOpen:a}=e,[o,c]=(0,l.useState)(!0),[d,u]=(0,l.useState)(!0),[m,p]=(0,l.useState)(!1),[h,f]=(0,l.useState)(""),x=(0,l.useRef)(""),{data:w}=(0,B.j)(),v=(0,_.F)({}),b=(0,z.Z)(),j=(0,l.useRef)(!1),S=(0,l.useRef)(null),[N,A]=(0,l.useState)(null),[C,R]=(0,l.useState)(""),[E,D]=(0,l.useState)(""),[I,M]=(0,l.useState)(!1),[F,P]=(0,l.useState)([]),[O,U]=(0,l.useState)(0),[G,J]=(0,l.useState)(),[K,Q]=(0,l.useState)(!1),Y=Date.now(),$=(0,l.useRef)(null),ee=(0,s.useRecoilValue)(T.sE),et=(0,s.useSetRecoilState)(r.Md);(0,l.useEffect)(()=>{let e=async()=>{var e;let t=await navigator.mediaDevices.getUserMedia({audio:!0});S.current=new MediaRecorder(t),S.current.ondataavailable=async e=>{e.data.size>0&&N.send(e.data)},null==S||null===(e=S.current)||void 0===e||e.start(100)};return N&&!d&&e(),()=>{var e;null===(e=S.current)||void 0===e||e.stop()}},[N,d]),(0,l.useEffect)(()=>{x.current=h},[h]),(0,l.useEffect)(()=>(d||m||o||"Wait..."===E||!K||ee||!G||($.current=setInterval(()=>{Date.now()-O>1e4&&es("Hey, I can't hear you. Can you please repeat?",G)},1e4)),()=>{$.current&&clearInterval($.current)}),[d,o,E,ee,O,K]),(0,g.useMicVAD)({startOnLoad:!0,onSpeechEnd:e=>{Q(!0),M(!1),setTimeout(()=>{x.current.trim()&&(en(x.current,(null==w?void 0:w._id)||"",!1),f(""))},1e3)},onSpeechStart:()=>{Q(!1),M(!0)},modelURL:"/silero_vad.onnx",workletURL:"/vad.worklet.bundle.min.js",positiveSpeechThreshold:.6,minSpeechFrames:4,ortConfig(e){let t=/^((?!chrome|android).)*safari/i.test(navigator.userAgent);e.env.wasm={wasmPaths:{"ort-wasm-simd-threaded.wasm":"/ort-wasm-simd-threaded.wasm","ort-wasm-simd.wasm":"/ort-wasm-simd.wasm","ort-wasm.wasm":"/ort-wasm.wasm","ort-wasm-threaded.wasm":"/ort-wasm-threaded.wasm"},numThreads:t?1:4}}});let ea=async()=>{if(t.open&&!N){let e=null;try{(e=(0,q.eI)("a81521d94a97b58a3efdb315355900d33b9f2b4b").listen.live({model:"nova-2-phonecall",language:"en-US",smart_format:!0})).on(W.l.Open,()=>{e.on(W.l.Close,()=>{console.log("Connection closed.")}),e.on(W.l.Transcript,e=>{let t=e.channel.alternatives[0].transcript;t.trim()&&f(e=>(e+" "+t).trim())}),e.on(W.l.Error,e=>{console.error(e)})}),A(e),e.keepAlive()}catch(e){console.error("Failed to initialize Deepgram:",e),y.default.error("Failed to initialize speech recognition")}}};function en(e,t,a){t&&!m&&(D("Wait..."),Z.ZP.emit("ai_call_request",{transcript:e,userId:t,charId:localStorage.getItem("sd_active_char_id")||"",convId:v(),callInitialization:a,timeAndDate:b()}),e&&P(t=>[...t,{role:"user",content:e,llm_model:"cohere/command-r-08-2024",tokens_usage:0,latency:Date.now()}]))}(0,l.useEffect)(()=>(ea(),()=>{S.current&&"recording"===S.current.state&&S.current.stop()}),[t.open]);let er=async()=>{a(e=>({...e,open:!1})),Z.ZP.off("ai_call_response_".concat(null==w?void 0:w._id)),S.current&&"recording"===S.current.state&&S.current.stop(),N&&(N.requestClose(),A(null));let e=Date.now()-Y,t="".concat(Math.floor(e/6e4),".").concat(Math.floor(e%6e4/1e3).toString().padStart(2,"0"));await H.j.post("/voice/voice_call_save",{conv_id:v(),char_id:localStorage.getItem("sd_active_char_id")||"",msg_history:F,duration:t})},es=async(e,t)=>{if(!e&&!t){(0,y.default)("no data");return}ee||await X(e,t,c)};return(0,l.useEffect)(()=>(t.open&&(null==w?void 0:w._id)&&v()&&!j.current&&(j.current=!0,en("",null==w?void 0:w._id,!0)),()=>{Z.ZP.off("ai_call_request")}),[t.open,null==w?void 0:w._id,v]),(0,l.useEffect)(()=>(Z.ZP.on("ai_call_response_".concat(null==w?void 0:w._id),e=>{D(""),U(Date.now()),R(e.responseTranscript),P(t=>[...t,{role:"assistant",content:e.responseTranscript,llm_model:"qwen/qwen2.5-32b-instruct",tokens_usage:0,latency:Date.now()}]),"true"===e.end_call?(e.responseTranscript.includes("complianceerror")&&et([e.responseTranscript.split("_")[1]]),er()):(J(e.char),es(e.responseTranscript,e.char))}),()=>{Z.ZP.off("ai_call_response_".concat(null==w?void 0:w._id))}),[null==w?void 0:w._id]),(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(i.Vq,{modal:!0,open:t.open,onOpenChange:()=>a(e=>({...e,open:!e.open})),children:[(0,n.jsx)(k.$N,{className:"hidden","aria-label":"Call Dialog"}),(0,n.jsx)(i.cZ,{closeBtn:"hidden",onInteractOutside:e=>e.preventDefault(),className:"p-0  right-0 left-auto translate-x-0 translate-y-0  bg-background overflow-clip w-screen h-full max-w-none top-0",children:o?(0,n.jsx)(V,{profileImage:null==t?void 0:t.image,name:null==t?void 0:t.text,handleEndCall:er,handleAcceptCall:()=>{}}):(0,n.jsx)(L,{stopResponse:m,setStopResponse:p,showText:E,profileImage:null==t?void 0:t.image,audioAnimation:I,transcript:h,response:C,handleEndCall:er,isMuted:d,setIsMuted:u})})]})})};function Q(){(0,S.useRouter)();let[e,t]=(0,s.useRecoilState)(r.$d);return(0,n.jsx)(n.Fragment,{children:e.open&&!o.tq?(0,n.jsx)(M,{open:e,setOpen:t}):(0,n.jsx)(K,{open:e,setOpen:t})})}},47898:function(e,t,a){var n=a(57437),r=a(16880),s=a(3114),o=a(93146),l=a(99441),i=a(64269),c=a(2265),d=a(56533);t.Z=e=>{let{isFullscreen:t,msg:a,setMsg:u}=e;var m=(0,r.Z)();try{let e=(0,s.P)(),r=(0,c.useRef)(null),m=(0,l.useSetRecoilState)(i.S);(0,d.c)(r);let p=()=>{""!==a.trim()&&m(a),u("")};return(0,n.jsxs)("div",{className:"fixed flex flex-row justify-center items-end gap-1  ".concat(t?"sm:bottom-8 bottom-4":"sm:bottom-3"," "),children:[(0,n.jsx)(o.g,{ref:r,className:"".concat(t?"sm:w-[60vw] w-[80vw] md:text-base md:min-h-10 md:max-h-10":"lg:text-sm sm:w-96"," custom-scrollbar overflow-y-scroll line-clamp-3  min-h-[33px] max-h-[33px]\n						    ring-offset-primary \n                    tracking-wide text-xs focus:outline-primary focus-visible:outline-none focus-visible:ring-0 focus-visible:ring-none focus-visible:ring-offset-1"),placeholder:"Type a message here",value:a,onChange:e=>u(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),p())}}),(0,n.jsx)("button",{"aria-label":"Send text message as a call input/prompt",type:"submit",onClick:p,className:"disabled:from-[#4c4c4c] disabled:to-[#4c4c4c] rounded-full p-1 bg-linear-to-b from-[#d42a5d] to-[#7e2d46]",children:(0,n.jsxs)("div",{className:"scale-[.8]",children:[e.sendIcon," "]})})]})}finally{m.f()}}},56533:function(e,t,a){a.d(t,{c:function(){return s}});var n=a(2265),r=a(63858);function s(e){let t=(0,n.useRef)(0),a=(0,n.useRef)(0);(0,n.useEffect)(()=>{if(!r.tq||!e.current)return;a.current=window.innerHeight;let n=()=>{t.current=window.scrollY,setTimeout(()=>{var t;let n=null===(t=e.current)||void 0===t?void 0:t.getBoundingClientRect();if(!n)return;let r=window.innerHeight,s=n.bottom,o=a.current===r?.4*r:a.current-r,l=r-o-40;if(s>l){let e=s-l;setTimeout(()=>{window.scrollTo({top:window.scrollY+e,behavior:"smooth"})},50)}},300)},s=()=>{setTimeout(()=>{window.scrollTo({top:t.current,behavior:"smooth"})},100)},o=e.current;return o.addEventListener("focus",n),o.addEventListener("blur",s),()=>{o.removeEventListener("focus",n),o.removeEventListener("blur",s)}},[e])}},64269:function(e,t,a){a.d(t,{S:function(){return n}});let n=(0,a(99441).atom)({key:"callTextMessageAtom",default:""})},76383:function(e,t,a){a.d(t,{R:function(){return r}});let n=()=>"undefined"!=typeof navigator&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream,r=()=>{let e=document.querySelector("meta[name=viewport]");e||((e=document.createElement("meta")).name="viewport",document.head.appendChild(e)),console.log("setting viewport settings"),n()?e.setAttribute("content","width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no"):e.setAttribute("content","width=device-width, initial-scale=1, viewport-fit=cover")}}}]);