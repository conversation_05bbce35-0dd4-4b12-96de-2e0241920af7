"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[784],{16463:function(e,n,t){var r=t(71169);t.o(r,"useParams")&&t.d(n,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(n,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(n,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(n,{useSearchParams:function(){return r.useSearchParams}})},13304:function(e,n,t){t.d(n,{$N:function(){return V},Dx:function(){return et},VY:function(){return en},Vq:function(){return j},aV:function(){return ee},cZ:function(){return k},dk:function(){return er},fC:function(){return G},h_:function(){return Q},jm:function(){return K},p8:function(){return D},x8:function(){return eo},xz:function(){return J}});var r=t(2265),o=t(78149),u=t(1584),i=t(98324),a=t(53201),l=t(91715),s=t(53938),c=t(80467),d=t(56935),f=t(31383),p=t(25171),m=t(20589),v=t(17590),g=t(78369),y=t(71538),N=t(57437),h="Dialog",[b,D]=(0,i.b)(h),[O,R]=b(h),j=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,c=r.useRef(null),d=r.useRef(null),[f=!1,p]=(0,l.T)({prop:o,defaultProp:u,onChange:i});return(0,N.jsx)(O,{scope:n,triggerRef:c,contentRef:d,contentId:(0,a.M)(),titleId:(0,a.M)(),descriptionId:(0,a.M)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:t})};j.displayName=h;var x="DialogTrigger",I=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=R(x,t),a=(0,u.e)(n,i.triggerRef);return(0,N.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":q(i.open),...r,ref:a,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});I.displayName=x;var E="DialogPortal",[M,C]=b(E,{forceMount:void 0}),P=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:u}=e,i=R(E,n);return(0,N.jsx)(M,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,N.jsx)(f.z,{present:t||i.open,children:(0,N.jsx)(d.h,{asChild:!0,container:u,children:e})}))})};P.displayName=E;var w="DialogOverlay",_=r.forwardRef((e,n)=>{let t=C(w,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,u=R(w,e.__scopeDialog);return u.modal?(0,N.jsx)(f.z,{present:r||u.open,children:(0,N.jsx)(A,{...o,ref:n})}):null});_.displayName=w;var A=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(w,t);return(0,N.jsx)(v.Z,{as:y.g7,allowPinchZoom:!0,shards:[o.contentRef],children:(0,N.jsx)(p.WV.div,{"data-state":q(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),T="DialogContent",k=r.forwardRef((e,n)=>{let t=C(T,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,u=R(T,e.__scopeDialog);return(0,N.jsx)(f.z,{present:r||u.open,children:u.modal?(0,N.jsx)(F,{...o,ref:n}):(0,N.jsx)(W,{...o,ref:n})})});k.displayName=T;var F=r.forwardRef((e,n)=>{let t=R(T,e.__scopeDialog),i=r.useRef(null),a=(0,u.e)(n,t.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,N.jsx)(U,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null===(n=t.triggerRef.current)||void 0===n||n.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),W=r.forwardRef((e,n)=>{let t=R(T,e.__scopeDialog),o=r.useRef(!1),u=r.useRef(!1);return(0,N.jsx)(U,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,i;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current||null===(i=t.triggerRef.current)||void 0===i||i.focus(),n.preventDefault()),o.current=!1,u.current=!1},onInteractOutside:n=>{var r,i;null===(r=e.onInteractOutside)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"!==n.detail.originalEvent.type||(u.current=!0));let a=n.target;(null===(i=t.triggerRef.current)||void 0===i?void 0:i.contains(a))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&u.current&&n.preventDefault()}})}),U=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...l}=e,d=R(T,t),f=r.useRef(null),p=(0,u.e)(n,f);return(0,m.EW)(),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,N.jsx)(s.XB,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":q(d.open),...l,ref:p,onDismiss:()=>d.onOpenChange(!1)})}),(0,N.jsxs)(N.Fragment,{children:[(0,N.jsx)(Y,{titleId:d.titleId}),(0,N.jsx)($,{contentRef:f,descriptionId:d.descriptionId})]})]})}),S="DialogTitle",V=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(S,t);return(0,N.jsx)(p.WV.h2,{id:o.titleId,...r,ref:n})});V.displayName=S;var L="DialogDescription",z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=R(L,t);return(0,N.jsx)(p.WV.p,{id:o.descriptionId,...r,ref:n})});z.displayName=L;var B="DialogClose",Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,u=R(B,t);return(0,N.jsx)(p.WV.button,{type:"button",...r,ref:n,onClick:(0,o.M)(e.onClick,()=>u.onOpenChange(!1))})});function q(e){return e?"open":"closed"}Z.displayName=B;var H="DialogTitleWarning",[K,X]=(0,i.k)(H,{contentName:T,titleName:S,docsSlug:"dialog"}),Y=e=>{let{titleId:n}=e,t=X(H),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&!document.getElementById(n)&&console.error(o)},[o,n]),null},$=e=>{let{contentRef:n,descriptionId:t}=e,o=X("DialogDescriptionWarning"),u="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=n.current)||void 0===e?void 0:e.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(u)},[u,n,t]),null},G=j,J=I,Q=P,ee=_,en=k,et=V,er=z,eo=Z},31383:function(e,n,t){t.d(n,{z:function(){return a}});var r=t(2265),o=t(54887),u=t(1584),i=t(1336),a=e=>{var n,t;let a,s;let{present:c,children:d}=e,f=function(e){var n,t;let[u,a]=r.useState(),s=r.useRef({}),c=r.useRef(e),d=r.useRef("none"),[f,p]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=l(s.current);d.current="mounted"===f?e:"none"},[f]),(0,i.b)(()=>{let n=s.current,t=c.current;if(t!==e){let r=d.current,o=l(n);e?p("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?p("UNMOUNT"):t&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),c.current=e}},[e,p]),(0,i.b)(()=>{if(u){let e=e=>{let n=l(s.current).includes(e.animationName);e.target===u&&n&&o.flushSync(()=>p("ANIMATION_END"))},n=e=>{e.target===u&&(d.current=l(s.current))};return u.addEventListener("animationstart",n),u.addEventListener("animationcancel",e),u.addEventListener("animationend",e),()=>{u.removeEventListener("animationstart",n),u.removeEventListener("animationcancel",e),u.removeEventListener("animationend",e)}}p("ANIMATION_END")},[u,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),a(e)},[])}}(c),p="function"==typeof d?d({present:f.isPresent}):r.Children.only(d),m=(0,u.e)(f.ref,(a=null===(n=Object.getOwnPropertyDescriptor(p.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in a&&a.isReactWarning?p.ref:(a=null===(t=Object.getOwnPropertyDescriptor(p,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in a&&a.isReactWarning?p.props.ref:p.props.ref||p.ref);return"function"==typeof d||f.isPresent?r.cloneElement(p,{ref:m}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},12218:function(e,n,t){t.d(n,{j:function(){return u}});let r=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,o=function(){for(var e,n,t=0,r="";t<arguments.length;)(e=arguments[t++])&&(n=function e(n){var t,r,o="";if("string"==typeof n||"number"==typeof n)o+=n;else if("object"==typeof n){if(Array.isArray(n))for(t=0;t<n.length;t++)n[t]&&(r=e(n[t]))&&(o&&(o+=" "),o+=r);else for(t in n)n[t]&&(o&&(o+=" "),o+=t)}return o}(e))&&(r&&(r+=" "),r+=n);return r},u=(e,n)=>t=>{var u;if((null==n?void 0:n.variants)==null)return o(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:a}=n,l=Object.keys(i).map(e=>{let n=null==t?void 0:t[e],o=null==a?void 0:a[e];if(null===n)return null;let u=r(n)||r(o);return i[e][u]}),s=t&&Object.entries(t).reduce((e,n)=>{let[t,r]=n;return void 0===r||(e[t]=r),e},{});return o(e,l,null==n?void 0:null===(u=n.compoundVariants)||void 0===u?void 0:u.reduce((e,n)=>{let{class:t,className:r,...o}=n;return Object.entries(o).every(e=>{let[n,t]=e;return Array.isArray(t)?t.includes({...a,...s}[n]):({...a,...s})[n]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}}}]);