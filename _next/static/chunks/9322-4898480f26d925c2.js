"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9322,6511],{39952:function(e,t,n){n.d(t,{I:function(){return i}});var r=n(15236);n(97175);var o=n(75735);let a=(0,r.ZF)({apiKey:"AIzaSyDa9-0wJAofNhhgK9KVHdbofs_tkVzNjh0",authDomain:"secret-desires-67796.firebaseapp.com",projectId:"secret-desires-67796",storageBucket:"secret-desires-67796.appspot.com",messagingSenderId:"889786157837",appId:"1:889786157837:web:4c3052c228929ac0bdbe44",measurementId:"G-WS929YTP5C"}),i=(0,o.v0)(a)},53428:function(e,t,n){n.d(t,{HX:function(){return l},YI:function(){return o},h_:function(){return u},k1:function(){return c},kB:function(){return a},x2:function(){return i}});var r=n(57437);function o(e){let{color:t,className:n=""}=e;return(0,r.jsxs)("svg",{className:n,width:"100",height:"100",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsxs)("g",{clipPath:"url(#clip0_3295_196)",children:[(0,r.jsx)("path",{d:"M14.229 3.64959C16.2642 3.64959 18.2537 4.25309 19.9458 5.38377C21.638 6.51445 22.9569 8.12153 23.7357 10.0018C24.5146 11.882 24.7183 13.951 24.3213 15.9471C23.9243 17.9431 22.9442 19.7766 21.5051 21.2157C20.0661 22.6548 18.2326 23.6348 16.2365 24.0319C14.2404 24.4289 12.1715 24.2251 10.2912 23.4463C8.41095 22.6675 6.80387 21.3486 5.67319 19.6564C4.54251 17.9642 3.93902 15.9748 3.93902 13.9396C3.95403 11.2151 5.04297 8.60655 6.96947 6.68005C8.89598 4.75354 11.5046 3.6646 14.229 3.64959ZM14.229 1.36292C11.7416 1.36292 9.31002 2.10053 7.24179 3.48247C5.17357 4.86442 3.56159 6.82862 2.60969 9.12671C1.65779 11.4248 1.40873 13.9535 1.89401 16.3932C2.37928 18.8328 3.57709 21.0738 5.33597 22.8326C7.09485 24.5915 9.3358 25.7893 11.7754 26.2746C14.2151 26.7599 16.7438 26.5108 19.0419 25.5589C21.34 24.607 23.3042 22.995 24.6861 20.9268C26.0681 18.8586 26.8057 16.427 26.8057 13.9396C26.8132 12.2859 26.4931 10.6471 25.8638 9.11777C25.2344 7.58848 24.3083 6.19903 23.1389 5.02967C21.9696 3.86031 20.5801 2.93421 19.0508 2.30485C17.5215 1.67549 15.8827 1.35536 14.229 1.36292Z",fill:"currentColor"}),(0,r.jsx)("path",{d:"M19.5458 7.82289L11.9426 11.6531L8.11244 19.2562C8.07369 19.3678 8.06715 19.488 8.09357 19.6032C8.11998 19.7183 8.17829 19.8237 8.26182 19.9072C8.34534 19.9907 8.4507 20.049 8.56583 20.0754C8.68095 20.1019 8.80119 20.0953 8.91278 20.0566L16.5159 16.2264L20.3461 8.62322C20.3849 8.51164 20.3914 8.3914 20.365 8.27628C20.3386 8.16115 20.2803 8.05579 20.1967 7.97227C20.1132 7.88874 20.0078 7.83043 19.8927 7.80402C19.7776 7.7776 19.6574 7.78414 19.5458 7.82289ZM14.2293 15.0831C14.0031 15.0831 13.7821 15.016 13.5941 14.8904C13.4061 14.7647 13.2595 14.5862 13.173 14.3773C13.0864 14.1683 13.0638 13.9385 13.1079 13.7167C13.152 13.4949 13.2609 13.2912 13.4208 13.1313C13.5807 12.9714 13.7844 12.8625 14.0062 12.8184C14.228 12.7742 14.4579 12.7969 14.6668 12.8834C14.8757 12.97 15.0543 13.1165 15.1799 13.3045C15.3056 13.4925 15.3726 13.7136 15.3726 13.9397C15.3726 14.243 15.2522 14.5338 15.0377 14.7482C14.8233 14.9626 14.5325 15.0831 14.2293 15.0831Z",fill:"currentColor"})]}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_3295_196",children:(0,r.jsx)("rect",{width:"27.44",height:"27.44",fill:"white",transform:"translate(0.508789 0.219727)"})})})]})}function a(e){let{color:t,background:n,className:o=""}=e;return(0,r.jsxs)("svg",{width:"100",height:"100",className:o,viewBox:"0 0 27 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M16.4977 23.6258L15.8779 24.673C15.3255 25.6063 13.9455 25.6063 13.393 24.673L12.7732 23.6258C12.2925 22.8135 12.0521 22.4076 11.666 22.1829C11.2799 21.9583 10.7938 21.95 9.82157 21.9333C8.38627 21.9085 7.48609 21.8206 6.73114 21.5079C5.33039 20.9276 4.21751 19.8148 3.6373 18.414C3.20215 17.3635 3.20215 16.0316 3.20215 13.368V12.2247C3.20215 8.48206 3.20215 6.61074 4.04456 5.23607C4.51593 4.46686 5.16266 3.82013 5.93187 3.34876C7.30654 2.50635 9.17786 2.50635 12.9205 2.50635H16.3505C20.0931 2.50635 21.9644 2.50635 23.3391 3.34876C24.1083 3.82013 24.755 4.46686 25.2264 5.23607C26.0688 6.61074 26.0688 8.48206 26.0688 12.2247V13.368C26.0688 16.0316 26.0688 17.3635 25.6337 18.414C25.0534 19.8148 23.9406 20.9276 22.5398 21.5079C21.7849 21.8206 20.8847 21.9085 19.4494 21.9333C18.4771 21.95 17.991 21.9583 17.6049 22.1829C17.2188 22.4074 16.9784 22.8135 16.4977 23.6258Z",fill:t}),(0,r.jsx)("path",{d:"M13.4806 16.5786C11.9636 15.4607 9.49023 13.3306 9.49023 11.3268C9.49023 8.26588 12.3201 7.12309 14.6352 9.4876C16.9504 7.12309 19.7802 8.26588 19.7802 11.3268C19.7802 13.3306 17.3069 15.4607 15.7899 16.5786C15.2707 16.9614 15.011 17.1527 14.6352 17.1527C14.2594 17.1527 13.9998 16.9614 13.4806 16.5786Z",fill:n})]})}function i(e){let{color:t,className:n=""}=e;return(0,r.jsxs)("svg",{className:n,width:"100%",height:"100%",viewBox:"0 0 28 28",transform:"translate(3,1)",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("g",{clipPath:"url(#clip0_3153_623)",children:(0,r.jsx)("path",{d:"M0.523438 18.8104C0.523438 19.9519 0.925891 20.9177 1.7308 21.708C2.5357 22.4983 3.50891 22.9007 4.65041 22.9154H18.3704C19.4973 22.9154 20.4632 22.5129 21.2681 21.708C22.073 20.9031 22.4754 19.9372 22.4754 18.8104V5.09035C22.4754 3.94885 22.073 2.97565 21.2681 2.17074C20.4632 1.36583 19.4973 0.963379 18.3704 0.963379H4.65041C3.50891 0.963379 2.5357 1.36583 1.7308 2.17074C0.925891 2.97565 0.523437 3.94885 0.523438 5.09035L0.523438 18.8104ZM3.26744 18.8104V5.09035C3.26744 4.70985 3.39915 4.38789 3.66257 4.12447C3.926 3.86104 4.25528 3.72201 4.65041 3.70738H18.3704C18.7363 3.70738 19.0582 3.84641 19.3363 4.12447C19.6144 4.40253 19.7461 4.72449 19.7314 5.09035V18.8104C19.7314 19.1909 19.5997 19.5128 19.3363 19.7762C19.0729 20.0397 18.7509 20.1714 18.3704 20.1714H4.65041C4.26991 20.1714 3.94063 20.0397 3.66257 19.7762C3.38452 19.5128 3.2528 19.1909 3.26744 18.8104ZM6.01144 11.9394C6.01144 12.3199 6.14315 12.6492 6.40657 12.9272C6.67 13.2053 6.99928 13.337 7.39441 13.3224H10.1384V16.0664C10.1384 16.4469 10.2701 16.7688 10.5335 17.0322C10.797 17.2957 11.1189 17.4274 11.4994 17.4274C11.8799 17.4274 12.2019 17.2957 12.4653 17.0322C12.7288 16.7688 12.8678 16.4469 12.8824 16.0664V13.3224H15.6264C15.9923 13.3224 16.3142 13.1906 16.5923 12.9272C16.8704 12.6638 17.0021 12.3345 16.9874 11.9394C16.9728 11.5442 16.8411 11.2223 16.5923 10.9735C16.3435 10.7247 16.0216 10.593 15.6264 10.5784H12.8824V7.83435C12.8824 7.45385 12.7434 7.13189 12.4653 6.86847C12.1873 6.60504 11.8653 6.46601 11.4994 6.45138C11.1336 6.43674 10.8116 6.57577 10.5335 6.86847C10.2555 7.16116 10.1238 7.48312 10.1384 7.83435V10.5784H7.39441C7.01391 10.5784 6.68463 10.7101 6.40657 10.9735C6.12851 11.2369 5.9968 11.5589 6.01144 11.9394Z",fill:t})}),(0,r.jsx)("defs",{children:(0,r.jsx)("clipPath",{id:"clip0_3153_623",children:(0,r.jsx)("rect",{width:"100",height:"100",fill:"white",transform:"translate(0.523438 0.963379)"})})})]})}function l(e){let{color:t,className:n=""}=e;return(0,r.jsxs)("svg",{className:n,width:"29",height:"28",viewBox:"0 0 29 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M25.6881 19.0174C25.7836 17.8456 25.7964 16.4175 25.7981 14.7372C25.7985 14.2967 25.4411 13.9394 25.0006 13.9394C24.5601 13.9394 24.2031 14.2968 24.2027 14.7373C24.201 16.4267 24.1885 17.7765 24.0979 18.8879C24.0019 20.0673 23.8219 20.9034 23.519 21.5398L20.5086 18.8305C19.403 17.8354 17.7564 17.7363 16.5395 18.5915L16.2223 18.8145C15.3766 19.4088 14.2261 19.3092 13.4951 18.5783L8.93268 14.0158C8.02205 13.1052 6.56132 13.0565 5.59213 13.9046L4.52736 14.8362C4.52699 14.547 4.52699 14.2483 4.52699 13.9394C4.52699 11.41 4.52868 9.59321 4.71458 8.21059C4.89737 6.85092 5.24505 6.03121 5.85091 5.42535C6.45678 4.81948 7.27649 4.47181 8.63615 4.289C9.86883 4.12327 11.4466 4.10396 13.5668 4.10172C14.0073 4.10125 14.365 3.74429 14.365 3.30374C14.365 2.86321 14.0075 2.50588 13.5669 2.50635C11.4763 2.50853 9.75861 2.52839 8.42358 2.70788C6.88701 2.91447 5.67452 3.34558 4.72283 4.29726C3.77114 5.24895 3.34004 6.46145 3.13345 7.99802C2.93162 9.49921 2.93163 11.4232 2.93164 13.8784V13.9404C2.93164 14.5016 2.93164 15.0335 2.93385 15.5363C2.94249 17.5134 2.98413 19.1054 3.21173 20.3851C3.44343 21.6878 3.87913 22.7378 4.72283 23.5816C5.67452 24.5332 6.88701 24.9643 8.42358 25.1709C9.92478 25.3727 11.8488 25.3727 14.3039 25.3727H14.426C16.8811 25.3727 18.8052 25.3727 20.3064 25.1709C21.8429 24.9643 23.0554 24.5332 24.0072 23.5816C24.3425 23.2463 24.6179 22.8734 24.842 22.4572C25.3502 21.5135 25.5773 20.3773 25.6881 19.0174Z",fill:t}),(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M20.6528 12.7961C18.2275 12.7961 17.0147 12.7961 16.2613 12.0426C15.5078 11.2892 15.5078 10.0765 15.5078 7.6511C15.5078 5.22573 15.5078 4.01304 16.2613 3.25957C17.0147 2.5061 18.2275 2.5061 20.6528 2.5061C23.0782 2.5061 24.2909 2.5061 25.0444 3.25957C25.7978 4.01304 25.7978 5.22573 25.7978 7.6511C25.7978 10.0765 25.7978 11.2892 25.0444 12.0426C24.2909 12.7961 23.0782 12.7961 20.6528 12.7961ZM18.9896 9.61305C18.1152 8.84638 17.2228 7.82481 17.2228 6.84851C17.2228 4.82124 19.1093 4.06435 20.6528 5.6304C22.1963 4.06435 24.0828 4.82124 24.0828 6.84849C24.0828 7.82482 23.1904 8.84639 22.3161 9.61306C21.6345 10.2106 21.2938 10.5094 20.6528 10.5094C20.0119 10.5094 19.6711 10.2106 18.9896 9.61305Z",fill:t})]})}let c=e=>{let{color:t="white",className:n=""}=e;return(0,r.jsx)("svg",{width:"100",height:"100",viewBox:"0 0 27 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:n,children:(0,r.jsx)("path",{d:"M23.1496 6.7248H20.9496M23.1496 12.4998H17.6496M23.1496 18.2748H17.6496M7.74961 20.1998V14.0025C7.74961 13.8023 7.74961 13.7022 7.72715 13.6064C7.70723 13.5215 7.67427 13.4393 7.62918 13.362C7.57835 13.275 7.50688 13.1968 7.36394 13.0404L3.73528 9.07163C3.59234 8.91529 3.52087 8.83712 3.47004 8.75007C3.42495 8.67284 3.39199 8.59063 3.37207 8.50568C3.34961 8.40993 3.34961 8.30982 3.34961 8.1096V6.3398C3.34961 5.80076 3.34961 5.53123 3.4695 5.32534C3.57497 5.14424 3.74324 4.99699 3.95022 4.90471C4.18552 4.7998 4.49355 4.7998 5.10961 4.7998H14.7896C15.4057 4.7998 15.7137 4.7998 15.949 4.90471C16.156 4.99699 16.3242 5.14424 16.4297 5.32534C16.5496 5.53123 16.5496 5.80076 16.5496 6.3398V8.1096C16.5496 8.30982 16.5496 8.40993 16.5272 8.50568C16.5073 8.59063 16.4743 8.67284 16.4292 8.75007C16.3783 8.83712 16.3068 8.91529 16.1639 9.07163L12.5353 13.0404C12.3924 13.1968 12.3209 13.275 12.2701 13.362C12.225 13.4393 12.192 13.5215 12.172 13.6064C12.1496 13.7022 12.1496 13.8023 12.1496 14.0025V17.3123L7.74961 20.1998Z",stroke:t,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})},u=()=>(0,r.jsx)("svg",{viewBox:"0 0 24 24",className:"absolute animate-pulse text-white!",width:"32px",height:"32px",style:{top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:20},children:(0,r.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.79544 4.16635C6.23434 4.27607 5.61457 4.56036 5.09669 4.94552C4.56822 5.33858 3.96639 6.12946 3.73283 6.7378C3.47265 7.41551 3.44195 7.59107  3.43962 8.41576C3.43776 9.06466 3.45021 9.22696 3.52894 9.58022C3.8713 11.1158 4.62349 12.345 6.1818 13.9154C7.20152 14.9431 8.17905 15.7504 9.42289 16.592C9.86243 16.8895 10.7544 17.4582 11.0343 17.6195C11.0969 17.6555 11.4079 17.4719 12.4053  16.8101C14.1693 15.6395 15.8072 14.2409 16.7518 13.0986C16.8599 12.9678 16.9738 12.8339 17.0047 12.8011C17.1059 12.694 17.4689 12.1884 17.6905 11.8461C18.1671 11.1098 18.4649 10.4019 18.6328 9.60625C18.746 9.06938 18.7753 8.07167 18.6905 7.64166C18.5037  6.69403 18.1478 6.00982 17.5023 5.35692C16.9478 4.7962 16.3107 4.41881 15.5855 4.22167C15.0823 4.08492 14.3296 4.0927 13.7965  4.24016C12.9336 4.47889 12.1482 4.96739 11.4125 5.72303L11.0913 6.05292L10.7211 5.68295C9.97811 4.94032 9.17069 4.44873 8.32333  4.22305C7.93293 4.11905 7.18015 4.09111 6.79544 4.16635Z",fill:"white"})})},22514:function(e,t,n){n.d(t,{a:function(){return u}});var r=n(57437),o=n(2265),a=n(13498),i=n(53428),l=n(44839);let c=(0,o.memo)(function(e){let{mainCircleSize:t=80,mainCircleOpacity:n=.24,numCircles:c=4,className:u,percent:s=0,...d}=e,[g,m]=(0,o.useState)(t);return(0,o.useEffect)(()=>{let e=()=>{m(window.innerWidth<900?10:window.innerWidth<1500?50:t)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[t]),(0,r.jsxs)("div",{className:(0,a.cn)("pointer-events-none absolute inset-0 select-none",u),...d,children:[Array.from({length:c},(e,t)=>{let o=g+40*t,a=5+5*t;return(0,r.jsx)("div",{style:{"--i":t+1,"--duration":"2s",width:"".concat(o,"px"),height:"".concat(o,"px"),opacity:n-.03*t,borderStyle:t===c-1?"dashed":"solid",backgroundColor:"#f50057",borderColor:"#f50057".concat(a),top:"50%",left:"50%",transform:"translate(-50%, -50%) scale(1)",zIndex:1},className:(0,l.Z)("absolute rounded-full shadow-xl",{"animate-ripple":s>25*t})},t)}),(0,r.jsx)(i.h_,{})]})});c.displayName="Ripple";let u=e=>{let{size:t=60,percent:n=100}=e;return(0,r.jsx)(c,{mainCircleSize:t,percent:n})}},9962:function(e,t,n){n.d(t,{Z:function(){return s}});var r=n(57437),o=n(2265),a=n(27071),i=n(13498);a.zt;let l=a.fC,c=a.xz,u=o.forwardRef((e,t)=>{let{className:n,sideOffset:o=4,...l}=e;return(0,r.jsx)(a.VY,{ref:t,side:"bottom",sideOffset:o,className:(0,i.cn)("z-50 rounded-md overflow-hidden border border-gray-600 bg-popover px-2 py-1 text-[0.7rem] text-popover-foreground shadow-xl animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 data-[side=bottom]:slide-in-from-top-2",n),...l})});u.displayName=a.VY.displayName;var s=e=>{let{trigger:t,content:n,side:o,className:a}=e;return(0,r.jsxs)(l,{delayDuration:500,children:[(0,r.jsx)(c,{children:t}),(0,r.jsx)(u,{side:o,className:a||"",children:n})]})}},93146:function(e,t,n){n.d(t,{g:function(){return i}});var r=n(57437),o=n(2265),a=n(13498);let i=o.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,r.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-[#444] bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-primary  focus-visible:ring-0 focus-visible:ring-none focus-visible:ring-offset-1 focus-visible:outline-none focus-visible:ring-[#444] disabled:cursor-not-allowed disabled:opacity-50",n),ref:t,...o})});i.displayName="Textarea"},61349:function(e,t,n){n.d(t,{SocketProvider:function(){return m},m:function(){return f},s:function(){return g}});var r=n(57437),o=n(2265),a=n(34999),i=n(13498),l=n(28590),c=n(22351),u=n(56688),s=n(99441);let d=(0,o.createContext)({socket:null,isConnected:!1,registerImageGenerationCallback:()=>{},unregisterImageGenerationCallback:()=>{},addPendingGeneration:()=>{},removePendingGeneration:()=>{},trackProfileImageGeneration:()=>{},trackExistingProfileImageGeneration:()=>{},trackImageRegeneration:()=>{},trackChatImageRegeneration:()=>{},trackLiveImageGeneration:()=>{},trackNewChatImageGeneration:()=>{},trackNewCharImageGeneration:()=>{}}),g=()=>(0,o.useContext)(d),m=e=>{let{children:t}=e,n=(0,o.useRef)(null),[l,g]=(0,o.useState)(!1),m=(0,o.useRef)({}),f=(0,o.useRef)({}),C=(0,s.useSetRecoilState)(u.FQ),h=(0,s.useSetRecoilState)(u.SI),I=(0,s.useSetRecoilState)(u.tJ),p=(0,s.useSetRecoilState)(u.hn),v=(0,s.useSetRecoilState)(u.w8),y=e=>{{let t=JSON.parse(localStorage.getItem("pendingRunIds")||"[]");t.includes(e)||(t.push(e),localStorage.setItem("pendingRunIds",JSON.stringify(t)))}},w=e=>{{let t=JSON.parse(localStorage.getItem("pendingRunIds")||"[]").filter(t=>t!==e);localStorage.setItem("pendingRunIds",JSON.stringify(t))}},k=(e,t)=>{console.log("registerImageGenerationCallback",e),console.log("imageGenerationCallbacks.current",m.current),m.current[e]||(m.current[e]=[]),m.current[e].push(t)},x=e=>{console.log("❌ Image generation failed for runId: ".concat(e.runId)),!e.isRetry&&(C(e.runId),h(t=>[...t,e.runId]),I(e=>e+1),p("Image"),e.convId&&v(e.convId)),w(e.runId)},D=e=>{if("failed"==e.status){x(e);return}"success"===e.status&&e.imageUrl&&(e.charId&&e.msgId&&c.E.setQueryData(["msg-history",{charId:e.charId}],t=>{if(!t)return t;let n=t.pages.map(t=>({...t,msg:t.msg.map(t=>t._id===e.msgId?{...t,image:e.imageUrl,status:e.status}:t)}));return{...t,pages:n}}),e.charId&&e.imageUrl&&e.msgId&&c.E.setQueryData(["gen-images",{charId:e.charId}],t=>{if(!t)return t;let n={_id:e.msgId,created_date:new Date().toISOString(),imageUrl:e.imageUrl,imageType:"message",image_prompt:e.prompt||"",live_photo:e.live_photo||!1};return t.msg&&t.msg.some(e=>e._id===n._id)?t:{...t,msg:t.msg&&t.msg.length>0?[t.msg[0],n,...t.msg.slice(1)]:[n]}}),c.E.removeQueries({queryKey:["get-image-reply"]}),e.charId&&(c.E.invalidateQueries({queryKey:["gen-images",{charId:e.charId}]}),c.E.invalidateQueries({queryKey:["msg-history",{charId:e.charId}]})))};(0,o.useEffect)(()=>{if(!n.current){console.log("Connecting: "+i.i7),n.current=(0,a.io)(i.i7,{reconnection:!0,reconnectionAttempts:20,reconnectionDelay:1e3,reconnectionDelayMax:5e3,timeout:2e4,transports:["websocket"],withCredentials:!0});let e=n.current;e.on("connect",()=>{console.log("✅ Connected with id: ".concat(e.id," and env is ").concat("development")),g(!0),JSON.parse(localStorage.getItem("pendingRunIds")||"[]").forEach(t=>{e.emit("reconnect-image-generation",{runId:t})})}),e.on("disconnect",()=>{console.log("❌ Disconnected"),g(!1)}),e.on("reconnect",e=>{console.log("\uD83D\uDD01 Reconnected after ".concat(e," attempts")),g(!0)}),e.on("image-generated1",e=>{if(console.log("\uD83D\uDCE5 Received 'image-generated1' event:",e),!e.runId){console.log("❌ image-generated1: No runId in data");return}if("failed"==e.status){x(e);let t=m.current[e.runId];t&&t.forEach(t=>{t({runId:e.runId,status:"failed",error:e.error||"Image generation failed",isRetry:e.isRetry})});return}console.log("\uD83D\uDCDD Updating image generation status for runId: ".concat(e.runId)),f.current[e.runId]={status:"success"==e.status?"success":"failed",imageUrl:e.image_url,error:e.error},console.log("\uD83D\uDCCA Updated status:",f.current[e.runId]);{let t=JSON.parse(localStorage.getItem("pendingRunIds")||"[]");console.log("\uD83D\uDCCB Current pending runIds:",t),console.log("\uD83D\uDD0D Checking if runId ".concat(e.runId," is in pending list:"),t.includes(e.runId)),t.includes(e.runId)?(console.log("✅ RunId ".concat(e.runId," found in pending list")),"success"==e.status?(console.log("✅ Image generation successful, updating cache and removing from pending"),D(e),w(e.runId)):console.log("❌ Image generation failed for runId: ".concat(e.runId,", status: ").concat(e.status,", error: ").concat(e.error))):console.log("⚠️ RunId ".concat(e.runId," not found in pending list"))}let t=m.current[e.runId];t?(console.log("\uD83D\uDCDE Found ".concat(t.length," callback(s) for runId: ").concat(e.runId,", executing them")),t.forEach((n,r)=>{console.log("\uD83D\uDCDE Executing callback ".concat(r+1,"/").concat(t.length," for runId: ").concat(e.runId)),n(e)})):console.log("\uD83D\uDCDE No callbacks found for runId: ".concat(e.runId)),console.log("✅ image-generated1 processing completed for runId: ".concat(e.runId))}),e.on("profile-image-generated",e=>{if(e.runId){if("failed"===e.status){x(e),m.current[e.runId]&&m.current[e.runId].forEach(t=>{t({runId:e.runId,status:"failed",error:e.error||"Profile image generation failed",isRetry:e.isRetry})});return}m.current[e.runId]&&m.current[e.runId].forEach(t=>{t({runId:e.runId,status:e.status,image_url:e.imageUrl,imageUrl:e.imageUrl,error:e.error,charId:e.charId,isRetry:e.isRetry})}),D(e)}}),e.on("error",e=>{console.log({err:e})})}return()=>{n.current&&(n.current.disconnect(),n.current=null)}},[]);let S={socket:n.current,isConnected:l,registerImageGenerationCallback:k,unregisterImageGenerationCallback:(e,t)=>{m.current[e]&&(m.current[e]=m.current[e].filter(e=>e!==t),0===m.current[e].length&&delete m.current[e])},addPendingGeneration:y,removePendingGeneration:w,trackProfileImageGeneration:(e,t)=>{e&&n.current&&(n.current.emit("new-profile-image-generation",e),y(e),t&&k(e,t))},trackExistingProfileImageGeneration:(e,t)=>{e&&n.current&&(n.current.emit("reconnect-profile-image-generation",{runId:e}),y(e),t&&k(e,t))},trackImageRegeneration:(e,t)=>{var r,o;if(console.log("\uD83D\uDD04 trackImageRegeneration called with runId: ".concat(e)),console.log("\uD83D\uDD04 Socket connected: ".concat(null===(r=n.current)||void 0===r?void 0:r.connected)),console.log("\uD83D\uDD04 Socket ID: ".concat(null===(o=n.current)||void 0===o?void 0:o.id)),!e||!n.current){console.log("❌ trackImageRegeneration early return - runId: ".concat(e,", socket: ").concat(!!n.current));return}console.log("\uD83D\uDCE4 Emitting 'new-image-generation' event with runId: ".concat(e)),n.current.emit("new-image-generation",e),console.log("\uD83D\uDCDD Adding runId to pending generations: ".concat(e)),y(e),t?(console.log("\uD83D\uDCCB Registering callback for runId: ".concat(e)),k(e,t)):console.log("\uD83D\uDCCB No callback provided for runId: ".concat(e)),console.log("✅ trackImageRegeneration completed for runId: ".concat(e))},trackChatImageRegeneration:(e,t)=>{e&&n.current&&(n.current.emit("new-chat-image-generation",e),console.log("trackChatImageRegeneration",e),console.log("socketRef.current",n.current),y(e),t&&k(e,t))},trackLiveImageGeneration:(e,t)=>{e&&n.current&&(n.current.emit("new-char-image-generation",e),y(e),t&&k(e,t))},trackNewChatImageGeneration:(e,t)=>{e&&n.current&&(n.current.emit("new-chat-image-generation",e),y(e),t&&k(e,t))},trackNewCharImageGeneration:(e,t)=>{e&&n.current&&(n.current.emit("new-char-image-generation",e),y(e),t&&k(e,t))}};return(0,r.jsx)(d.Provider,{value:S,children:t})},f=e=>{let{socket:t}=g();(0,o.useEffect)(()=>{let n;if(!e||!e._id||!t)return;let r=((n=localStorage.getItem("sessionId"))||(n=(0,l.Z)(),localStorage.setItem("sessionId",n)),n);if(r){let n=()=>{null==t||t.emit("register-session",{userId:e._id,sessionId:r}),console.log("Socket Connected: "+(null==t?void 0:t.id))};return t.connected?n():t.on("connect",n),()=>{null==t||t.off("connect",n)}}},[e,t])}},20708:function(e,t,n){n.d(t,{l:function(){return i}});var r=n(99441),o=n(79861),a=n(63894);let i=()=>{let{data:e}=(0,a.T)({}),t=(0,r.useRecoilValue)(o.G);return n=>{let{takeCharIdFrom:r,customCharId:o}=n,a="";return a=o||("editCharIdAtom"===r?t:localStorage.getItem("sd_active_char_id")||""),null==e?void 0:e.msg.find(e=>e._id===a)}}},45018:function(e,t,n){n.d(t,{v:function(){return i}});var r=n(22351),o=n(76351),a=n(14738);let i=e=>{let{lastCharId:t}=e,{data:n}=(0,a.j)(),i=null==n?void 0:n._id,l=async()=>{try{let e=(await r.j.post("/character/get_characters",{last_char_id:t||"",is_created:!0})).data;if(e)return localStorage.getItem("sd_active_char_id")||localStorage.setItem("sd_active_char_id",e.msg[0]._id),e;return null}catch(e){return null}};return(0,o.useQuery)({queryKey:["my-chars",i],queryFn:l,enabled:!!i,refetchOnWindowFocus:!1,staleTime:1/0,gcTime:1/0})}},63894:function(e,t,n){n.d(t,{T:function(){return i}});var r=n(22351),o=n(76351),a=n(14738);let i=e=>{let{lastCharId:t}=e,{data:n}=(0,a.j)(),i=null==n?void 0:n._id,l=async()=>{try{let e=await r.j.post("/character/user_characters",{last_char_id:t||""});console.log("Characters Fetched",i);let n=e.data;if(n)return n;return null}catch(e){return null}};return(0,o.useQuery)({queryKey:["user-chars",i],queryFn:l,enabled:!!i,refetchOnWindowFocus:!1,staleTime:1/0,gcTime:1/0})}},97991:function(e,t,n){n.d(t,{e:function(){return a}});var r=n(76351),o=n(22351);let a=()=>{let e=async()=>(await o.j.get("/promotional_data/")).data;return(0,r.useQuery)({queryKey:["promotional-data"],queryFn:e,refetchOnWindowFocus:!1,staleTime:1/0})}},14738:function(e,t,n){n.d(t,{j:function(){return i}});var r=n(22351),o=n(76351),a=n(88726);let i=()=>{let e=localStorage.getItem("access_token")||localStorage.getItem("visitor_token"),t=async()=>{try{if(!e)return;return(await r.j.get("/user/")).data}catch(e){e.response&&"TokenExpiredError"==e.response.data.msg?(localStorage.getItem("access_token")&&a.toast.error("Please login again"),localStorage.removeItem("access_token"),localStorage.removeItem("visitor_token"),window.location.href="/"):console.error(e)}};return(0,o.useQuery)({queryKey:["user",e],queryFn:t,enabled:!!e,refetchOnWindowFocus:!1,staleTime:6048e5})}},34084:function(e,t,n){n.d(t,{AL:function(){return a},FG:function(){return i},b7:function(){return c},sE:function(){return l},zK:function(){return o}});var r=n(99441);let o=(0,r.atom)({key:"callStartedAtom",default:!1}),a=(0,r.atom)({key:"callEndedAtom",default:!1}),i=(0,r.atom)({key:"unlockMicrophoneAtom",default:!1}),l=(0,r.atom)({key:"howlerPlayingAtom",default:!1}),c=(0,r.atom)({key:"callInfoAtom",default:{convId:"",charId:"",msgHistory:[]}})},79861:function(e,t,n){n.d(t,{G:function(){return r}});let r=(0,n(99441).atom)({key:"editCharIdAtom",default:""})},54628:function(e,t,n){n.d(t,{i:function(){return r}});let r=(0,n(99441).atom)({key:"preventRefreshAtom",default:!1})},56688:function(e,t,n){n.d(t,{FQ:function(){return a},SI:function(){return u},VZ:function(){return l},hn:function(){return i},tJ:function(){return c},w8:function(){return o}});var r=n(99441);let o=(0,r.atom)({key:"timedoutConvIdAtom",default:""}),a=(0,r.atom)({key:"failedImgIdAtom",default:""}),i=(0,r.atom)({key:"resTimedoutAtom",default:""}),l=(0,r.atom)({key:"lastPromptAtom",default:""}),c=(0,r.atom)({key:"numberOfFailedImgsAtom",default:0}),u=(0,r.atom)({key:"arrayOfTimedoutImgIdsAtom",default:[]})},85499:function(e,t,n){n.d(t,{$K:function(){return I},$L:function(){return C},$d:function(){return u},CP:function(){return D},Dz:function(){return g},It:function(){return f},LM:function(){return y},Md:function(){return v},NI:function(){return h},Oc:function(){return i},PC:function(){return s},Qg:function(){return c},Yu:function(){return m},_A:function(){return d},cG:function(){return p},d8:function(){return a},dG:function(){return l},kb:function(){return k},nA:function(){return x},s4:function(){return w},wi:function(){return o},wl:function(){return S}});var r=n(99441);let o=(0,r.atom)({key:"showCharProfileSidebarOnChatPageMobile",default:!1}),a=(0,r.atom)({key:"showCharProfileSidebarOnChatPage",default:!0}),i=(0,r.atom)({key:"showPromptSidebarOnGeneratorPageMobile",default:!0}),l=(0,r.atom)({key:"showMobMenuAtom",default:!1});(0,r.atom)({key:"showPaymentModalAtom",default:!1});let c=(0,r.atom)({key:"showUpgradeToPremiumDialogAtom",default:{open:!1,text:"",image:"",type:"subscription"}}),u=(0,r.atom)({key:"phoneCallModalStateAtom",default:{open:!1,receivingCall:!1,text:"",image:"",userCalling:!1}}),s=(0,r.atom)({key:"showAuthDialogAtom",default:{open:!1,isSignup:!1,text:"",image:"",gaClass:""}}),d=(0,r.atom)({key:"viewImageAtom",default:{open:!1,id:"",src:"",prompt:"",isLiveImg:!1}}),g=(0,r.atom)({key:"showVerifyEmailDialogAtom",default:!1}),m=(0,r.atom)({key:"showGenderPrefDialogAtom",default:!1}),f=(0,r.atom)({key:"genderPrefAtom",default:""}),C=(0,r.atom)({key:"showSystemNotificationDialogAtom",default:{open:!1,_id:"",sender:"",description:"	",date:"",type:"",img:"",redirectUrl:"",datetime:"",charId:"",convId:"",content:""}}),h=(0,r.atom)({key:"disableChatInputAtom",default:!1}),I=(0,r.atom)({key:"chatInputAtom",default:""});(0,r.atom)({key:"myProfileModalStateAtom",default:!1});let p=(0,r.atom)({key:"thankyouModalStateAtom",default:!1}),v=(0,r.atom)({key:"bannedWordPopupAtom",default:[]});(0,r.atom)({key:"nsfwLivePhotoAtom2",default:!1});let y=(0,r.atom)({key:"showSelectShareCharImgsPopupAtom",default:!1}),w=(0,r.atom)({key:"selectedShareCharImgsAtom",default:[]}),k=(0,r.atom)({key:"showPublishCharPopupAtom",default:!1}),x=(0,r.atom)({key:"showSetCharImagesPopupAtom",default:!1}),D=(0,r.atom)({key:"flaggedIdAtom",default:""}),S=(0,r.atom)({key:"showUpdateBillingModalAtom",default:!1})},66511:function(e,t,n){n.d(t,{loggedIn:function(){return r}});let r=(0,n(99441).atom)({key:"loggedIn",default:!1})},50189:function(e,t,n){n.d(t,{P:function(){return o},X:function(){return a}});var r=n(99441);let o=(0,r.atom)({key:"liveCharId",default:""}),a=(0,r.atom)({key:"enableBanner",default:!1})},22351:function(e,t,n){n.d(t,{E:function(){return f},j:function(){return g}});var r=n(13498),o=n(58421),a=n(86623),i=n(38472),l=n(75735),c=n(39952),u=n(88726),s=n(22351);let d=async()=>{await (0,l.w7)(c.I);{localStorage.removeItem("access_token"),localStorage.removeItem("pref"),localStorage.removeItem("character_id"),localStorage.removeItem("character"),localStorage.removeItem("timezone"),localStorage.removeItem("visitor_token");let{loggedIn:e}=await Promise.all([n.e(691),n.e(6511)]).then(n.bind(n,66511)),{setRecoil:t}=await Promise.all([n.e(691),n.e(2912)]).then(n.bind(n,82912));t(e,!1),u.toast.success("Logged out please log in again"),window.location.href="/",s.E.clear()}},g=i.default.create({baseURL:r.fw});g.interceptors.request.use(e=>{e.headers||(e.headers=new a.uu);let t=localStorage.getItem("access_token"),n=localStorage.getItem("visitor_token"),r=t||n;return"undefined"==r?d():r&&"undefined"!==r&&(e.headers.set("Authorization",r),e.headers.set("User-Type",t?"login_user":"visitor")),e},e=>Promise.reject(e)),g.interceptors.response.use(e=>e,async e=>{var t,n,r,o,a;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&((null===(r=e.response)||void 0===r?void 0:null===(n=r.data)||void 0===n?void 0:n.msg)==="user_not_found"||(null===(a=e.response)||void 0===a?void 0:null===(o=a.data)||void 0===o?void 0:o.msg)==="no_header_error")&&await d(),Promise.reject(e)});let m=async e=>{let{queryKey:t}=e;try{let[e,n]=t;return(await g(e,n)).data}catch(e){if(e instanceof a.d7){var n;console.error("Axios error:",(null===(n=e.response)||void 0===n?void 0:n.data)||e.message)}else console.error("Unexpected error:",e);throw e}},f=new o.S({defaultOptions:{queries:{queryFn:m}}})}}]);