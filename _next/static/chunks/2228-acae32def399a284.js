"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2228],{59152:function(e,t,a){a.d(t,{bi:function(){return w},cK:function(){return p},qj:function(){return v},y1:function(){return S},zB:function(){return y}});var s=a(38472),i=a(13498),n=a(75735),r=a(39952),o=a(88726),l=a(39940),c=a(82912),d=a(85499),u=a(16427),g=a(98280);let m=async e=>{let{email:t,accessToken:a,userId:n}=e,r=(0,c.$l)(u.A),l=(0,c.$l)(d.PC);try{let e=window.location.search,c=new URLSearchParams(e),d=(null==c?void 0:c.get("rl"))||"",u=await s.default.post("".concat(i.fw,"/user/login"),{email:t,referral:d,invite:r,accessToken:a,userId:n});if(1==u.data.code){let e=u.data.token;e&&(localStorage.setItem("access_token",e),o.toast.success("Login Successful"),"Sign Up to See Plans"===l.text&&window.location.replace("/subscription"),window.location.replace("/"))}else if(2==u.data.code)throw Error("User not found");return}catch(e){throw e}},h=async(e,t)=>{let{user:a}=e,n=(0,c.$l)(u.A),r=(0,c.$l)(d.PC);try{let e=(0,l.L)(),c=a.email,d=window.location.search,u=new URLSearchParams(d),g=(null==u?void 0:u.get("rl"))||"",m=a.displayName,h=await s.default.post("".concat(i.fw,"/user/login"),{email:c,referral:g,invite:n,accessToken:a.accessToken,userId:a.uid});if(1==h.data.code){let e=h.data.token;e&&(localStorage.setItem("access_token",e),o.toast.success("Login Successful"),"Sign Up to See Plans"===r.text&&window.location.replace("/subscription"),window.location.replace("/"))}else if(2==h.data.code){let a=(await s.default.post("".concat(i.fw,"/user/signUp"),{email:c,name:m,uid:e,invite:n,referral:g,source:null==t?void 0:t.source,tracking_id:null==t?void 0:t.tracking_id})).data.token;a&&(localStorage.setItem("access_token",a),o.toast.success("Account Created Successfully"),"Sign Up to See Plans"===r.text&&window.location.replace("/subscription"))}return}catch(e){throw e}},p=async e=>{let{email:t,name:a,uid:n,consent:r}=e,l=(0,c.$l)(u.A),g=(0,c.$l)(d.PC);try{var m,h;let e=window.location.search,c=new URLSearchParams(e),d=(null==c?void 0:c.get("rl"))||"",u=(null==c?void 0:c.get("invite"))||"";a&&(null==a?void 0:a.trim())!==""||(a=t.split("@")[0]);let p=await s.default.post("".concat(i.fw,"/user/signUp"),{email:t,name:a,uid:n,consent:r||!1,referral:d,invite:l||u});if((null==p?void 0:null===(m=p.data)||void 0===m?void 0:m.code)==1){let e=p.data.token;e&&(localStorage.setItem("access_token",e),o.toast.success("User Logged In Successfully",{duration:3e3}),"Sign up to See Plans"===g.text&&window.location.replace("/subscription"))}else if((null==p?void 0:null===(h=p.data)||void 0===h?void 0:h.code)==2)throw Error("User with this email already exists, please sign in");return}catch(e){throw e}},f=async e=>{try{return(await (0,n.rh)(r.I,e)).user}catch(e){throw console.error("Error signing in:",e),e}},v=async e=>{try{let t=new n.hJ,a=await f(t);await h({user:a},e),localStorage.removeItem("visitor_token")}catch(e){throw console.error("Error signing in:",e),e}},w=async()=>{try{let e=new n.c4,t=await f(e);await h({user:t}),localStorage.removeItem("visitor_token")}catch(e){throw console.error("Error signing in:",e),e}},x=async(e,t)=>{let{email:a,password:s}=e;try{(await (0,n.Xb)(r.I,a,s)).user&&(o.toast.success("Welcome to Secret Desires 2.0, please reset your password to continue"),t.push("/password/reset"))}catch(e){throw console.error("Error signing in:",e),e}},y=async(e,t)=>{let{email:a,password:o}=e;try{if(!a||!o)throw Error("Email and password are required");let e=await s.default.post("".concat(i.fw,"/user/check_migrated"),{email:a});if(1==e.data.code){await x({email:a,password:"secret_desires@Bus#39!cap2"},t);return}let l=(await (0,n.e5)(r.I,a,o)).user;if(l)return await m({email:a,accessToken:l.accessToken,userId:l.uid}),localStorage.removeItem("visitor_token"),t.push("/"),1;throw Error(" Sign-In failed")}catch(e){throw console.error("Error signing in:",e),e}},S=async(e,t)=>{let{email:a,password:m,name:h,consent:p}=e,f=null;(0,c.$l)(u.A);let v=(0,c.$l)(d.PC);try{if(!a||!m)throw Error("Email and password are required");f=(await (0,n.Xb)(r.I,a,m)).user;let e=(0,l.L)(),t=await s.default.post("".concat(i.fw,"/email/sendVerificationEmail"),{user_id:f.uid,email:a,name:h,consent:p,uid:e});o.toast.success(t.data.message),"Sign up to See Plans"===v.text&&window.location.replace("/subscription"),localStorage.setItem("reg_data",JSON.stringify({email:a,name:h,consent:p,user_id:f.uid}));let u=window.location.pathname.split("/")[1];if("create"==u){let e=(0,c.$l)(g.j_),t=(0,c.$l)(g.Y3),a=(0,c.$l)(g.ho),s=(0,c.$l)(g.C0),i=(0,c.$l)(g.nY);localStorage.setItem("before_route",JSON.stringify(u)),localStorage.setItem("create_data",JSON.stringify({data:e,completed:t,edit:a,customizedFields:s,page_no:i}))}(0,c.setRecoil)(d.Dz,!0)}catch(e){throw console.error("Error signing up:",e),f&&await (0,n.h8)(f),e}}},72228:function(e,t,a){a.d(t,{Z:function(){return E}});var s=a(57437),i=a(16880),n=a(50495),r=a(83102),o=a(66648),l=a(3114),c=a(66511),d=a(99441),u=a(85499),g=a(2265),m=a(13498),h=a(88726),p=a(27485),f=a(38472),v=a(90058),w=a(59152),x=a(16463),y=a(14738),S=a(22351),b=a(86466),k=a(16427),I=a(39940),_=a(82912),j=a(98130),N=a(80223);let C=()=>{let e=(0,x.useRouter)();return{signInWithDiscord:(0,g.useCallback)(async t=>{try{let a=h.toast.loading("Loading...");N.ZP.emit("discordLogin",{socketId:N.ZP.id}),(null==t?void 0:t.source)&&await S.j.post("/marketing",{source:null==t?void 0:t.source,interaction:"user_registered",tracking_id:null==t?void 0:t.tracking_id}),h.toast.dismiss(a),e.push("".concat(m.fw,"/auth/discord"))}catch(e){throw console.error("Error signing in with Discord:",e),e}},[e])}};function E(e){let{text:t}=e;var a=(0,i.Z)();try{let e=(0,l.P)(),{data:a}=(0,y.j)(),[i,E]=(0,d.useRecoilState)(u.PC),[A,P]=(0,g.useState)(""),[D,L]=(0,g.useState)(!1),[U,R]=(0,g.useState)(""),[z,F]=(0,g.useState)(!1),[$,q]=(0,g.useState)(""),[O,T]=(0,g.useState)(!1),[J,K]=(0,g.useState)(""),Q=(0,j.L)(),[W,Z]=(0,d.useRecoilState)(c.loggedIn),V=(0,d.useRecoilValue)(u.Dz),Y={open:!1,isSignup:!1,text:"",image:""},[B,M]=(0,g.useState)(!1),[X,G]=(0,g.useState)(!1),[H,ee]=(0,g.useState)({hasNum:null,minCharacter:null,hasLowerCase:null,hasUpperCase:null,hasSpecialChar:null}),[et,ea]=(0,g.useState)(!1),es=(0,x.useSearchParams)(),ei=(0,d.useSetRecoilState)(k.A),[en,er]=(0,g.useState)(0),{signInWithDiscord:eo}=C();(0,g.useEffect)(()=>{ei(es.get("invite"))},[]),(0,g.useEffect)(()=>{let e=/\d/.test(U),t=/[a-z]/.test(U),a=/[A-Z]/.test(U),s=/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(U),i=U.length>=8;ee({hasNum:e,minCharacter:i,hasLowerCase:t,hasUpperCase:a,hasSpecialChar:s})},[U,H.hasLowerCase,H.hasNum,H.hasSpecialChar,H.hasUpperCase,H.minCharacter]);let el=(0,x.useRouter)(),ec=e=>{if(e.origin===m.i7){if(e.data.loggedIn){let t=e.data.token;if(t&&!W){ed(),localStorage.setItem("access_token",t),localStorage.removeItem("visitor_token"),h.default.success("Login Successful"),S.E.invalidateQueries({queryKey:["user"]}),Z(!0),"Sign Up to See Plans"===i.text?el.push("/subscription"):window.location.href="/";return}}else{var t;let a=e.data.error;h.default.error((null==a?void 0:null===(t=a.response)||void 0===t?void 0:t.data.msg)||(null==a?void 0:a.message)||"Failed to Login")}ed()}},ed=async e=>{!e&&i.isSignup&&L(!0),P(""),R(""),q(""),E(Y),S.E.invalidateQueries({queryKey:["user-chars",null==a?void 0:a._id]}),await S.E.invalidateQueries({queryKey:["my-chars",null==a?void 0:a._id]});let t=localStorage.getItem("campaign")||"";if(t&&!e&&i.isSignup){let e=Math.floor(new Date().getTime()/1e3);await S.j.post("/user/ga_trigger",{id:e,campaign_name:t})}};(0,g.useEffect)(()=>(window.addEventListener("message",ec,!1),()=>{window.removeEventListener("message",ec)}),[]);let eu=async()=>{try{let e={source:(null==Q?void 0:Q.source)||localStorage.getItem("marketing_source")||"direct",tracking_id:(null==Q?void 0:Q.tracking_id)||localStorage.getItem("tracking_id")||""};await (0,w.qj)(e),Z(!0)}catch(s){var e,t;let a=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==s?void 0:s.message)||"Something went wrong";(null==a?void 0:a.startsWith("Firebase:"))&&(a=(a=a.split(" ")[2]).substring(1,a.length-2)),h.default.error(a)}ed()},eg=async()=>{try{let e={source:(null==Q?void 0:Q.source)||localStorage.getItem("marketing_source")||"direct",tracking_id:(null==Q?void 0:Q.tracking_id)||localStorage.getItem("tracking_id")||""};await eo(e)}catch(s){var e,t;let a=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==s?void 0:s.message)||"Something went wrong";(null==a?void 0:a.startsWith("Firebase:"))&&(a=(a=a.split(" ")[2]).substring(1,a.length-2)),h.default.error(a),ed()}},em=async()=>{try{await (0,w.bi)(),Z(!0)}catch(s){var e,t;let a=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==s?void 0:s.message)||"Something went wrong";(null==a?void 0:a.startsWith("Firebase:"))&&(a=(a=a.split(" ")[2]).substring(1,a.length-2)),h.default.error(a)}ed()},eh=async()=>{F(!0);try{await (0,w.zB)({email:A,password:U},el)&&Z(!0)}catch(s){var e,t;let a=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==s?void 0:s.message)||"Something went wrong";(null==a?void 0:a.startsWith("Firebase:"))&&(a=(a=a.split(" ")[2]).substring(1,a.length-2)),(0,v.dd)(a)}ed("login"),F(!1)},ep=async()=>{F(!0);try{if(!H.hasNum||!H.minCharacter||!H.hasLowerCase||!H.hasUpperCase||!H.hasSpecialChar){h.default.error("Password does not meet requirements");return}await (0,w.y1)({email:A,password:U,name:$,consent:O},el)}catch(s){var e,t;let a=(null==s?void 0:null===(t=s.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.msg)||(null==s?void 0:s.message)||"Something went wrong";if(null==a?void 0:a.startsWith("Firebase:")){if(a=(a=a.split(" ")[2]).substring(1,a.length-2),s instanceof Error&&s.message.includes("auth/email-already-in-use")){let e=localStorage.getItem("access_token")||localStorage.getItem("visitor_token"),t=await f.default.post("".concat(m.fw,"/user/complete-signup"),{email:A},{params:{email:A},headers:{Authorization:e}});"deleted"===t.data?await (0,w.y1)({email:A,password:U,name:$,consent:O},el):(0,v.dd)(a)}else(0,v.dd)(a)}else(0,v.dd)(a)}F(!1)},ef=()=>{er(59);let e=setInterval(()=>{er(t=>t<=1?(clearInterval(e),0):t-1)},1e3)},ev=async()=>{try{let e=localStorage.getItem("reg_data")||"{}",{email:t,name:a,consent:s,user_id:i}=JSON.parse(e),n=(0,I.L)(),r=await f.default.post("".concat(m.fw,"/email/sendVerificationEmail"),{user_id:i,email:t,name:a,consent:s,uid:n});h.default.success(r.data.message),ef()}catch(a){var e,t;h.default.error((null==a?void 0:null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Something went wrong")}};async function N(e,t,a){let s=(0,I.L)();try{await (0,w.cK)({email:e,name:t,uid:s,consent:a}),(0,_.setRecoil)(c.loggedIn,!0),localStorage.removeItem("visitor_token"),localStorage.removeItem("reg_data"),(0,_.setRecoil)(u.PC,{open:!1,isSignup:!1,text:"",image:""})}catch(e){var i,n;h.default.error((null==e?void 0:null===(n=e.response)||void 0===n?void 0:null===(i=n.data)||void 0===i?void 0:i.msg)||(null==e?void 0:e.message)||"Something went wrong",{duration:7e3})}}let ew=async()=>{try{let e=await f.default.post("".concat(m.fw,"/email/verifyEmail"),{otp:J,source:null==Q?void 0:Q.source,tracking_id:null==Q?void 0:Q.tracking_id});e&&(h.default.success("Successfully Verified"),N(e.data.email,e.data.name,e.data.consent))}catch(a){var e,t;h.default.error((null==a?void 0:null===(t=a.response)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.message)||"Something went wrong")}};return(0,s.jsxs)(s.Fragment,{children:[z&&(0,s.jsx)("div",{className:"absolute h-full w-full flex justify-center items-center z-51",children:(0,s.jsx)(p.gy,{color:"#d62a5e"})}),(0,s.jsxs)("div",{className:"flex flex-wrap w-full",id:D?"ga-registration-done":"",children:[(0,s.jsx)("div",{className:"flex-1 md:max-w-[40%]",children:(0,s.jsx)(o.default,{src:i.image||((null==a?void 0:a.gender_preference)==="Male"?"/images/male-signup.png":(null==a?void 0:a.gender_preference)==="Female"?"/images/female-signup.png":"/images/Kazuki_signup.png"),alt:"Your Character",width:500,height:500,className:"object-cover object-top md:object-center min-w-52 h-36 w-full md:h-full"})}),V?(0,s.jsxs)("div",{className:"flex-1 p-4 md:p-8 flex flex-col justify-start md:min-h-[200px] lg:min-h-[500px] max-w-80 md:max-w-[60%]",children:[(0,s.jsx)("h2",{className:"mb-2 md:mb-4 text-lg md:text-2xl font-bold",children:"Confirm your email address"}),(0,s.jsx)("p",{className:"text-sm text-white",children:"Thank you for signing up! Please check your email for a verification code and enter it below."}),(0,s.jsx)("br",{}),(0,s.jsxs)("div",{className:"flex flex-row gap-x-2 justify-center items-center",children:[(0,s.jsx)(r.I,{placeholder:"Enter Code",width:200,onChange:e=>K(e.target.value)}),(0,s.jsx)(n.z,{onClick:ev,disabled:en>0,variant:"link",className:"text-primary font-bold ".concat(en>0?"opacity-50 cursor-not-allowed":""),children:en>0?"Resend (".concat(en,"s)"):"Resend"})]}),(0,s.jsx)(n.z,{"aria-label":"verify",variant:"default",className:"p-3 my-5 w-[80px]",onClick:ew,children:"Verify"})]}):(0,s.jsxs)("div",{className:"flex-1 p-4 md:p-8 flex flex-col max-w-80 md:max-w-[60%]",children:[(0,s.jsx)("h2",{className:"mb-4 md:mb-6 text-lg md:text-2xl font-bold text-center",children:t||i.text||"Welcome"+(i.isSignup?"":" Back!")}),(0,s.jsxs)("div",{className:"space-y-2 md:space-y-4",children:[i.isSignup&&(0,s.jsx)(r.I,{value:$,onChange:e=>q(e.target.value),placeholder:"Your nickname (optional)",className:"bg-background w-full",autoFocus:!1}),(0,s.jsx)(r.I,{value:A,onChange:e=>P(e.target.value),placeholder:"Email Address",className:"bg-background w-full ",autoFocus:!1,autoComplete:"off"}),(0,s.jsxs)("div",{className:"relative ".concat(B?"cursor-not-allowed opacity-50":""),children:[(0,s.jsx)(r.I,{type:et?"text":"password",placeholder:"Password",value:U,className:"w-full px-4 py-2  text-white sm:text-sm text-xs ".concat(B?"cursor-not-allowed pointer-events-none ":""),onClick:()=>G(!0),onChange:e=>{R(e.target.value)},autoFocus:!1}),(0,s.jsx)("button",{"aria-label":"Toggle Password Visibility",type:"button",onClick:()=>{ea(!et)},className:"absolute top-1/2 right-3 transform -translate-y-1/2 sm:scale-[.9] scale-[.7]  ".concat(B?"cursor-not-allowed pointer-events-none ":""),children:(0,s.jsx)(o.default,{src:"/icons/".concat(et?"eye-primary":"eye-slash-primary",".svg"),alt:"Password not visible Icon",width:"24",height:"24"})})]}),i.isSignup&&(0,s.jsxs)("div",{className:"".concat(X?"max-h-[900px] opacity-100":"max-h-0 opacity-0 hidden"," sm:text-xs text-[10px] sm:leading-4 leading-[13px] pl-2  transition-all duration-300 ease-in-out overflow-hiddenS"),children:[(0,s.jsx)("p",{className:"".concat(H.minCharacter?"text-green-700":"text-red-700"),children:"Minimum of 8 characters"}),(0,s.jsx)("p",{className:"".concat(H.hasUpperCase?"text-green-700":"text-red-700"),children:"Atleast one upper case character"}),(0,s.jsx)("p",{className:"".concat(H.hasLowerCase?"text-green-700":"text-red-700"),children:"Atleast one lower case character"}),(0,s.jsx)("p",{className:"".concat(H.hasNum?"text-green-700":"text-red-700"),children:"Atleast one number"}),(0,s.jsx)("p",{className:"".concat(H.hasSpecialChar?"text-green-700":"text-red-700"),children:"Atleast one special character"})]}),i.isSignup&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(b.X,{id:"email_pref",className:"sm:scale-[1] scale-[.65]",checked:O,onCheckedChange:()=>{T(e=>!e)}}),(0,s.jsx)("label",{htmlFor:"terms",className:"sm:text-base text-[11px] px-2 sm:tracking-wide font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Opt-in to marketing emails"})]}),(0,s.jsx)(n.z,{"aria-label":i.isSignup?"Create Account":"Log in",onClick:i.isSignup?ep:eh,className:"w-full font-bold",disabled:z,children:i.isSignup?"Create Account":"Log in"}),!i.isSignup&&(0,s.jsx)("button",{"aria-label":"Forgot Password",onClick:()=>{E(Y),el.replace("/password/reset")},className:"text-gray-600 hover:text-primary text-sm",children:"Forgot password?"})]}),(0,s.jsxs)("div",{className:"flex items-center my-4",children:[(0,s.jsx)("div",{className:"flex-1 h-px bg-gray-700"}),(0,s.jsxs)("span",{className:"px-4 text-sm text-gray-500",children:["or sign ",i.isSignup?"up":"in"," with"]}),(0,s.jsx)("div",{className:"flex-1 h-px bg-gray-700"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(n.z,{"aria-label":"Sign in with Google",onClick:eu,variant:"outline",className:"flex-1 bg-white text-black hover:bg-white/90 hover:text-black",children:[(0,s.jsx)("span",{className:"md:mr-2 h-6 w-6",children:(0,s.jsx)(o.default,{src:"/icons/google.png",alt:"Google icon",width:24,height:24})}),(0,s.jsx)("span",{className:"hidden md:block font-bold",children:"Google"})]}),(0,s.jsxs)(n.z,{"aria-label":"Sign in with Discord",onClick:eg,variant:"outline",className:"flex-1 bg-[#5865F2] hover:bg-[#5865F2]/80",children:[(0,s.jsx)("span",{className:"md:mr-2 scale-125",children:e.discord}),(0,s.jsx)("span",{className:"hidden md:block font-bold",children:"Discord"})]}),(0,s.jsxs)(n.z,{"aria-label":"Sign in with Twitter",onClick:em,variant:"outline",className:"flex-1 bg-black hover:bg-black/70",children:[(0,s.jsx)("span",{className:"md:mr-2",children:e.x}),(0,s.jsx)("span",{className:"hidden md:block font-bold",children:"Twitter"})]})]}),(0,s.jsxs)("p",{className:"mt-4 text-sm text-center text-gray-500",children:[(i.isSignup?"Already have an account?":"Don't have an account yet?")+" ",(0,s.jsx)("button",{"aria-label":"Toggle to Sign in or Sign up form",onClick:()=>E(e=>({...e,isSignup:!e.isSignup})),className:"text-primary",children:"Sign "+(i.isSignup?"in":"up")})]})]})]})]})}finally{a.f()}}},86466:function(e,t,a){a.d(t,{X:function(){return l}});var s=a(57437),i=a(2265),n=a(45310),r=a(22468),o=a(13498);let l=i.forwardRef((e,t)=>{let{className:a,...i}=e;return(0,s.jsx)(n.fC,{ref:t,className:(0,o.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...i,children:(0,s.jsx)(n.z$,{className:(0,o.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(r.Z,{className:"h-4 w-4"})})})});l.displayName=n.fC.displayName},80223:function(e,t,a){var s=a(34999),i=a(13498);a(2265);var n=a(22351);console.log("Connecting: "+i.i7);let r=(0,s.io)(i.i7,{reconnection:!0,reconnectionAttempts:20,reconnectionDelay:1e3,reconnectionDelayMax:5e3,timeout:2e4,transports:["websocket"],withCredentials:!0});r.on("reconnect",e=>{console.log("\uD83D\uDD01 Reconnected after ".concat(e," attempts"))}),r.on("connect",()=>{console.log("✅ Connected with id: ".concat(r.id))});let o={},l={};function c(e){{let t=JSON.parse(localStorage.getItem("pendingRunIds")||"[]").filter(t=>t!==e);localStorage.setItem("pendingRunIds",JSON.stringify(t))}}function d(e){"success"===e.status&&e.imageUrl&&(e.charId&&e.msgId&&n.E.setQueryData(["msg-history",{charId:e.charId}],t=>{if(!t)return t;let a=t.pages.map(t=>({...t,msg:t.msg.map(t=>t._id===e.msgId?{...t,image:e.imageUrl,status:e.status}:t)}));return{...t,pages:a}}),e.charId&&e.imageUrl&&e.msgId&&n.E.setQueryData(["gen-images",e.charId],t=>{if(!t)return t;let a={_id:e.msgId,created_date:new Date().toISOString(),imageUrl:e.imageUrl,imageType:"message",image_prompt:e.prompt||"",live_photo:e.live_photo||!1};return t.msg&&t.msg.some(e=>e._id===a._id)?t:{...t,msg:t.msg&&t.msg.length>0?[t.msg[0],a,...t.msg.slice(1)]:[a]}}),n.E.removeQueries({queryKey:["get-image-reply"]}),e.charId&&(n.E.invalidateQueries({queryKey:["gen-images",e.charId]}),n.E.invalidateQueries({queryKey:["msg-history",{charId:e.charId}]})))}r.on("connect",()=>{console.log("Socket connected, setting up persistent listeners"),JSON.parse(localStorage.getItem("pendingRunIds")||"[]").forEach(e=>{r.emit("reconnect-image-generation",{runId:e})}),r.off("image-generated2"),r.off("image-generated1"),r.off("profile-image-generated"),r.on("image-generated2",e=>{e.runId&&(JSON.parse(localStorage.getItem("pendingRunIds")||"[]").includes(e.runId)&&(e.charId?n.E.invalidateQueries({queryKey:["gen-images",e.charId]}):n.E.invalidateQueries({queryKey:["gen-images"]}),"success"===e.status&&c(e.runId)),d(e))}),r.on("image-generated1",e=>{e.runId&&(o[e.runId]={status:"success"===e.status?"success":"failed",imageUrl:e.image_url,error:e.error},JSON.parse(localStorage.getItem("pendingRunIds")||"[]").includes(e.runId)&&"success"===e.status&&(d(e),c(e.runId)),l[e.runId]&&l[e.runId].forEach(t=>{t(e)}))}),r.on("profile-image-generated",e=>{console.log("Received profile image generation update:",e),e.runId&&(JSON.parse(localStorage.getItem("pendingRunIds")||"[]").includes(e.runId)&&"success"===e.status&&(c(e.runId),n.E.setQueryData(["my-chars"],t=>t?t.map(t=>t._id===e.charId?{...t,initial_image:e.imageUrl}:t):t)),d(e))})}),r.on("error",e=>{console.log({err:e})}),t.ZP=r},16427:function(e,t,a){a.d(t,{A:function(){return s}});let s=(0,a(99441).atom)({key:"maxTrialIdAtom",default:void 0})},98280:function(e,t,a){a.d(t,{C0:function(){return u},Hf:function(){return g},Y3:function(){return m},gn:function(){return r},hE:function(){return o},ho:function(){return h},j_:function(){return c},jo:function(){return n},l0:function(){return d},mg:function(){return l},n8:function(){return i},nY:function(){return p}});var s=a(99441);let i=(0,s.atom)({key:"looksDataAtom",default:{}}),n=(0,s.atom)({key:"personalityDataAtom",default:{}}),r=(0,s.atom)({key:"lifestyleDataAtom",default:{}}),o=(0,s.atom)({key:"reviewIconsDataAtom",default:{}}),l={style:"",age:null,personality:"",looks:{face_swap_consent:!1,face_swap:""},gender:"",work:[],hobbies:[],relationships:"",voice:"",voice_embedding:"",kinks:"",voice_consent:!1,voice_url:"",first_name:"",last_name:"",about_me:""},c=(0,s.atom)({key:"CharacterDataAtom",default:l}),d={ethnicities:!1,eye_colors:!1,hair_styles:!1,hair_colors:!1,body_types:!1,breast_sizes:!1,butt_sizes:!1,personality:!1,relationships:!1,work:!1,hobbies:!1,kinks:!1},u=(0,s.atom)({key:"customizedFieldsAtom",default:d}),g={1:!1,2:!1,3:!1,4:!1,5:!1,6:!1,7:!1,8:!1},m=(0,s.atom)({key:"completedAtom",default:g}),h=(0,s.atom)({key:"editAtom",default:g}),p=(0,s.atom)({key:"pageAtom",default:1})}}]);