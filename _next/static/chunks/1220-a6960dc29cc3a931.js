"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1220],{90976:function(e,r,n){n.d(r,{B:function(){return i}});var t=n(2265),o=n(98324),a=n(1584),u=n(71538),l=n(57437);function i(e){let r=e+"CollectionProvider",[n,i]=(0,o.b)(r),[c,d]=n(r,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:r,children:n}=e,o=t.useRef(null),a=t.useRef(new Map).current;return(0,l.jsx)(c,{scope:r,itemMap:a,collectionRef:o,children:n})};s.displayName=r;let f=e+"CollectionSlot",p=t.forwardRef((e,r)=>{let{scope:n,children:t}=e,o=d(f,n),i=(0,a.e)(r,o.collectionRef);return(0,l.jsx)(u.g7,{ref:i,children:t})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",g=t.forwardRef((e,r)=>{let{scope:n,children:o,...i}=e,c=t.useRef(null),s=(0,a.e)(r,c),f=d(v,n);return t.useEffect(()=>(f.itemMap.set(c,{ref:c,...i}),()=>void f.itemMap.delete(c))),(0,l.jsx)(u.g7,{[m]:"",ref:s,children:o})});return g.displayName=v,[{Provider:s,Slot:p,ItemSlot:g},function(r){let n=d(e+"CollectionConsumer",r);return t.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let r=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>r.indexOf(e.ref.current)-r.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},i]}},21220:function(e,r,n){n.d(r,{oC:function(){return rs},VY:function(){return rl},h_:function(){return eJ},bO:function(){return e9},AW:function(){return e7},_x:function(){return e4},qB:function(){return e6},$F:function(){return e0},ZA:function(){return ri},ck:function(){return rd},wU:function(){return rv},__:function(){return rc},Uv:function(){return ru},Ee:function(){return rf},Rk:function(){return rp},fC:function(){return ro},Z0:function(){return rm},Tr:function(){return rg},tu:function(){return rw},fF:function(){return rh},xz:function(){return ra}});var t=n(2265),o=n(78149),a=n(1584),u=n(98324),l=n(91715),i=n(25171),c=n(90976),d=n(87513),s=n(53938),f=n(20589),p=n(80467),v=n(53201),m=n(25510),g=n(56935),h=n(31383),w=n(75137),x=n(57437),M="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[C,R,j]=(0,c.B)(b),[D,_]=(0,u.b)(b,[j]),[k,I]=D(b),E=t.forwardRef((e,r)=>(0,x.jsx)(C.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(C.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,x.jsx)(P,{...e,ref:r})})}));E.displayName=b;var P=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:u,loop:c=!1,dir:s,currentTabStopId:f,defaultCurrentTabStopId:p,onCurrentTabStopIdChange:v,onEntryFocus:m,preventScrollOnEntryFocus:g=!1,...h}=e,b=t.useRef(null),C=(0,a.e)(r,b),j=(0,d.gm)(s),[D=null,_]=(0,l.T)({prop:f,defaultProp:p,onChange:v}),[I,E]=t.useState(!1),P=(0,w.W)(m),T=R(n),S=t.useRef(!1),[F,A]=t.useState(0);return t.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(M,P),()=>e.removeEventListener(M,P)},[P]),(0,x.jsx)(k,{scope:n,orientation:u,dir:j,loop:c,currentTabStopId:D,onItemFocus:t.useCallback(e=>_(e),[_]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>A(e=>e-1),[]),children:(0,x.jsx)(i.WV.div,{tabIndex:I||0===F?-1:0,"data-orientation":u,...h,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let r=!S.current;if(e.target===e.currentTarget&&r&&!I){let r=new CustomEvent(M,y);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=T().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),g)}}S.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>E(!1))})})}),T="RovingFocusGroupItem",S=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:l,...c}=e,d=(0,v.M)(),s=l||d,f=I(T,n),p=f.currentTabStopId===s,m=R(n),{onFocusableItemAdd:g,onFocusableItemRemove:h}=f;return t.useEffect(()=>{if(a)return g(),()=>h()},[a,g,h]),(0,x.jsx)(C.ItemSlot,{scope:n,id:s,focusable:a,active:u,children:(0,x.jsx)(i.WV.span,{tabIndex:p?0:-1,"data-orientation":f.orientation,...c,ref:r,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?f.onItemFocus(s):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>f.onItemFocus(s)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return F[o]}(e,f.orientation,f.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=m().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)o.reverse();else if("prev"===r||"next"===r){var n,t;"prev"===r&&o.reverse();let a=o.indexOf(e.currentTarget);o=f.loop?(n=o,t=a+1,n.map((e,r)=>n[(t+r)%n.length])):o.slice(a+1)}setTimeout(()=>N(o))}})})})});S.displayName=T;var F={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var A=n(71538),O=n(78369),L=n(17590),K=["Enter"," "],V=["ArrowUp","PageDown","End"],W=["ArrowDown","PageUp","Home",...V],G={ltr:[...K,"ArrowRight"],rtl:[...K,"ArrowLeft"]},B={ltr:["ArrowLeft"],rtl:["ArrowRight"]},U="Menu",[z,X,Y]=(0,c.B)(U),[Z,H]=(0,u.b)(U,[Y,m.D7,_]),q=(0,m.D7)(),$=_(),[J,Q]=Z(U),[ee,er]=Z(U),en=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=q(r),[c,s]=t.useState(null),f=t.useRef(!1),p=(0,w.W)(u),v=(0,d.gm)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,x.jsx)(m.fC,{...i,children:(0,x.jsx)(J,{scope:r,open:n,onOpenChange:p,content:c,onContentChange:s,children:(0,x.jsx)(ee,{scope:r,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:l,children:o})})})};en.displayName=U;var et=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=q(n);return(0,x.jsx)(m.ee,{...o,...t,ref:r})});et.displayName="MenuAnchor";var eo="MenuPortal",[ea,eu]=Z(eo,{forceMount:void 0}),el=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=Q(eo,r);return(0,x.jsx)(ea,{scope:r,forceMount:n,children:(0,x.jsx)(h.z,{present:n||a.open,children:(0,x.jsx)(g.h,{asChild:!0,container:o,children:t})})})};el.displayName=eo;var ei="MenuContent",[ec,ed]=Z(ei),es=t.forwardRef((e,r)=>{let n=eu(ei,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=Q(ei,e.__scopeMenu),u=er(ei,e.__scopeMenu);return(0,x.jsx)(z.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(h.z,{present:t||a.open,children:(0,x.jsx)(z.Slot,{scope:e.__scopeMenu,children:u.modal?(0,x.jsx)(ef,{...o,ref:r}):(0,x.jsx)(ep,{...o,ref:r})})})})}),ef=t.forwardRef((e,r)=>{let n=Q(ei,e.__scopeMenu),u=t.useRef(null),l=(0,a.e)(r,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,O.Ry)(e)},[]),(0,x.jsx)(ev,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),ep=t.forwardRef((e,r)=>{let n=Q(ei,e.__scopeMenu);return(0,x.jsx)(ev,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),ev=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:M,onDismiss:y,disableOutsideScroll:b,...C}=e,R=Q(ei,n),j=er(ei,n),D=q(n),_=$(n),k=X(n),[I,P]=t.useState(null),T=t.useRef(null),S=(0,a.e)(r,T,R.onContentChange),F=t.useRef(0),N=t.useRef(""),O=t.useRef(0),K=t.useRef(null),G=t.useRef("right"),B=t.useRef(0),U=b?L.Z:t.Fragment,z=b?{as:A.g7,allowPinchZoom:!0}:void 0,Y=e=>{var r,n;let t=N.current+e,o=k().filter(e=>!e.disabled),a=document.activeElement,u=null===(r=o.find(e=>e.ref.current===a))||void 0===r?void 0:r.textValue,l=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(t=Math.max(n?e.indexOf(n):-1,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,u),i=null===(n=o.find(e=>e.textValue===l))||void 0===n?void 0:n.ref.current;!function e(r){N.current=r,window.clearTimeout(F.current),""!==r&&(F.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(F.current),[]),(0,f.EW)();let Z=t.useCallback(e=>{var r,n,t;return G.current===(null===(r=K.current)||void 0===r?void 0:r.side)&&!!(t=null===(n=K.current)||void 0===n?void 0:n.area)&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let u=r[e].x,l=r[e].y,i=r[a].x,c=r[a].y;l>t!=c>t&&n<(i-u)*(t-l)/(c-l)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,x.jsx)(ec,{scope:n,searchRef:N,onItemEnter:t.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),onItemLeave:t.useCallback(e=>{var r;Z(e)||(null===(r=T.current)||void 0===r||r.focus(),P(null))},[Z]),onTriggerLeave:t.useCallback(e=>{Z(e)&&e.preventDefault()},[Z]),pointerGraceTimerRef:O,onPointerGraceIntentChange:t.useCallback(e=>{K.current=e},[]),children:(0,x.jsx)(U,{...z,children:(0,x.jsx)(p.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(i,e=>{var r;e.preventDefault(),null===(r=T.current)||void 0===r||r.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,x.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:M,onDismiss:y,children:(0,x.jsx)(E,{asChild:!0,..._,dir:j.dir,orientation:"vertical",loop:u,currentTabStopId:I,onCurrentTabStopIdChange:P,onEntryFocus:(0,o.M)(v,e=>{j.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eG(R.open),"data-radix-menu-content":"",dir:j.dir,...D,...C,ref:S,style:{outline:"none",...C.style},onKeyDown:(0,o.M)(C.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&Y(e.key));let o=T.current;if(e.target!==o||!W.includes(e.key))return;e.preventDefault();let a=k().filter(e=>!e.disabled).map(e=>e.ref.current);V.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(F.current),N.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ez(e=>{let r=e.target,n=B.current!==e.clientX;if(e.currentTarget.contains(r)&&n){let r=e.clientX>B.current?"right":"left";G.current=r,B.current=e.clientX}}))})})})})})})});es.displayName=ei;var em=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.WV.div,{role:"group",...t,ref:r})});em.displayName="MenuGroup";var eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.WV.div,{...t,ref:r})});eg.displayName="MenuLabel";var eh="MenuItem",ew="menu.itemSelect",ex=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:u,...l}=e,c=t.useRef(null),d=er(eh,e.__scopeMenu),s=ed(eh,e.__scopeMenu),f=(0,a.e)(r,c),p=t.useRef(!1);return(0,x.jsx)(eM,{...l,ref:f,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!n&&e){let r=new CustomEvent(ew,{bubbles:!0,cancelable:!0});e.addEventListener(ew,e=>null==u?void 0:u(e),{once:!0}),(0,i.jH)(e,r),r.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,r),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var r;p.current||null===(r=e.currentTarget)||void 0===r||r.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let r=""!==s.searchRef.current;!n&&(!r||" "!==e.key)&&K.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ex.displayName=eh;var eM=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:u=!1,textValue:l,...c}=e,d=ed(eh,n),s=$(n),f=t.useRef(null),p=(0,a.e)(r,f),[v,m]=t.useState(!1),[g,h]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var r;h((null!==(r=e.textContent)&&void 0!==r?r:"").trim())}},[c.children]),(0,x.jsx)(z.ItemSlot,{scope:n,disabled:u,textValue:null!=l?l:g,children:(0,x.jsx)(S,{asChild:!0,...s,focusable:!u,children:(0,x.jsx)(i.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,ez(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ez(e=>d.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),ey=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,x.jsx)(eI,{scope:e.__scopeMenu,checked:n,children:(0,x.jsx)(ex,{role:"menuitemcheckbox","aria-checked":eB(n)?"mixed":n,...a,ref:r,"data-state":eU(n),onSelect:(0,o.M)(a.onSelect,()=>null==t?void 0:t(!!eB(n)||!n),{checkForDefaultPrevented:!1})})})});ey.displayName="MenuCheckboxItem";var eb="MenuRadioGroup",[eC,eR]=Z(eb,{value:void 0,onValueChange:()=>{}}),ej=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,w.W)(t);return(0,x.jsx)(eC,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,x.jsx)(em,{...o,ref:r})})});ej.displayName=eb;var eD="MenuRadioItem",e_=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=eR(eD,e.__scopeMenu),u=n===a.value;return(0,x.jsx)(eI,{scope:e.__scopeMenu,checked:u,children:(0,x.jsx)(ex,{role:"menuitemradio","aria-checked":u,...t,ref:r,"data-state":eU(u),onSelect:(0,o.M)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});e_.displayName=eD;var ek="MenuItemIndicator",[eI,eE]=Z(ek,{checked:!1}),eP=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eE(ek,n);return(0,x.jsx)(h.z,{present:t||eB(a.checked)||!0===a.checked,children:(0,x.jsx)(i.WV.span,{...o,ref:r,"data-state":eU(a.checked)})})});eP.displayName=ek;var eT=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,x.jsx)(i.WV.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});eT.displayName="MenuSeparator";var eS=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=q(n);return(0,x.jsx)(m.Eh,{...o,...t,ref:r})});eS.displayName="MenuArrow";var eF="MenuSub",[eN,eA]=Z(eF),eO=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,u=Q(eF,r),l=q(r),[i,c]=t.useState(null),[d,s]=t.useState(null),f=(0,w.W)(a);return t.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,x.jsx)(m.fC,{...l,children:(0,x.jsx)(J,{scope:r,open:o,onOpenChange:f,content:d,onContentChange:s,children:(0,x.jsx)(eN,{scope:r,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:i,onTriggerChange:c,children:n})})})};eO.displayName=eF;var eL="MenuSubTrigger",eK=t.forwardRef((e,r)=>{let n=Q(eL,e.__scopeMenu),u=er(eL,e.__scopeMenu),l=eA(eL,e.__scopeMenu),i=ed(eL,e.__scopeMenu),c=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:s}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),s(null)}},[d,s]),(0,x.jsx)(et,{asChild:!0,...f,children:(0,x.jsx)(eM,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eG(n.open),...e,ref:(0,a.F)(r,l.onTriggerChange),onClick:r=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ez(r=>{i.onItemEnter(r),r.defaultPrevented||e.disabled||n.open||c.current||(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ez(e=>{var r,t;p();let o=null===(r=n.content)||void 0===r?void 0:r.getBoundingClientRect();if(o){let r=null===(t=n.content)||void 0===t?void 0:t.dataset.side,a="right"===r,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&G[u.dir].includes(r.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),r.preventDefault()}})})})});eK.displayName=eL;var eV="MenuSubContent",eW=t.forwardRef((e,r)=>{let n=eu(ei,e.__scopeMenu),{forceMount:u=n.forceMount,...l}=e,i=Q(ei,e.__scopeMenu),c=er(ei,e.__scopeMenu),d=eA(eV,e.__scopeMenu),s=t.useRef(null),f=(0,a.e)(r,s);return(0,x.jsx)(z.Provider,{scope:e.__scopeMenu,children:(0,x.jsx)(h.z,{present:u||i.open,children:(0,x.jsx)(z.Slot,{scope:e.__scopeMenu,children:(0,x.jsx)(ev,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;c.isUsingKeyboardRef.current&&(null===(r=s.current)||void 0===r||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=B[c.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null===(t=d.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eG(e){return e?"open":"closed"}function eB(e){return"indeterminate"===e}function eU(e){return eB(e)?"indeterminate":e?"checked":"unchecked"}function ez(e){return r=>"mouse"===r.pointerType?e(r):void 0}eW.displayName=eV;var eX="DropdownMenu",[eY,eZ]=(0,u.b)(eX,[H]),eH=H(),[eq,e$]=eY(eX),eJ=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:c=!0}=e,d=eH(r),s=t.useRef(null),[f=!1,p]=(0,l.T)({prop:a,defaultProp:u,onChange:i});return(0,x.jsx)(eq,{scope:r,triggerId:(0,v.M)(),triggerRef:s,contentId:(0,v.M)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,x.jsx)(en,{...d,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eJ.displayName=eX;var eQ="DropdownMenuTrigger",e0=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...u}=e,l=e$(eQ,n),c=eH(n);return(0,x.jsx)(et,{asChild:!0,...c,children:(0,x.jsx)(i.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.F)(r,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});e0.displayName=eQ;var e1=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eH(r);return(0,x.jsx)(el,{...t,...n})};e1.displayName="DropdownMenuPortal";var e5="DropdownMenuContent",e7=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,u=e$(e5,n),l=eH(n),i=t.useRef(!1);return(0,x.jsx)(es,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:r,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var r;i.current||null===(r=u.triggerRef.current)||void 0===r||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e7.displayName=e5;var e3=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(em,{...o,...t,ref:r})});e3.displayName="DropdownMenuGroup";var e2=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eg,{...o,...t,ref:r})});e2.displayName="DropdownMenuLabel";var e8=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(ex,{...o,...t,ref:r})});e8.displayName="DropdownMenuItem";var e9=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(ey,{...o,...t,ref:r})});e9.displayName="DropdownMenuCheckboxItem";var e4=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(ej,{...o,...t,ref:r})});e4.displayName="DropdownMenuRadioGroup";var e6=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(e_,{...o,...t,ref:r})});e6.displayName="DropdownMenuRadioItem";var re=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eP,{...o,...t,ref:r})});re.displayName="DropdownMenuItemIndicator";var rr=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eT,{...o,...t,ref:r})});rr.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eS,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var rn=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eK,{...o,...t,ref:r})});rn.displayName="DropdownMenuSubTrigger";var rt=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eH(n);return(0,x.jsx)(eW,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});rt.displayName="DropdownMenuSubContent";var ro=eJ,ra=e0,ru=e1,rl=e7,ri=e3,rc=e2,rd=e8,rs=e9,rf=e4,rp=e6,rv=re,rm=rr,rg=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,u=eH(r),[i=!1,c]=(0,l.T)({prop:t,defaultProp:a,onChange:o});return(0,x.jsx)(eO,{...u,open:i,onOpenChange:c,children:n})},rh=rn,rw=rt}}]);