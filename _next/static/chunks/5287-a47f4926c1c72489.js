"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5287],{25510:function(t,e,n){n.d(e,{ee:function(){return t$},Eh:function(){return tQ},VY:function(){return tq},fC:function(){return tU},D7:function(){return tP}});var r=n(2265);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,s=Math.round,a=Math.floor,u=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(t,e){return"function"==typeof t?t(e):t}function h(t){return t.split("-")[0]}function p(t){return t.split("-")[1]}function m(t){return"x"===t?"y":"x"}function g(t){return"y"===t?"height":"width"}function y(t){return["top","bottom"].includes(h(t))?"y":"x"}function v(t){return t.replace(/start|end/g,t=>f[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function x(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function b(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function R(t,e,n){let r,{reference:i,floating:o}=t,l=y(e),s=m(y(e)),a=g(s),u=h(e),c="y"===l,f=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,v=i[a]/2-o[a]/2;switch(u){case"top":r={x:f,y:i.y-o.height};break;case"bottom":r={x:f,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(p(e)){case"start":r[s]-=v*(n&&c?-1:1);break;case"end":r[s]+=v*(n&&c?-1:1)}return r}let A=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,s=o.filter(Boolean),a=await (null==l.isRTL?void 0:l.isRTL(e)),u=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:f}=R(u,r,a),d=r,h={},p=0;for(let n=0;n<s.length;n++){let{name:o,fn:m}=s[n],{x:g,y:y,data:v,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:i,middlewareData:h,rects:u,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,f=null!=y?y:f,h={...h,[o]:{...h[o],...v}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):w.rects),{x:c,y:f}=R(u,d,a)),n=-1)}return{x:c,y:f,placement:d,strategy:i,middlewareData:h}};async function O(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:s,strategy:a}=t,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:h=!1,padding:p=0}=d(e,t),m=x(p),g=s[h?"floating"===f?"reference":"floating":f],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:a})),v="floating"===f?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),R=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:w,strategy:a}):v);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function S(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function E(t){return i.some(e=>t[e]>=0)}async function C(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=h(n),s=p(n),a="y"===y(n),u=["left","top"].includes(l)?-1:1,c=o&&a?-1:1,f=d(e,t),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return s&&"number"==typeof v&&(g="end"===s?-1*v:v),a?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function L(){return"undefined"!=typeof window}function T(t){return k(t)?(t.nodeName||"").toLowerCase():"#document"}function M(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function P(t){var e;return null==(e=(k(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function k(t){return!!L()&&(t instanceof Node||t instanceof M(t).Node)}function H(t){return!!L()&&(t instanceof Element||t instanceof M(t).Element)}function D(t){return!!L()&&(t instanceof HTMLElement||t instanceof M(t).HTMLElement)}function W(t){return!!L()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof M(t).ShadowRoot)}function V(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=B(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function F(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function j(t){let e=z(),n=H(t)?B(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function N(t){return["html","body","#document"].includes(T(t))}function B(t){return M(t).getComputedStyle(t)}function Y(t){return H(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _(t){if("html"===T(t))return t;let e=t.assignedSlot||t.parentNode||W(t)&&t.host||P(t);return W(e)?e.host:e}function I(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=_(e);return N(n)?e.ownerDocument?e.ownerDocument.body:e.body:D(n)&&V(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=M(i);if(o){let t=K(l);return e.concat(l,l.visualViewport||[],V(i)?i:[],t&&n?I(t):[])}return e.concat(i,I(i,[],n))}function K(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function X(t){let e=B(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=D(t),o=i?t.offsetWidth:n,l=i?t.offsetHeight:r,a=s(n)!==o||s(r)!==l;return a&&(n=o,r=l),{width:n,height:r,$:a}}function U(t){return H(t)?t:t.contextElement}function $(t){let e=U(t);if(!D(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=X(e),l=(o?s(n.width):n.width)/r,a=(o?s(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let q=u(0);function Q(t){let e=M(t);return z()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:q}function Z(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=U(t),s=u(1);e&&(r?H(r)&&(s=$(r)):s=$(t));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===M(l))&&i)?Q(l):u(0),c=(o.left+a.x)/s.x,f=(o.top+a.y)/s.y,d=o.width/s.x,h=o.height/s.y;if(l){let t=M(l),e=r&&H(r)?M(r):r,n=t,i=K(n);for(;i&&r&&e!==n;){let t=$(i),e=i.getBoundingClientRect(),r=B(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,f*=t.y,d*=t.x,h*=t.y,c+=o,f+=l,i=K(n=M(i))}}return b({width:d,height:h,x:c,y:f})}function G(t,e){let n=Y(t).scrollLeft;return e?e.left+n:Z(P(t)).left+n}function J(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=M(t),r=P(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,s=0,a=0;if(i){o=i.width,l=i.height;let t=z();(!t||t&&"fixed"===e)&&(s=i.offsetLeft,a=i.offsetTop)}return{width:o,height:l,x:s,y:a}}(t,n);else if("document"===e)r=function(t){let e=P(t),n=Y(t),r=t.ownerDocument.body,i=l(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),o=l(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+G(t),a=-n.scrollTop;return"rtl"===B(r).direction&&(s+=l(e.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:a}}(P(t));else if(H(e))r=function(t,e){let n=Z(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=D(t)?$(t):u(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{let n=Q(t);r={...e,x:e.x-n.x,y:e.y-n.y}}return b(r)}function tt(t){return"static"===B(t).position}function te(t,e){if(!D(t)||"fixed"===B(t).position)return null;if(e)return e(t);let n=t.offsetParent;return P(t)===n&&(n=n.ownerDocument.body),n}function tn(t,e){let n=M(t);if(F(t))return n;if(!D(t)){let e=_(t);for(;e&&!N(e);){if(H(e)&&!tt(e))return e;e=_(e)}return n}let r=te(t,e);for(;r&&["table","td","th"].includes(T(r))&&tt(r);)r=te(r,e);return r&&N(r)&&tt(r)&&!j(r)?n:r||function(t){let e=_(t);for(;D(e)&&!N(e);){if(j(e))return e;if(F(e))break;e=_(e)}return null}(t)||n}let tr=async function(t){let e=this.getOffsetParent||tn,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=D(e),i=P(e),o="fixed"===n,l=Z(t,!0,o,e),s={scrollLeft:0,scrollTop:0},a=u(0);if(r||!r&&!o){if(("body"!==T(e)||V(i))&&(s=Y(e)),r){let t=Z(e,!0,o,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&(a.x=G(i))}let c=0,f=0;if(i&&!r&&!o){let t=i.getBoundingClientRect();f=t.top+s.scrollTop,c=t.left+s.scrollLeft-G(i,t)}return{x:l.left+s.scrollLeft-a.x-c,y:l.top+s.scrollTop-a.y-f,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ti={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=P(r),s=!!e&&F(e.floating);if(r===l||s&&o)return n;let a={scrollLeft:0,scrollTop:0},c=u(1),f=u(0),d=D(r);if((d||!d&&!o)&&(("body"!==T(r)||V(l))&&(a=Y(r)),D(r))){let t=Z(r);c=$(r),f.x=t.x+r.clientLeft,f.y=t.y+r.clientTop}return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+f.x,y:n.y*c.y-a.scrollTop*c.y+f.y}},getDocumentElement:P,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:i}=t,s=[..."clippingAncestors"===n?F(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=I(t,[],!1).filter(t=>H(t)&&"body"!==T(t)),i=null,o="fixed"===B(t).position,l=o?_(t):t;for(;H(l)&&!N(l);){let e=B(l),n=j(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||V(l)&&!n&&function t(e,n){let r=_(e);return!(r===n||!H(r)||N(r))&&("fixed"===B(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=_(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],a=s[0],u=s.reduce((t,n)=>{let r=J(e,n,i);return t.top=l(r.top,t.top),t.right=o(r.right,t.right),t.bottom=o(r.bottom,t.bottom),t.left=l(r.left,t.left),t},J(e,a,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:tn,getElementRects:tr,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=X(t);return{width:e,height:n}},getScale:$,isElement:H,isRTL:function(t){return"rtl"===B(t).direction}},to=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:i,rects:s,platform:a,elements:u,middlewareData:c}=e,{element:f,padding:h=0}=d(t,e)||{};if(null==f)return{};let v=x(h),w={x:n,y:r},b=m(y(i)),R=g(b),A=await a.getDimensions(f),O="y"===b,S=O?"clientHeight":"clientWidth",E=s.reference[R]+s.reference[b]-w[b]-s.floating[R],C=w[b]-s.reference[b],L=await (null==a.getOffsetParent?void 0:a.getOffsetParent(f)),T=L?L[S]:0;T&&await (null==a.isElement?void 0:a.isElement(L))||(T=u.floating[S]||s.floating[R]);let M=T/2-A[R]/2-1,P=o(v[O?"top":"left"],M),k=o(v[O?"bottom":"right"],M),H=T-A[R]-k,D=T/2-A[R]/2+(E/2-C/2),W=l(P,o(D,H)),V=!c.arrow&&null!=p(i)&&D!==W&&s.reference[R]/2-(D<P?P:k)-A[R]/2<0,F=V?D<P?D-P:D-H:0;return{[b]:w[b]+F,data:{[b]:W,centerOffset:D-W-F,...V&&{alignmentOffset:F}},reset:V}}}),tl=(t,e,n)=>{let r=new Map,i={platform:ti,...n},o={...i.platform,_c:r};return A(t,e,{...i,platform:o})};var ts=n(54887),ta="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function tu(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!tu(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!tu(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function tc(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function tf(t,e){let n=tc(t);return Math.round(e*n)/n}function td(t){let e=r.useRef(t);return ta(()=>{e.current=t}),e}let th=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:r}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?to({element:n.current,padding:r}).fn(e):{}:n?to({element:n,padding:r}).fn(e):{}}}),tp=(t,e)=>{var n;return{...(void 0===(n=t)&&(n=0),{name:"offset",options:n,async fn(t){var e,r;let{x:i,y:o,placement:l,middlewareData:s}=t,a=await C(t,n);return l===(null==(e=s.offset)?void 0:e.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:i+a.x,y:o+a.y,data:{...a,placement:l}}}}),options:[t,e]}},tm=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"shift",options:n,async fn(t){let{x:e,y:r,placement:i}=t,{mainAxis:s=!0,crossAxis:a=!1,limiter:u={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=d(n,t),f={x:e,y:r},p=await O(t,c),g=y(h(i)),v=m(g),w=f[v],x=f[g];if(s){let t="y"===v?"top":"left",e="y"===v?"bottom":"right",n=w+p[t],r=w-p[e];w=l(n,o(w,r))}if(a){let t="y"===g?"top":"left",e="y"===g?"bottom":"right",n=x+p[t],r=x-p[e];x=l(n,o(x,r))}let b=u.fn({...t,[v]:w,[g]:x});return{...b,data:{x:b.x-e,y:b.y-r}}}}),options:[t,e]}},tg=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{options:n,fn(t){let{x:e,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:s=0,mainAxis:a=!0,crossAxis:u=!0}=d(n,t),c={x:e,y:r},f=y(i),p=m(f),g=c[p],v=c[f],w=d(s,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(a){let t="y"===p?"height":"width",e=o.reference[p]-o.floating[t]+x.mainAxis,n=o.reference[p]+o.reference[t]-x.mainAxis;g<e?g=e:g>n&&(g=n)}if(u){var b,R;let t="y"===p?"width":"height",e=["top","left"].includes(h(i)),n=o.reference[f]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[f])||0)+(e?0:x.crossAxis),r=o.reference[f]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[f])||0)-(e?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[p]:g,[f]:v}}}),options:[t,e]}},ty=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"flip",options:n,async fn(t){var e,r,i,o,l;let{placement:s,middlewareData:a,rects:u,initialPlacement:c,platform:f,elements:x}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:C=!0,...L}=d(n,t);if(null!=(e=a.arrow)&&e.alignmentOffset)return{};let T=h(s),M=y(c),P=h(c)===c,k=await (null==f.isRTL?void 0:f.isRTL(x.floating)),H=A||(P||!C?[w(c)]:function(t){let e=w(t);return[v(t),e,v(e)]}(c)),D="none"!==E;!A&&D&&H.push(...function(t,e,n,r){let i=p(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(h(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(v)))),o}(c,C,E,k));let W=[c,...H],V=await O(t,L),F=[],j=(null==(r=a.flip)?void 0:r.overflows)||[];if(b&&F.push(V[T]),R){let t=function(t,e,n){void 0===n&&(n=!1);let r=p(t),i=m(y(t)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(s,u,k);F.push(V[t[0]],V[t[1]])}if(j=[...j,{placement:s,overflows:F}],!F.every(t=>t<=0)){let t=((null==(i=a.flip)?void 0:i.index)||0)+1,e=W[t];if(e)return{data:{index:t,overflows:j},reset:{placement:e}};let n=null==(o=j.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let t=null==(l=j.filter(t=>{if(D){let e=y(t.placement);return e===M||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(s!==n)return{reset:{placement:n}}}return{}}}),options:[t,e]}},tv=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"size",options:n,async fn(t){let e,r;let{placement:i,rects:s,platform:a,elements:u}=t,{apply:c=()=>{},...f}=d(n,t),m=await O(t,f),g=h(i),v=p(i),w="y"===y(i),{width:x,height:b}=s.floating;"top"===g||"bottom"===g?(e=g,r=v===(await (null==a.isRTL?void 0:a.isRTL(u.floating))?"start":"end")?"left":"right"):(r=g,e="end"===v?"top":"bottom");let R=b-m.top-m.bottom,A=x-m.left-m.right,S=o(b-m[e],R),E=o(x-m[r],A),C=!t.middlewareData.shift,L=S,T=E;if(w?T=v||C?o(E,A):A:L=v||C?o(S,R):R,C&&!v){let t=l(m.left,0),e=l(m.right,0),n=l(m.top,0),r=l(m.bottom,0);w?T=x-2*(0!==t||0!==e?t+e:l(m.left,m.right)):L=b-2*(0!==n||0!==r?n+r:l(m.top,m.bottom))}await c({...t,availableWidth:T,availableHeight:L});let M=await a.getDimensions(u.floating);return x!==M.width||b!==M.height?{reset:{rects:!0}}:{}}}),options:[t,e]}},tw=(t,e)=>{var n;return{...(void 0===(n=t)&&(n={}),{name:"hide",options:n,async fn(t){let{rects:e}=t,{strategy:r="referenceHidden",...i}=d(n,t);switch(r){case"referenceHidden":{let n=S(await O(t,{...i,elementContext:"reference"}),e.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:E(n)}}}case"escaped":{let n=S(await O(t,{...i,altBoundary:!0}),e.floating);return{data:{escapedOffsets:n,escaped:E(n)}}}default:return{}}}}),options:[t,e]}},tx=(t,e)=>({...th(t),options:[t,e]});var tb=n(25171),tR=n(57437),tA=r.forwardRef((t,e)=>{let{children:n,width:r=10,height:i=5,...o}=t;return(0,tR.jsx)(tb.WV.svg,{...o,ref:e,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?n:(0,tR.jsx)("polygon",{points:"0,0 30,0 15,10"})})});tA.displayName="Arrow";var tO=n(1584),tS=n(98324),tE=n(75137),tC=n(1336),tL=n(75238),tT="Popper",[tM,tP]=(0,tS.b)(tT),[tk,tH]=tM(tT),tD=t=>{let{__scopePopper:e,children:n}=t,[i,o]=r.useState(null);return(0,tR.jsx)(tk,{scope:e,anchor:i,onAnchorChange:o,children:n})};tD.displayName=tT;var tW="PopperAnchor",tV=r.forwardRef((t,e)=>{let{__scopePopper:n,virtualRef:i,...o}=t,l=tH(tW,n),s=r.useRef(null),a=(0,tO.e)(e,s);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||s.current)}),i?null:(0,tR.jsx)(tb.WV.div,{...o,ref:a})});tV.displayName=tW;var tF="PopperContent",[tj,tz]=tM(tF),tN=r.forwardRef((t,e)=>{var n,i,s,u,c,f,d,h;let{__scopePopper:p,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:v=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:R=0,sticky:A="partial",hideWhenDetached:O=!1,updatePositionStrategy:S="optimized",onPlaced:E,...C}=t,L=tH(tF,p),[T,M]=r.useState(null),k=(0,tO.e)(e,t=>M(t)),[H,D]=r.useState(null),W=(0,tL.t)(H),V=null!==(d=null==W?void 0:W.width)&&void 0!==d?d:0,F=null!==(h=null==W?void 0:W.height)&&void 0!==h?h:0,j="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},z=Array.isArray(b)?b:[b],N=z.length>0,B={padding:j,boundary:z.filter(tI),altBoundary:N},{refs:Y,floatingStyles:_,placement:K,isPositioned:X,middlewareData:$}=function(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:s}={},transform:a=!0,whileElementsMounted:u,open:c}=t,[f,d]=r.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[h,p]=r.useState(i);tu(h,i)||p(i);let[m,g]=r.useState(null),[y,v]=r.useState(null),w=r.useCallback(t=>{t!==A.current&&(A.current=t,g(t))},[]),x=r.useCallback(t=>{t!==O.current&&(O.current=t,v(t))},[]),b=l||m,R=s||y,A=r.useRef(null),O=r.useRef(null),S=r.useRef(f),E=null!=u,C=td(u),L=td(o),T=r.useCallback(()=>{if(!A.current||!O.current)return;let t={placement:e,strategy:n,middleware:h};L.current&&(t.platform=L.current),tl(A.current,O.current,t).then(t=>{let e={...t,isPositioned:!0};M.current&&!tu(S.current,e)&&(S.current=e,ts.flushSync(()=>{d(e)}))})},[h,e,n,L]);ta(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(t=>({...t,isPositioned:!1})))},[c]);let M=r.useRef(!1);ta(()=>(M.current=!0,()=>{M.current=!1}),[]),ta(()=>{if(b&&(A.current=b),R&&(O.current=R),b&&R){if(C.current)return C.current(b,R,T);T()}},[b,R,T,C,E]);let P=r.useMemo(()=>({reference:A,floating:O,setReference:w,setFloating:x}),[w,x]),k=r.useMemo(()=>({reference:b,floating:R}),[b,R]),H=r.useMemo(()=>{let t={position:n,left:0,top:0};if(!k.floating)return t;let e=tf(k.floating,f.x),r=tf(k.floating,f.y);return a?{...t,transform:"translate("+e+"px, "+r+"px)",...tc(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,a,k.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:T,refs:P,elements:k,floatingStyles:H}),[f,T,P,k,H])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t,e,n,r){let i;void 0===r&&(r={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=U(t),p=s||u?[...h?I(h):[],...I(e)]:[];p.forEach(t=>{s&&t.addEventListener("scroll",n,{passive:!0}),u&&t.addEventListener("resize",n)});let m=h&&f?function(t,e){let n,r=null,i=P(t);function s(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function u(c,f){void 0===c&&(c=!1),void 0===f&&(f=1),s();let{left:d,top:h,width:p,height:m}=t.getBoundingClientRect();if(c||e(),!p||!m)return;let g=a(h),y=a(i.clientWidth-(d+p)),v={rootMargin:-g+"px "+-y+"px "+-a(i.clientHeight-(h+m))+"px "+-a(d)+"px",threshold:l(0,o(1,f))||1},w=!0;function x(t){let e=t[0].intersectionRatio;if(e!==f){if(!w)return u();e?u(!1,e):n=setTimeout(()=>{u(!1,1e-7)},1e3)}w=!1}try{r=new IntersectionObserver(x,{...v,root:i.ownerDocument})}catch(t){r=new IntersectionObserver(x,v)}r.observe(t)}(!0),s}(h,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),h&&!d&&y.observe(h),y.observe(e));let v=d?Z(t):null;return d&&function e(){let r=Z(t);v&&(r.x!==v.x||r.y!==v.y||r.width!==v.width||r.height!==v.height)&&n(),v=r,i=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{s&&t.removeEventListener("scroll",n),u&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===S})},elements:{reference:L.anchor},middleware:[tp({mainAxis:g+F,alignmentAxis:v}),x&&tm({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?tg():void 0,...B}),x&&ty({...B}),tv({...B,apply:t=>{let{elements:e,rects:n,availableWidth:r,availableHeight:i}=t,{width:o,height:l}=n.reference,s=e.floating.style;s.setProperty("--radix-popper-available-width","".concat(r,"px")),s.setProperty("--radix-popper-available-height","".concat(i,"px")),s.setProperty("--radix-popper-anchor-width","".concat(o,"px")),s.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),H&&tx({element:H,padding:w}),tK({arrowWidth:V,arrowHeight:F}),O&&tw({strategy:"referenceHidden",...B})]}),[q,Q]=tX(K),G=(0,tE.W)(E);(0,tC.b)(()=>{X&&(null==G||G())},[X,G]);let J=null===(n=$.arrow)||void 0===n?void 0:n.x,tt=null===(i=$.arrow)||void 0===i?void 0:i.y,te=(null===(s=$.arrow)||void 0===s?void 0:s.centerOffset)!==0,[tn,tr]=r.useState();return(0,tC.b)(()=>{T&&tr(window.getComputedStyle(T).zIndex)},[T]),(0,tR.jsx)("div",{ref:Y.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:X?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:tn,"--radix-popper-transform-origin":[null===(u=$.transformOrigin)||void 0===u?void 0:u.x,null===(c=$.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(f=$.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:(0,tR.jsx)(tj,{scope:p,placedSide:q,onArrowChange:D,arrowX:J,arrowY:tt,shouldHideArrow:te,children:(0,tR.jsx)(tb.WV.div,{"data-side":q,"data-align":Q,...C,ref:k,style:{...C.style,animation:X?void 0:"none"}})})})});tN.displayName=tF;var tB="PopperArrow",tY={top:"bottom",right:"left",bottom:"top",left:"right"},t_=r.forwardRef(function(t,e){let{__scopePopper:n,...r}=t,i=tz(tB,n),o=tY[i.placedSide];return(0,tR.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,tR.jsx)(tA,{...r,ref:e,style:{...r.style,display:"block"}})})});function tI(t){return null!==t}t_.displayName=tB;var tK=t=>({name:"transformOrigin",options:t,fn(e){var n,r,i,o,l;let{placement:s,rects:a,middlewareData:u}=e,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,f=c?0:t.arrowWidth,d=c?0:t.arrowHeight,[h,p]=tX(s),m={start:"0%",center:"50%",end:"100%"}[p],g=(null!==(o=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+f/2,y=(null!==(l=null===(i=u.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,v="",w="";return"bottom"===h?(v=c?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===h?(v=c?m:"".concat(g,"px"),w="".concat(a.floating.height+d,"px")):"right"===h?(v="".concat(-d,"px"),w=c?m:"".concat(y,"px")):"left"===h&&(v="".concat(a.floating.width+d,"px"),w=c?m:"".concat(y,"px")),{data:{x:v,y:w}}}});function tX(t){let[e,n="center"]=t.split("-");return[e,n]}var tU=tD,t$=tV,tq=tN,tQ=t_},75238:function(t,e,n){n.d(e,{t:function(){return o}});var r=n(2265),i=n(1336);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}},25524:function(t,e,n){n.d(e,{useMutation:function(){return f}});var r=n(2265),i=n(92812),o=n(69948),l=n(49010),s=n(56298),a=class extends l.l{#t;#e=void 0;#n;#r;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,s.VS)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#n,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,s.Ym)(e.mutationKey)!==(0,s.Ym)(this.options.mutationKey)?this.reset():this.#n?.state.status==="pending"&&this.#n.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#n?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#o(t)}getCurrentResult(){return this.#e}reset(){this.#n?.removeObserver(this),this.#n=void 0,this.#i(),this.#o()}mutate(t,e){return this.#r=e,this.#n?.removeObserver(this),this.#n=this.#t.getMutationCache().build(this.#t,this.options),this.#n.addObserver(this),this.#n.execute(t)}#i(){let t=this.#n?.state??(0,i.R)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#o(t){o.V.batch(()=>{if(this.#r&&this.hasListeners()){let e=this.#e.variables,n=this.#e.context;t?.type==="success"?(this.#r.onSuccess?.(t.data,e,n),this.#r.onSettled?.(t.data,null,e,n)):t?.type==="error"&&(this.#r.onError?.(t.error,e,n),this.#r.onSettled?.(void 0,t.error,e,n))}this.listeners.forEach(t=>{t(this.#e)})})}},u=n(93191),c=n(37832);function f(t,e){let n=(0,u.useQueryClient)(e),[i]=r.useState(()=>new a(n,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let l=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(o.V.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),s=r.useCallback((t,e)=>{i.mutate(t,e).catch(c.Z)},[i]);if(l.error&&(0,c.L)(i.options.throwOnError,[l.error]))throw l.error;return{...l,mutate:s,mutateAsync:l.mutate}}}}]);