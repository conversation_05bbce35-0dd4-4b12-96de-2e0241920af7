"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3850],{83667:function(t,e,r){r.d(e,{z:function(){return c}});var s=r(56298),i=r(69948),n=r(34939),u=r(49010),o=r(2459),c=class extends u.l{constructor(t,e){super(),this.options=e,this.#t=t,this.#e=null,this.bindMethods(),this.setOptions(e)}#t;#r=void 0;#s=void 0;#i=void 0;#n;#u;#e;#o;#c;#a;#h;#l;#d;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#r.addObserver(this),a(this.#r,this.options)?this.#p():this.updateResult(),this.#y())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return h(this.#r,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return h(this.#r,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#R(),this.#v(),this.#r.removeObserver(this)}setOptions(t,e){let r=this.options,i=this.#r;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,s.Nc)(this.options.enabled,this.#r))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#Q(),this.#r.setOptions(this.options),r._defaulted&&!(0,s.VS)(this.options,r)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#r,observer:this});let n=this.hasListeners();n&&l(this.#r,i,this.options,r)&&this.#p(),this.updateResult(e),n&&(this.#r!==i||(0,s.Nc)(this.options.enabled,this.#r)!==(0,s.Nc)(r.enabled,this.#r)||(0,s.KC)(this.options.staleTime,this.#r)!==(0,s.KC)(r.staleTime,this.#r))&&this.#b();let u=this.#C();n&&(this.#r!==i||(0,s.Nc)(this.options.enabled,this.#r)!==(0,s.Nc)(r.enabled,this.#r)||u!==this.#d)&&this.#m(u)}getOptimisticResult(t){let e=this.#t.getQueryCache().build(this.#t,t),r=this.createResult(e,t);return(0,s.VS)(this.getCurrentResult(),r)||(this.#i=r,this.#u=this.options,this.#n=this.#r.state),r}getCurrentResult(){return this.#i}trackResult(t,e){let r={};return Object.keys(t).forEach(s=>{Object.defineProperty(r,s,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(s),e?.(s),t[s])})}),r}trackProp(t){this.#f.add(t)}getCurrentQuery(){return this.#r}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),r=this.#t.getQueryCache().build(this.#t,e);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,e))}fetch(t){return this.#p({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#i))}#p(t){this.#Q();let e=this.#r.fetch(this.options,t);return t?.throwOnError||(e=e.catch(s.ZT)),e}#b(){this.#R();let t=(0,s.KC)(this.options.staleTime,this.#r);if(s.sk||this.#i.isStale||!(0,s.PN)(t))return;let e=(0,s.Kp)(this.#i.dataUpdatedAt,t);this.#h=setTimeout(()=>{this.#i.isStale||this.updateResult()},e+1)}#C(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#r):this.options.refetchInterval)??!1}#m(t){this.#v(),this.#d=t,!s.sk&&!1!==(0,s.Nc)(this.options.enabled,this.#r)&&(0,s.PN)(this.#d)&&0!==this.#d&&(this.#l=setInterval(()=>{(this.options.refetchIntervalInBackground||n.j.isFocused())&&this.#p()},this.#d))}#y(){this.#b(),this.#m(this.#C())}#R(){this.#h&&(clearTimeout(this.#h),this.#h=void 0)}#v(){this.#l&&(clearInterval(this.#l),this.#l=void 0)}createResult(t,e){let r;let i=this.#r,n=this.options,u=this.#i,c=this.#n,h=this.#u,f=t!==i?t.state:this.#s,{state:p}=t,y={...p},R=!1;if(e._optimisticResults){let r=this.hasListeners(),s=!r&&a(t,e),u=r&&l(t,i,e,n);(s||u)&&(y={...y,...(0,o.z)(p.data,t.options)}),"isRestoring"===e._optimisticResults&&(y.fetchStatus="idle")}let{error:v,errorUpdatedAt:Q,status:b}=y;if(e.select&&void 0!==y.data){if(u&&y.data===c?.data&&e.select===this.#o)r=this.#c;else try{this.#o=e.select,r=e.select(y.data),r=(0,s.oE)(u?.data,r,e),this.#c=r,this.#e=null}catch(t){this.#e=t}}else r=y.data;if(void 0!==e.placeholderData&&void 0===r&&"pending"===b){let t;if(u?.isPlaceholderData&&e.placeholderData===h?.placeholderData)t=u.data;else if(t="function"==typeof e.placeholderData?e.placeholderData(this.#a?.state.data,this.#a):e.placeholderData,e.select&&void 0!==t)try{t=e.select(t),this.#e=null}catch(t){this.#e=t}void 0!==t&&(b="success",r=(0,s.oE)(u?.data,t,e),R=!0)}this.#e&&(v=this.#e,r=this.#c,Q=Date.now(),b="error");let C="fetching"===y.fetchStatus,m="pending"===b,O="error"===b,g=m&&C,I=void 0!==r;return{status:b,fetchStatus:y.fetchStatus,isPending:m,isSuccess:"success"===b,isError:O,isInitialLoading:g,isLoading:g,data:r,dataUpdatedAt:y.dataUpdatedAt,error:v,errorUpdatedAt:Q,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>f.dataUpdateCount||y.errorUpdateCount>f.errorUpdateCount,isFetching:C,isRefetching:C&&!m,isLoadingError:O&&!I,isPaused:"paused"===y.fetchStatus,isPlaceholderData:R,isRefetchError:O&&I,isStale:d(t,e),refetch:this.refetch}}updateResult(t){let e=this.#i,r=this.createResult(this.#r,this.options);if(this.#n=this.#r.state,this.#u=this.options,void 0!==this.#n.data&&(this.#a=this.#r),(0,s.VS)(r,e))return;this.#i=r;let i={};t?.listeners!==!1&&(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#f.size)return!0;let s=new Set(r??this.#f);return this.options.throwOnError&&s.add("error"),Object.keys(this.#i).some(t=>this.#i[t]!==e[t]&&s.has(t))})()&&(i.listeners=!0),this.#O({...i,...t})}#Q(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#r)return;let e=this.#r;this.#r=t,this.#s=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#y()}#O(t){i.V.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#i)}),this.#t.getQueryCache().notify({query:this.#r,type:"observerResultsUpdated"})})}};function a(t,e){return!1!==(0,s.Nc)(e.enabled,t)&&void 0===t.state.data&&!("error"===t.state.status&&!1===e.retryOnMount)||void 0!==t.state.data&&h(t,e,e.refetchOnMount)}function h(t,e,r){if(!1!==(0,s.Nc)(e.enabled,t)){let s="function"==typeof r?r(t):r;return"always"===s||!1!==s&&d(t,e)}return!1}function l(t,e,r,i){return(t!==e||!1===(0,s.Nc)(i.enabled,t))&&(!r.suspense||"error"!==t.state.status)&&d(t,r)}function d(t,e){return!1!==(0,s.Nc)(e.enabled,t)&&t.isStaleByTime((0,s.KC)(e.staleTime,t))}},93191:function(t,e,r){r.r(e),r.d(e,{QueryClientContext:function(){return n},QueryClientProvider:function(){return o},useQueryClient:function(){return u}});var s=r(2265),i=r(57437),n=s.createContext(void 0),u=t=>{let e=s.useContext(n);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},o=t=>{let{client:e,children:r}=t;return s.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(n.Provider,{value:e,children:r})}},53e3:function(t,e,r){r.d(e,{QueryErrorResetBoundary:function(){return c},useQueryErrorResetBoundary:function(){return o}});var s=r(2265),i=r(57437);function n(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}var u=s.createContext(n()),o=()=>s.useContext(u),c=t=>{let{children:e}=t,[r]=s.useState(()=>n());return(0,i.jsx)(u.Provider,{value:r,children:"function"==typeof e?e(r):e})}},88472:function(t,e,r){r.d(e,{JN:function(){return u},KJ:function(){return o},pf:function(){return n}});var s=r(2265),i=r(37832),n=(t,e)=>{(t.suspense||t.throwOnError)&&!e.isReset()&&(t.retryOnMount=!1)},u=t=>{s.useEffect(()=>{t.clearReset()},[t])},o=t=>{let{result:e,errorResetBoundary:r,throwOnError:s,query:n}=t;return e.isError&&!r.isReset()&&!e.isFetching&&n&&(0,i.L)(s,[e.error,n])}},99038:function(t,e,r){r.d(e,{IsRestoringProvider:function(){return u},useIsRestoring:function(){return n}});var s=r(2265),i=s.createContext(!1),n=()=>s.useContext(i),u=i.Provider},38261:function(t,e,r){r.d(e,{Ct:function(){return s},Fb:function(){return i},SB:function(){return u},Z$:function(){return n},j8:function(){return o}});var s=(t,e)=>void 0===e.state.data,i=t=>{t.suspense&&"number"!=typeof t.staleTime&&(t.staleTime=1e3)},n=(t,e)=>t.isLoading&&t.isFetching&&!e,u=(t,e)=>t?.suspense&&e.isPending,o=(t,e,r)=>e.fetchOptimistic(t).catch(()=>{r.clearReset()})},91235:function(t,e,r){r.d(e,{r:function(){return h}});var s=r(2265),i=r(69948),n=r(53e3),u=r(93191),o=r(99038),c=r(88472),a=r(38261);function h(t,e,r){var h,l,d,f;let p=(0,u.useQueryClient)(r),y=(0,o.useIsRestoring)(),R=(0,n.useQueryErrorResetBoundary)(),v=p.defaultQueryOptions(t);null===(l=p.getDefaultOptions().queries)||void 0===l||null===(h=l._experimental_beforeQuery)||void 0===h||h.call(l,v),v._optimisticResults=y?"isRestoring":"optimistic",(0,a.Fb)(v),(0,c.pf)(v,R),(0,c.JN)(R);let[Q]=s.useState(()=>new e(p,v)),b=Q.getOptimisticResult(v);if(s.useSyncExternalStore(s.useCallback(t=>{let e=y?()=>void 0:Q.subscribe(i.V.batchCalls(t));return Q.updateResult(),e},[Q,y]),()=>Q.getCurrentResult(),()=>Q.getCurrentResult()),s.useEffect(()=>{Q.setOptions(v,{listeners:!1})},[v,Q]),(0,a.SB)(v,b))throw(0,a.j8)(v,Q,R);if((0,c.KJ)({result:b,errorResetBoundary:R,throwOnError:v.throwOnError,query:p.getQueryCache().get(v.queryHash)}))throw b.error;return null===(f=p.getDefaultOptions().queries)||void 0===f||null===(d=f._experimental_afterQuery)||void 0===d||d.call(f,v,b),v.notifyOnChangeProps?b:Q.trackResult(b)}},37832:function(t,e,r){function s(t,e){return"function"==typeof t?t(...e):!!t}function i(){}r.d(e,{L:function(){return s},Z:function(){return i}})}}]);